(()=>{var e={};e.id=3084,e.ids=[1489,3084,8108],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>s,createSupabaseServerClientOnRequest:()=>n});var a=r(34386),i=r(44999);async function n(){let e=await (0,i.UL)();return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,a){try{e.set({name:t,value:r,...a})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function s(e){return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59490:(e,t,r)=>{"use strict";let a,i,n,s,o;r.r(t),r.d(t,{patchFetch:()=>rP,routeModule:()=>rE,serverHooks:()=>rj,workAsyncStorage:()=>rS,workUnitAsyncStorage:()=>rI});var l={};r.r(l),r.d(l,{JsonPatchError:()=>eI,_areEquals:()=>eN,applyOperation:()=>ek,applyPatch:()=>ex,applyReducer:()=>eR,deepClone:()=>ej,getValueByPointer:()=>eA,validate:()=>eC,validator:()=>e$});var u={};r.r(u),r.d(u,{POST:()=>rO});var d=r(96559),c=r(48088),h=r(37719),p=r(32190),m=r(2507);class f{constructor(e){Object.defineProperty(this,"pageContent",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.pageContent=void 0!==e.pageContent?e.pageContent.toString():"",this.metadata=e.metadata??{},this.id=e.id}}var g=r(45697),b=r(63611),w=r(82116),y=r(71719);let _=(...e)=>fetch(...e),v=Symbol.for("ls:fetch_implementation"),O=()=>globalThis[v]??_,E=[400,401,403,404,405,406,407,408],S=[409];class I{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedResponseHook",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.queue=new y.default({concurrency:this.maxConcurrency}),this.onFailedResponseHook=e?.onFailedResponseHook}call(e,...t){let r=this.onFailedResponseHook;return this.queue.add(()=>b(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{async onFailedAttempt(e){if(e.message.startsWith("Cancel")||e.message.startsWith("TimeoutError")||e.message.startsWith("AbortError")||e?.code==="ECONNABORTED")throw e;let t=e?.response,a=t?.status;if(a){if(E.includes(+a))throw e;if(S.includes(+a))return;r&&await r(t)}},retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>O()(...e).then(e=>e.ok?e:Promise.reject(e)))}}function j(e){return"function"==typeof e?._getType}function P(e){let t={type:e._getType(),data:{content:e.content}};return e?.additional_kwargs&&Object.keys(e.additional_kwargs).length>0&&(t.data.additional_kwargs={...e.additional_kwargs}),t}var T=r(73726);function A(e,t){if(!T.A(e))throw Error(void 0!==t?`Invalid UUID for ${t}: ${e}`:`Invalid UUID: ${e}`);return e}let k={};function x(e){k[e]||(console.warn(e),k[e]=!0)}var R=r(28584);function $(e){if(!e||e.split("/").length>2||e.startsWith("/")||e.endsWith("/")||e.split(":").length>2)throw Error(`Invalid identifier format: ${e}`);let[t,r]=e.split(":"),a=r||"latest";if(t.includes("/")){let[r,i]=t.split("/",2);if(!r||!i)throw Error(`Invalid identifier format: ${e}`);return[r,i,a]}if(!t)throw Error(`Invalid identifier format: ${e}`);return["-",t,a]}class C extends Error{constructor(e){super(e),this.name="LangSmithConflictError"}}async function N(e,t,r){let a;if(e.ok){r&&(a=await e.text());return}a=await e.text();let i=`Failed to ${t}. Received status [${e.status}]: ${e.statusText}. Server response: ${a}`;if(409===e.status)throw new C(i);throw Error(i)}var M={result:"[Circular]"},L=[],U=[];function D(e,t,r,a){try{return JSON.stringify(e,t,r)}catch(s){if(!s.message?.includes("Converting circular structure to JSON"))return console.warn("[WARNING]: LangSmith received unserializable value."),"[Unserializable]";console.warn("[WARNING]: LangSmith received circular JSON. This will decrease tracer performance."),void 0===a&&(a={depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}),function e(t,r,a,i,n,s,o){if(s+=1,"object"==typeof t&&null!==t){for(l=0;l<i.length;l++)if(i[l]===t)return void H(M,t,r,n);if(void 0!==o.depthLimit&&s>o.depthLimit||void 0!==o.edgesLimit&&a+1>o.edgesLimit)return void H("[...]",t,r,n);if(i.push(t),Array.isArray(t))for(l=0;l<t.length;l++)e(t[l],l,l,i,t,s,o);else{var l,u=Object.keys(t);for(l=0;l<u.length;l++){var d=u[l];e(t[d],d,l,i,t,s,o)}}i.pop()}}(e,"",0,[],void 0,0,a);try{i=0===U.length?JSON.stringify(e,t,r):JSON.stringify(e,function(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(U.length>0)for(var a=0;a<U.length;a++){var i=U[a];if(i[1]===t&&i[0]===r){r=i[2],U.splice(a,1);break}}return e.call(this,t,r)}}(t),r)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==L.length;){var i,n=L.pop();4===n.length?Object.defineProperty(n[0],n[1],n[3]):n[0][n[1]]=n[2]}}return i}}function H(e,t,r,a){var i=Object.getOwnPropertyDescriptor(a,r);void 0!==i.get?i.configurable?(Object.defineProperty(a,r,{value:e}),L.push([a,r,t,i])):U.push([t,r,e]):(a[r]=e,L.push([a,r,t]))}function F(e){let t=er(),r=function(){let e=function(){try{if("undefined"!=typeof process&&process.env)return Object.entries(process.env).reduce((e,[t,r])=>(e[t]=String(r),e),{});return}catch(e){return}}()||{},t={},r=["LANGCHAIN_API_KEY","LANGCHAIN_ENDPOINT","LANGCHAIN_TRACING_V2","LANGCHAIN_PROJECT","LANGCHAIN_SESSION"];for(let[a,i]of Object.entries(e))!a.startsWith("LANGCHAIN_")||"string"!=typeof i||r.includes(a)||a.toLowerCase().includes("key")||a.toLowerCase().includes("secret")||a.toLowerCase().includes("token")||("LANGCHAIN_REVISION_ID"===a?t.revision_id=i:t[a]=i);return t}(),a=e.extra??{},i=a.metadata;return e.extra={...a,runtime:{...t,...a?.runtime},metadata:{...r,...r.revision_id||e.revision_id?{revision_id:e.revision_id??r.revision_id}:{},...i}},e}let B=()=>{let e=ei("TRACING_SAMPLING_RATE");if(void 0===e)return;let t=parseFloat(e);if(t<0||t>1)throw Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${t}`);return t},J=e=>{let t=e.replace("http://","").replace("https://","").split("/")[0].split(":")[0];return"localhost"===t||"127.0.0.1"===t||"::1"===t};async function z(e){let t=[];for await(let r of e)t.push(r);return t}function q(e){if(void 0!==e)return e.trim().replace(/^"(.*)"$/,"$1").replace(/^'(.*)'$/,"$1")}let G=async e=>{if(e?.status===429){let t=1e3*parseInt(e.headers.get("retry-after")??"30",10);if(t>0)return await new Promise(e=>setTimeout(e,t)),!0}return!1};class K{constructor(){Object.defineProperty(this,"items",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"sizeBytes",{enumerable:!0,configurable:!0,writable:!0,value:0})}peek(){return this.items[0]}push(e){let t,r=new Promise(e=>{t=e}),a=D(e.item).length;return this.items.push({action:e.action,payload:e.item,itemPromiseResolve:t,itemPromise:r,size:a}),this.sizeBytes+=a,r}pop(e){if(e<1)throw Error("Number of bytes to pop off may not be less than 1.");let t=[],r=0;for(;r+(this.peek()?.size??0)<e&&this.items.length>0;){let e=this.items.shift();e&&(t.push(e),r+=e.size,this.sizeBytes-=e.size)}if(0===t.length&&this.items.length>0){let e=this.items.shift();t.push(e),r+=e.size,this.sizeBytes-=e.size}return[t.map(e=>({action:e.action,item:e.payload})),()=>t.forEach(e=>e.itemPromiseResolve())]}}class W{constructor(e={}){Object.defineProperty(this,"apiKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"apiUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"webUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"batchIngestCaller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"timeout_ms",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_tenantId",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"hideInputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"hideOutputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingSampleRate",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"filteredPostUuids",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"autoBatchTracing",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"autoBatchQueue",{enumerable:!0,configurable:!0,writable:!0,value:new K}),Object.defineProperty(this,"autoBatchTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"autoBatchInitialDelayMs",{enumerable:!0,configurable:!0,writable:!0,value:250}),Object.defineProperty(this,"autoBatchAggregationDelayMs",{enumerable:!0,configurable:!0,writable:!0,value:50}),Object.defineProperty(this,"batchSizeBytesLimit",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fetchOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"settings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"blockOnRootRunFinalization",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"_serverInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_getServerInfoPromise",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let t=W.getDefaultClientConfig();this.tracingSampleRate=B(),this.apiUrl=q(e.apiUrl??t.apiUrl)??"",this.apiUrl.endsWith("/")&&(this.apiUrl=this.apiUrl.slice(0,-1)),this.apiKey=q(e.apiKey??t.apiKey),this.webUrl=q(e.webUrl??t.webUrl),this.webUrl?.endsWith("/")&&(this.webUrl=this.webUrl.slice(0,-1)),this.timeout_ms=e.timeout_ms??12e3,this.caller=new I(e.callerOptions??{}),this.batchIngestCaller=new I({...e.callerOptions??{},onFailedResponseHook:G}),this.hideInputs=e.hideInputs??e.anonymizer??t.hideInputs,this.hideOutputs=e.hideOutputs??e.anonymizer??t.hideOutputs,this.autoBatchTracing=e.autoBatchTracing??this.autoBatchTracing,this.blockOnRootRunFinalization=e.blockOnRootRunFinalization??this.blockOnRootRunFinalization,this.batchSizeBytesLimit=e.batchSizeBytesLimit,this.fetchOptions=e.fetchOptions||{}}static getDefaultClientConfig(){let e=ei("API_KEY"),t=ei("ENDPOINT")??"https://api.smith.langchain.com";return{apiUrl:t,apiKey:e,webUrl:void 0,hideInputs:"true"===ei("HIDE_INPUTS"),hideOutputs:"true"===ei("HIDE_OUTPUTS")}}getHostUrl(){if(this.webUrl)return this.webUrl;if(J(this.apiUrl))return this.webUrl="http://localhost:3000",this.webUrl;if(this.apiUrl.includes("/api")&&!this.apiUrl.split(".",1)[0].endsWith("api"))return this.webUrl=this.apiUrl.replace("/api",""),this.webUrl;if(this.apiUrl.split(".",1)[0].includes("dev"))return this.webUrl="https://dev.smith.langchain.com",this.webUrl;if(this.apiUrl.split(".",1)[0].includes("eu"))return this.webUrl="https://eu.smith.langchain.com",this.webUrl;else return this.webUrl="https://smith.langchain.com",this.webUrl}get headers(){let e={"User-Agent":`langsmith-js/${V}`};return this.apiKey&&(e["x-api-key"]=`${this.apiKey}`),e}processInputs(e){return!1===this.hideInputs?e:!0===this.hideInputs?{}:"function"==typeof this.hideInputs?this.hideInputs(e):e}processOutputs(e){return!1===this.hideOutputs?e:!0===this.hideOutputs?{}:"function"==typeof this.hideOutputs?this.hideOutputs(e):e}prepareRunCreateOrUpdateInputs(e){let t={...e};return void 0!==t.inputs&&(t.inputs=this.processInputs(t.inputs)),void 0!==t.outputs&&(t.outputs=this.processOutputs(t.outputs)),t}async _getResponse(e,t){let r=t?.toString()??"",a=`${this.apiUrl}${e}?${r}`,i=await this.caller.call(O(),a,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(i,`Failed to fetch ${e}`),i}async _get(e,t){return(await this._getResponse(e,t)).json()}async *_getPaginated(e,t=new URLSearchParams,r){let a=Number(t.get("offset"))||0,i=Number(t.get("limit"))||100;for(;;){t.set("offset",String(a)),t.set("limit",String(i));let n=`${this.apiUrl}${e}?${t}`,s=await this.caller.call(O(),n,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(s,`Failed to fetch ${e}`);let o=r?r(await s.json()):await s.json();if(0===o.length||(yield o,o.length<i))break;a+=o.length}}async *_getCursorPaginatedList(e,t=null,r="POST",a="runs"){let i=t?{...t}:{};for(;;){let t=await this.caller.call(O(),`${this.apiUrl}${e}`,{method:r,headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions,body:JSON.stringify(i)}),n=await t.json();if(!n||!n[a])break;yield n[a];let s=n.cursors;if(!s||!s.next)break;i.cursor=s.next}}_filterForSampling(e,t=!1){if(void 0===this.tracingSampleRate)return e;if(t){let t=[];for(let r of e)this.filteredPostUuids.has(r.id)?this.filteredPostUuids.delete(r.id):t.push(r);return t}{let t=[];for(let r of e)r.id!==r.trace_id&&!this.filteredPostUuids.has(r.trace_id)||Math.random()<this.tracingSampleRate?t.push(r):this.filteredPostUuids.add(r.id);return t}}async _getBatchSizeLimitBytes(){let e=await this._ensureServerInfo();return this.batchSizeBytesLimit??e.batch_ingest_config?.size_limit_bytes??0x1400000}async drainAutoBatchQueue(){for(;this.autoBatchQueue.items.length>=0;){let[e,t]=this.autoBatchQueue.pop(await this._getBatchSizeLimitBytes());if(!e.length)return void t();try{let t={runCreates:e.filter(e=>"create"===e.action).map(e=>e.item),runUpdates:e.filter(e=>"update"===e.action).map(e=>e.item)},r=await this._ensureServerInfo();r?.batch_ingest_config?.use_multipart_endpoint?await this.multipartIngestRuns(t):await this.batchIngestRuns(t)}finally{t()}}}async processRunOperation(e,t){let r=this.autoBatchTimeout;clearTimeout(this.autoBatchTimeout),this.autoBatchTimeout=void 0,"create"===e.action&&(e.item=F(e.item));let a=this.autoBatchQueue.push(e),i=await this._getBatchSizeLimitBytes();return(t||this.autoBatchQueue.sizeBytes>i)&&await this.drainAutoBatchQueue().catch(console.error),this.autoBatchQueue.items.length>0&&(this.autoBatchTimeout=setTimeout(()=>{this.autoBatchTimeout=void 0,this.drainAutoBatchQueue().catch(console.error)},r?this.autoBatchAggregationDelayMs:this.autoBatchInitialDelayMs)),a}async _getServerInfo(){let e=await O()(`${this.apiUrl}/info`,{method:"GET",headers:{Accept:"application/json"},signal:AbortSignal.timeout(1e3),...this.fetchOptions});return await N(e,"get server info"),e.json()}async _ensureServerInfo(){return void 0===this._getServerInfoPromise&&(this._getServerInfoPromise=(async()=>{if(void 0===this._serverInfo)try{this._serverInfo=await this._getServerInfo()}catch(e){console.warn("[WARNING]: LangSmith failed to fetch info on supported operations. Falling back to single calls and default limits.")}return this._serverInfo??{}})()),this._getServerInfoPromise.then(e=>(void 0===this._serverInfo&&(this._getServerInfoPromise=void 0),e))}async _getSettings(){return this.settings||(this.settings=this._get("/settings")),await this.settings}async createRun(e){if(!this._filterForSampling([e]).length)return;let t={...this.headers,"Content-Type":"application/json"},r=e.project_name;delete e.project_name;let a=this.prepareRunCreateOrUpdateInputs({session_name:r,...e,start_time:e.start_time??Date.now()});if(this.autoBatchTracing&&void 0!==a.trace_id&&void 0!==a.dotted_order)return void this.processRunOperation({action:"create",item:a}).catch(console.error);let i=F(a),n=await this.caller.call(O(),`${this.apiUrl}/runs`,{method:"POST",headers:t,body:D(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(n,"create run",!0)}async batchIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r=e?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[],a=t?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[];if(r.length>0&&a.length>0){let e=r.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of a)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);r=Object.values(e),a=t}let i={post:this._filterForSampling(r),patch:this._filterForSampling(a,!0)};if(!i.post.length&&!i.patch.length)return;if(void 0===(await this._ensureServerInfo()).version){for(let e of(this.autoBatchTracing=!1,i.post))await this.createRun(e);for(let e of i.patch)void 0!==e.id&&await this.updateRun(e.id,e);return}let n={post:[],patch:[]};for(let e of["post","patch"]){let t=i[e].reverse(),r=t.pop();for(;void 0!==r;)n[e].push(r),r=t.pop()}(n.post.length>0||n.patch.length>0)&&await this._postBatchIngestRuns(D(n))}async _postBatchIngestRuns(e){let t={...this.headers,"Content-Type":"application/json",Accept:"application/json"},r=await this.batchIngestCaller.call(O(),`${this.apiUrl}/runs/batch`,{method:"POST",headers:t,body:e,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(r,"batch create run",!0)}async multipartIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r={},a=[];for(let t of e??[]){let e=this.prepareRunCreateOrUpdateInputs(t);void 0!==e.id&&void 0!==e.attachments&&(r[e.id]=e.attachments),delete e.attachments,a.push(e)}let i=[];for(let e of t??[])i.push(this.prepareRunCreateOrUpdateInputs(e));if(void 0!==a.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when creating a run');if(void 0!==i.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when updating a run');if(a.length>0&&i.length>0){let e=a.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of i)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);a=Object.values(e),i=t}if(0===a.length&&0===i.length)return;let n=[],s=[];for(let[e,t]of[["post",a],["patch",i]])for(let a of t){let{inputs:t,outputs:i,events:o,...l}=a,u={inputs:t,outputs:i,events:o},d=D(l);for(let[t,r]of(s.push({name:`${e}.${l.id}`,payload:new Blob([d],{type:`application/json; length=${d.length}`})}),Object.entries(u))){if(void 0===r)continue;let a=D(r);s.push({name:`${e}.${l.id}.${t}`,payload:new Blob([a],{type:`application/json; length=${a.length}`})})}if(void 0!==l.id){let e=r[l.id];if(e)for(let[t,[a,i]]of(delete r[l.id],Object.entries(e)))s.push({name:`attachment.${l.id}.${t}`,payload:new Blob([i],{type:`${a}; length=${i.length}`})})}n.push(`trace=${l.trace_id},id=${l.id}`)}await this._sendMultipartRequest(s,n.join("; "))}async _sendMultipartRequest(e,t){try{let t=new FormData;for(let r of e)t.append(r.name,r.payload);await this.batchIngestCaller.call(O(),`${this.apiUrl}/runs/multipart`,{method:"POST",headers:{...this.headers},body:t,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions})}catch(r){let e="Failed to multipart ingest runs";r instanceof Error?e+=`: ${r.stack||r.message}`:e+=`: ${String(r)}`,console.warn(`${e.trim()}

Context: ${t}`)}}async updateRun(e,t){A(e),t.inputs&&(t.inputs=this.processInputs(t.inputs)),t.outputs&&(t.outputs=this.processOutputs(t.outputs));let r={...t,id:e};if(!this._filterForSampling([r],!0).length)return;if(this.autoBatchTracing&&void 0!==r.trace_id&&void 0!==r.dotted_order)return void 0!==t.end_time&&void 0===r.parent_run_id&&this.blockOnRootRunFinalization?void await this.processRunOperation({action:"update",item:r},!0):void this.processRunOperation({action:"update",item:r}).catch(console.error);let a={...this.headers,"Content-Type":"application/json"},i=await this.caller.call(O(),`${this.apiUrl}/runs/${e}`,{method:"PATCH",headers:a,body:D(t),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(i,"update run",!0)}async readRun(e,{loadChildRuns:t}={loadChildRuns:!1}){A(e);let r=await this._get(`/runs/${e}`);return t&&r.child_run_ids&&(r=await this._loadChildRuns(r)),r}async getRunUrl({runId:e,run:t,projectOpts:r}){if(void 0!==t){let e;e=t.session_id?t.session_id:r?.projectName?(await this.readProject({projectName:r?.projectName})).id:r?.projectId?r?.projectId:(await this.readProject({projectName:ei("PROJECT")||"default"})).id;let a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/projects/p/${e}/r/${t.id}?poll=true`}if(void 0!==e){let t=await this.readRun(e);if(!t.app_path)throw Error(`Run ${e} has no app_path`);let r=this.getHostUrl();return`${r}${t.app_path}`}throw Error("Must provide either runId or run")}async _loadChildRuns(e){let t=await z(this.listRuns({id:e.child_run_ids})),r={},a={};for(let e of(t.sort((e,t)=>(e?.dotted_order??"").localeCompare(t?.dotted_order??"")),t)){if(null===e.parent_run_id||void 0===e.parent_run_id)throw Error(`Child run ${e.id} has no parent`);e.parent_run_id in r||(r[e.parent_run_id]=[]),r[e.parent_run_id].push(e),a[e.id]=e}for(let t in e.child_runs=r[e.id]||[],r)t!==e.id&&(a[t].child_runs=r[t]);return e}async *listRuns(e){let{projectId:t,projectName:r,parentRunId:a,traceId:i,referenceExampleId:n,startTime:s,executionOrder:o,isRoot:l,runType:u,error:d,id:c,query:h,filter:p,traceFilter:m,treeFilter:f,limit:g,select:b}=e,w=[];if(t&&(w=Array.isArray(t)?t:[t]),r){let e=Array.isArray(r)?r:[r],t=await Promise.all(e.map(e=>this.readProject({projectName:e}).then(e=>e.id)));w.push(...t)}let y={session:w.length?w:null,run_type:u,reference_example:n,query:h,filter:p,trace_filter:m,tree_filter:f,execution_order:o,parent_run:a,start_time:s?s.toISOString():null,error:d,id:c,limit:g,trace:i,select:b||["app_path","child_run_ids","completion_cost","completion_tokens","dotted_order","end_time","error","events","extra","feedback_stats","first_token_time","id","inputs","name","outputs","parent_run_id","parent_run_ids","prompt_cost","prompt_tokens","reference_example_id","run_type","session_id","start_time","status","tags","total_cost","total_tokens","trace_id"],is_root:l},_=0;for await(let e of this._getCursorPaginatedList("/runs/query",y))if(g){if(_>=g)break;if(e.length+_>g){let t=e.slice(0,g-_);yield*t;break}_+=e.length,yield*e}else yield*e}async getRunStats({id:e,trace:t,parentRun:r,runType:a,projectNames:i,projectIds:n,referenceExampleIds:s,startTime:o,endTime:l,error:u,query:d,filter:c,traceFilter:h,treeFilter:p,isRoot:m,dataSourceType:f}){let g=n||[];i&&(g=[...n||[],...await Promise.all(i.map(e=>this.readProject({projectName:e}).then(e=>e.id)))]);let b=Object.fromEntries(Object.entries({id:e,trace:t,parent_run:r,run_type:a,session:g,reference_example:s,start_time:o,end_time:l,error:u,query:d,filter:c,trace_filter:h,tree_filter:p,is_root:m,data_source_type:f}).filter(([e,t])=>void 0!==t)),w=await this.caller.call(O(),`${this.apiUrl}/runs/stats`,{method:"POST",headers:this.headers,body:JSON.stringify(b),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await w.json()}async shareRun(e,{shareId:t}={}){let r={run_id:e,share_token:t||w.A()};A(e);let a=await this.caller.call(O(),`${this.apiUrl}/runs/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await a.json();if(null===i||!("share_token"in i))throw Error("Invalid response from server");return`${this.getHostUrl()}/public/${i.share_token}/r`}async unshareRun(e){A(e);let t=await this.caller.call(O(),`${this.apiUrl}/runs/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(t,"unshare run",!0)}async readRunSharedLink(e){A(e);let t=await this.caller.call(O(),`${this.apiUrl}/runs/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(null!==r&&"share_token"in r)return`${this.getHostUrl()}/public/${r.share_token}/r`}async listSharedRuns(e,{runIds:t}={}){let r=new URLSearchParams({share_token:e});if(void 0!==t)for(let e of t)r.append("id",e);A(e);let a=await this.caller.call(O(),`${this.apiUrl}/public/${e}/runs${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await a.json()}async readDatasetSharedSchema(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id),A(e);let r=await this.caller.call(O(),`${this.apiUrl}/datasets/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),a=await r.json();return a.url=`${this.getHostUrl()}/public/${a.share_token}/d`,a}async shareDataset(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id);let r={dataset_id:e};A(e);let a=await this.caller.call(O(),`${this.apiUrl}/datasets/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await a.json();return i.url=`${this.getHostUrl()}/public/${i.share_token}/d`,i}async unshareDataset(e){A(e);let t=await this.caller.call(O(),`${this.apiUrl}/datasets/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(t,"unshare dataset",!0)}async readSharedDataset(e){A(e);let t=await this.caller.call(O(),`${this.apiUrl}/public/${e}/datasets`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await t.json()}async listSharedExamples(e,t){let r={};t?.exampleIds&&(r.id=t.exampleIds);let a=new URLSearchParams;Object.entries(r).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>a.append(e,t)):a.append(e,t)});let i=await this.caller.call(O(),`${this.apiUrl}/public/${e}/examples?${a.toString()}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),n=await i.json();if(!i.ok){if("detail"in n)throw Error(`Failed to list shared examples.
Status: ${i.status}
Message: ${n.detail.join("\n")}`);throw Error(`Failed to list shared examples: ${i.status} ${i.statusText}`)}return n.map(e=>({...e,_hostUrl:this.getHostUrl()}))}async createProject({projectName:e,description:t=null,metadata:r=null,upsert:a=!1,projectExtra:i=null,referenceDatasetId:n=null}){let s=`${this.apiUrl}/sessions${a?"?upsert=true":""}`,o=i||{};r&&(o.metadata=r);let l={name:e,extra:o,description:t};null!==n&&(l.reference_dataset_id=n);let u=await this.caller.call(O(),s,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(u,"create project"),await u.json()}async updateProject(e,{name:t=null,description:r=null,metadata:a=null,projectExtra:i=null,endTime:n=null}){let s=`${this.apiUrl}/sessions/${e}`,o=i;a&&(o={...o||{},metadata:a});let l={name:t,extra:o,description:r,end_time:n?new Date(n).toISOString():null},u=await this.caller.call(O(),s,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(u,"update project"),await u.json()}async hasProject({projectId:e,projectName:t}){let r="/sessions",a=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)A(e),r+=`/${e}`;else if(void 0!==t)a.append("name",t);else throw Error("Must provide projectName or projectId");let i=await this.caller.call(O(),`${this.apiUrl}${r}?${a}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});try{let e=await i.json();if(!i.ok)return!1;if(Array.isArray(e))return e.length>0;return!0}catch(e){return!1}}async readProject({projectId:e,projectName:t,includeStats:r}){let a,i="/sessions",n=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)A(e),i+=`/${e}`;else if(void 0!==t)n.append("name",t);else throw Error("Must provide projectName or projectId");void 0!==r&&n.append("include_stats",r.toString());let s=await this._get(i,n);if(Array.isArray(s)){if(0===s.length)throw Error(`Project[id=${e}, name=${t}] not found`);a=s[0]}else a=s;return a}async getProjectUrl({projectId:e,projectName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either projectName or projectId");let r=await this.readProject({projectId:e,projectName:t}),a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/projects/p/${r.id}`}async getDatasetUrl({datasetId:e,datasetName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either datasetName or datasetId");let r=await this.readDataset({datasetId:e,datasetName:t}),a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/datasets/${r.id}`}async _getTenantId(){if(null!==this._tenantId)return this._tenantId;let e=new URLSearchParams({limit:"1"});for await(let t of this._getPaginated("/sessions",e))return this._tenantId=t[0].tenant_id,t[0].tenant_id;throw Error("No projects found to resolve tenant.")}async *listProjects({projectIds:e,name:t,nameContains:r,referenceDatasetId:a,referenceDatasetName:i,referenceFree:n,metadata:s}={}){let o=new URLSearchParams;if(void 0!==e)for(let t of e)o.append("id",t);if(void 0!==t&&o.append("name",t),void 0!==r&&o.append("name_contains",r),void 0!==a)o.append("reference_dataset",a);else if(void 0!==i){let e=await this.readDataset({datasetName:i});o.append("reference_dataset",e.id)}for await(let e of(void 0!==n&&o.append("reference_free",n.toString()),void 0!==s&&o.append("metadata",JSON.stringify(s)),this._getPaginated("/sessions",o)))yield*e}async deleteProject({projectId:e,projectName:t}){let r;if(void 0===e&&void 0===t)throw Error("Must provide projectName or projectId");if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");A(r=void 0===e?(await this.readProject({projectName:t})).id:e);let a=await this.caller.call(O(),`${this.apiUrl}/sessions/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(a,`delete session ${r} (${t})`,!0)}async uploadCsv({csvFile:e,fileName:t,inputKeys:r,outputKeys:a,description:i,dataType:n,name:s}){let o=`${this.apiUrl}/datasets/upload`,l=new FormData;l.append("file",e,t),r.forEach(e=>{l.append("input_keys",e)}),a.forEach(e=>{l.append("output_keys",e)}),i&&l.append("description",i),n&&l.append("data_type",n),s&&l.append("name",s);let u=await this.caller.call(O(),o,{method:"POST",headers:this.headers,body:l,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(u,"upload CSV"),await u.json()}async createDataset(e,{description:t,dataType:r,inputsSchema:a,outputsSchema:i,metadata:n}={}){let s={name:e,description:t,extra:n?{metadata:n}:void 0};r&&(s.data_type=r),a&&(s.inputs_schema_definition=a),i&&(s.outputs_schema_definition=i);let o=await this.caller.call(O(),`${this.apiUrl}/datasets`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(o,"create dataset"),await o.json()}async readDataset({datasetId:e,datasetName:t}){let r,a="/datasets",i=new URLSearchParams({limit:"1"});if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)A(e),a+=`/${e}`;else if(void 0!==t)i.append("name",t);else throw Error("Must provide datasetName or datasetId");let n=await this._get(a,i);if(Array.isArray(n)){if(0===n.length)throw Error(`Dataset[id=${e}, name=${t}] not found`);r=n[0]}else r=n;return r}async hasDataset({datasetId:e,datasetName:t}){try{return await this.readDataset({datasetId:e,datasetName:t}),!0}catch(e){if(e instanceof Error&&e.message.toLocaleLowerCase().includes("not found"))return!1;throw e}}async diffDatasetVersions({datasetId:e,datasetName:t,fromVersion:r,toVersion:a}){let i=e;if(void 0===i&&void 0===t)throw Error("Must provide either datasetName or datasetId");if(void 0!==i&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");void 0===i&&(i=(await this.readDataset({datasetName:t})).id);let n=new URLSearchParams({from_version:"string"==typeof r?r:r.toISOString(),to_version:"string"==typeof a?a:a.toISOString()});return await this._get(`/datasets/${i}/versions/diff`,n)}async readDatasetOpenaiFinetuning({datasetId:e,datasetName:t}){if(void 0!==e);else if(void 0!==t)e=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide datasetName or datasetId");let r=await this._getResponse(`/datasets/${e}/openai_ft`);return(await r.text()).trim().split("\n").map(e=>JSON.parse(e))}async *listDatasets({limit:e=100,offset:t=0,datasetIds:r,datasetName:a,datasetNameContains:i,metadata:n}={}){let s=new URLSearchParams({limit:e.toString(),offset:t.toString()});if(void 0!==r)for(let e of r)s.append("id",e);for await(let e of(void 0!==a&&s.append("name",a),void 0!==i&&s.append("name_contains",i),void 0!==n&&s.append("metadata",JSON.stringify(n)),this._getPaginated("/datasets",s)))yield*e}async updateDataset(e){let{datasetId:t,datasetName:r,...a}=e;if(!t&&!r)throw Error("Must provide either datasetName or datasetId");let i=t??(await this.readDataset({datasetName:r})).id;A(i);let n=await this.caller.call(O(),`${this.apiUrl}/datasets/${i}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(n,"update dataset"),await n.json()}async deleteDataset({datasetId:e,datasetName:t}){let r="/datasets",a=e;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==t&&(a=(await this.readDataset({datasetName:t})).id),void 0!==a)A(a),r+=`/${a}`;else throw Error("Must provide datasetName or datasetId");let i=await this.caller.call(O(),this.apiUrl+r,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(i,`delete ${r}`),await i.json()}async indexDataset({datasetId:e,datasetName:t,tag:r}){let a=e;if(a||t)if(a&&t)throw Error("Must provide either datasetName or datasetId, not both");else a||(a=(await this.readDataset({datasetName:t})).id);else throw Error("Must provide either datasetName or datasetId");A(a);let i=await this.caller.call(O(),`${this.apiUrl}/datasets/${a}/index`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({tag:r}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(i,"index dataset"),await i.json()}async similarExamples(e,t,r,{filter:a}={}){let i={limit:r,inputs:e};void 0!==a&&(i.filter=a),A(t);let n=await this.caller.call(O(),`${this.apiUrl}/datasets/${t}/search`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(n,"fetch similar examples"),(await n.json()).examples}async createExample(e,t,{datasetId:r,datasetName:a,createdAt:i,exampleId:n,metadata:s,split:o,sourceRunId:l}){let u=r;if(void 0===u&&void 0===a)throw Error("Must provide either datasetName or datasetId");if(void 0!==u&&void 0!==a)throw Error("Must provide either datasetName or datasetId, not both");void 0===u&&(u=(await this.readDataset({datasetName:a})).id);let d=i||new Date,c={dataset_id:u,inputs:e,outputs:t,created_at:d?.toISOString(),id:n,metadata:s,split:o,source_run_id:l},h=await this.caller.call(O(),`${this.apiUrl}/examples`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(c),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(h,"create example"),await h.json()}async createExamples(e){let{inputs:t,outputs:r,metadata:a,sourceRunIds:i,exampleIds:n,datasetId:s,datasetName:o}=e,l=s;if(void 0===l&&void 0===o)throw Error("Must provide either datasetName or datasetId");if(void 0!==l&&void 0!==o)throw Error("Must provide either datasetName or datasetId, not both");void 0===l&&(l=(await this.readDataset({datasetName:o})).id);let u=t.map((t,s)=>({dataset_id:l,inputs:t,outputs:r?r[s]:void 0,metadata:a?a[s]:void 0,split:e.splits?e.splits[s]:void 0,id:n?n[s]:void 0,source_run_id:i?i[s]:void 0})),d=await this.caller.call(O(),`${this.apiUrl}/examples/bulk`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(u),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(d,"create examples"),await d.json()}async createLLMExample(e,t,r){return this.createExample({input:e},{output:t},r)}async createChatExample(e,t,r){let a=e.map(e=>j(e)?P(e):e),i=j(t)?P(t):t;return this.createExample({input:a},{output:i},r)}async readExample(e){A(e);let t=`/examples/${e}`;return await this._get(t)}async *listExamples({datasetId:e,datasetName:t,exampleIds:r,asOf:a,splits:i,inlineS3Urls:n,metadata:s,limit:o,offset:l,filter:u}={}){let d;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)d=e;else if(void 0!==t)d=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide a datasetName or datasetId");let c=new URLSearchParams({dataset:d}),h=a?"string"==typeof a?a:a?.toISOString():void 0;if(h&&c.append("as_of",h),c.append("inline_s3_urls",(n??!0).toString()),void 0!==r)for(let e of r)c.append("id",e);if(void 0!==i)for(let e of i)c.append("splits",e);if(void 0!==s){let e=JSON.stringify(s);c.append("metadata",e)}void 0!==o&&c.append("limit",o.toString()),void 0!==l&&c.append("offset",l.toString()),void 0!==u&&c.append("filter",u);let p=0;for await(let e of this._getPaginated("/examples",c)){for(let t of e)yield t,p++;if(void 0!==o&&p>=o)break}}async deleteExample(e){A(e);let t=`/examples/${e}`,r=await this.caller.call(O(),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(r,`delete ${t}`),await r.json()}async updateExample(e,t){A(e);let r=await this.caller.call(O(),`${this.apiUrl}/examples/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(r,"update example"),await r.json()}async updateExamples(e){let t=await this.caller.call(O(),`${this.apiUrl}/examples/bulk`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(e),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(t,"update examples"),await t.json()}async listDatasetSplits({datasetId:e,datasetName:t,asOf:r}){let a;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");A(a=void 0===e?(await this.readDataset({datasetName:t})).id:e);let i=new URLSearchParams,n=r?"string"==typeof r?r:r?.toISOString():void 0;return n&&i.append("as_of",n),await this._get(`/datasets/${a}/splits`,i)}async updateDatasetSplits({datasetId:e,datasetName:t,splitName:r,exampleIds:a,remove:i=!1}){let n;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");A(n=void 0===e?(await this.readDataset({datasetName:t})).id:e);let s={split_name:r,examples:a.map(e=>(A(e),e)),remove:i},o=await this.caller.call(O(),`${this.apiUrl}/datasets/${n}/splits`,{method:"PUT",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(o,"update dataset splits",!0)}async evaluateRun(e,t,{sourceInfo:r,loadChildRuns:a,referenceExample:i}={loadChildRuns:!1}){let n;if(x("This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead."),"string"==typeof e)n=await this.readRun(e,{loadChildRuns:a});else if("object"==typeof e&&"id"in e)n=e;else throw Error(`Invalid run type: ${typeof e}`);null!==n.reference_example_id&&void 0!==n.reference_example_id&&(i=await this.readExample(n.reference_example_id));let s=await t.evaluateRun(n,i),[o,l]=await this._logEvaluationFeedback(s,n,r);return l[0]}async createFeedback(e,t,{score:r,value:a,correction:i,comment:n,sourceInfo:s,feedbackSourceType:o="api",sourceRunId:l,feedbackId:u,feedbackConfig:d,projectId:c,comparativeExperimentId:h}){if(!e&&!c)throw Error("One of runId or projectId must be provided");if(e&&c)throw Error("Only one of runId or projectId can be provided");let p={type:o??"api",metadata:s??{}};void 0===l||p?.metadata===void 0||p.metadata.__run||(p.metadata.__run={run_id:l}),p?.metadata!==void 0&&p.metadata.__run?.run_id!==void 0&&A(p.metadata.__run.run_id);let m={id:u??w.A(),run_id:e,key:t,score:r,value:a,correction:i,comment:n,feedback_source:p,comparative_experiment_id:h,feedbackConfig:d,session_id:c},f=`${this.apiUrl}/feedback`,g=await this.caller.call(O(),f,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(m),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(g,"create feedback",!0),m}async updateFeedback(e,{score:t,value:r,correction:a,comment:i}){let n={};null!=t&&(n.score=t),null!=r&&(n.value=r),null!=a&&(n.correction=a),null!=i&&(n.comment=i),A(e);let s=await this.caller.call(O(),`${this.apiUrl}/feedback/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(n),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(s,"update feedback",!0)}async readFeedback(e){A(e);let t=`/feedback/${e}`;return await this._get(t)}async deleteFeedback(e){A(e);let t=`/feedback/${e}`,r=await this.caller.call(O(),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(r,`delete ${t}`),await r.json()}async *listFeedback({runIds:e,feedbackKeys:t,feedbackSourceTypes:r}={}){let a=new URLSearchParams;if(e&&a.append("run",e.join(",")),t)for(let e of t)a.append("key",e);if(r)for(let e of r)a.append("source",e);for await(let e of this._getPaginated("/feedback",a))yield*e}async createPresignedFeedbackToken(e,t,{expiration:r,feedbackConfig:a}={}){let i={run_id:e,feedback_key:t,feedback_config:a};r?"string"==typeof r?i.expires_at=r:(r?.hours||r?.minutes||r?.days)&&(i.expires_in=r):i.expires_in={hours:3};let n=await this.caller.call(O(),`${this.apiUrl}/feedback/tokens`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await n.json()}async createComparativeExperiment({name:e,experimentIds:t,referenceDatasetId:r,createdAt:a,description:i,metadata:n,id:s}){if(0===t.length)throw Error("At least one experiment is required");if(r||(r=(await this.readProject({projectId:t[0]})).reference_dataset_id),null==!r)throw Error("A reference dataset is required");let o={id:s,name:e,experiment_ids:t,reference_dataset_id:r,description:i,created_at:(a??new Date)?.toISOString(),extra:{}};n&&(o.extra.metadata=n);let l=await this.caller.call(O(),`${this.apiUrl}/datasets/comparative`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await l.json()}async *listPresignedFeedbackTokens(e){A(e);let t=new URLSearchParams({run_id:e});for await(let e of this._getPaginated("/feedback/tokens",t))yield*e}_selectEvalResults(e){let t;return"results"in e?e.results:[e]}async _logEvaluationFeedback(e,t,r){let a=this._selectEvalResults(e),i=[];for(let e of a){let a=r||{};e.evaluatorInfo&&(a={...e.evaluatorInfo,...a});let n=null;e.targetRunId?n=e.targetRunId:t&&(n=t.id),i.push(await this.createFeedback(n,e.key,{score:e.score,value:e.value,comment:e.comment,correction:e.correction,sourceInfo:a,sourceRunId:e.sourceRunId,feedbackConfig:e.feedbackConfig,feedbackSourceType:"model"}))}return[a,i]}async logEvaluationFeedback(e,t,r){let[a]=await this._logEvaluationFeedback(e,t,r);return a}async *listAnnotationQueues(e={}){let{queueIds:t,name:r,nameContains:a,limit:i}=e,n=new URLSearchParams;t&&t.forEach((e,t)=>{A(e,`queueIds[${t}]`),n.append("ids",e)}),r&&n.append("name",r),a&&n.append("name_contains",a),n.append("limit",(void 0!==i?Math.min(i,100):100).toString());let s=0;for await(let e of this._getPaginated("/annotation-queues",n))if(yield*e,s++,void 0!==i&&s>=i)break}async createAnnotationQueue(e){let{name:t,description:r,queueId:a}=e,i={name:t,description:r,id:a||w.A()},n=await this.caller.call(O(),`${this.apiUrl}/annotation-queues`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(Object.fromEntries(Object.entries(i).filter(([e,t])=>void 0!==t))),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(n,"create annotation queue"),await n.json()}async readAnnotationQueue(e){let t=await this.listAnnotationQueues({queueIds:[e]}).next();if(t.done)throw Error(`Annotation queue with ID ${e} not found`);return t.value}async updateAnnotationQueue(e,t){let{name:r,description:a}=t,i=await this.caller.call(O(),`${this.apiUrl}/annotation-queues/${A(e,"queueId")}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({name:r,description:a}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(i,"update annotation queue")}async deleteAnnotationQueue(e){let t=await this.caller.call(O(),`${this.apiUrl}/annotation-queues/${A(e,"queueId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(t,"delete annotation queue")}async addRunsToAnnotationQueue(e,t){let r=await this.caller.call(O(),`${this.apiUrl}/annotation-queues/${A(e,"queueId")}/runs`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t.map((e,t)=>A(e,`runIds[${t}]`).toString())),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(r,"add runs to annotation queue")}async getRunFromAnnotationQueue(e,t){let r=`/annotation-queues/${A(e,"queueId")}/run`,a=await this.caller.call(O(),`${this.apiUrl}${r}/${t}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(a,"get run from annotation queue"),await a.json()}async _currentTenantIsOwner(e){let t=await this._getSettings();return"-"==e||t.tenant_handle===e}async _ownerConflictError(e,t){let r=await this._getSettings();return Error(`Cannot ${e} for another tenant.

      Current tenant: ${r.tenant_handle}

      Requested tenant: ${t}`)}async _getLatestCommitHash(e){let t=await this.caller.call(O(),`${this.apiUrl}/commits/${e}/?limit=1&offset=0`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(!t.ok){let e="string"==typeof r.detail?r.detail:JSON.stringify(r.detail),a=Error(`Error ${t.status}: ${t.statusText}
${e}`);throw a.statusCode=t.status,a}if(0!==r.commits.length)return r.commits[0].commit_hash}async _likeOrUnlikePrompt(e,t){let[r,a,i]=$(e),n=await this.caller.call(O(),`${this.apiUrl}/likes/${r}/${a}`,{method:"POST",body:JSON.stringify({like:t}),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(n,`${t?"like":"unlike"} prompt`),await n.json()}async _getPromptUrl(e){let[t,r,a]=$(e);if(await this._currentTenantIsOwner(t)){let e=await this._getSettings();return"latest"!==a?`${this.getHostUrl()}/prompts/${r}/${a.substring(0,8)}?organizationId=${e.id}`:`${this.getHostUrl()}/prompts/${r}?organizationId=${e.id}`}return"latest"!==a?`${this.getHostUrl()}/hub/${t}/${r}/${a.substring(0,8)}`:`${this.getHostUrl()}/hub/${t}/${r}`}async promptExists(e){return!!await this.getPrompt(e)}async likePrompt(e){return this._likeOrUnlikePrompt(e,!0)}async unlikePrompt(e){return this._likeOrUnlikePrompt(e,!1)}async *listCommits(e){for await(let t of this._getPaginated(`/commits/${e}/`,new URLSearchParams,e=>e.commits))yield*t}async *listPrompts(e){let t=new URLSearchParams;for await(let r of(t.append("sort_field",e?.sortField??"updated_at"),t.append("sort_direction","desc"),t.append("is_archived",(!!e?.isArchived).toString()),e?.isPublic!==void 0&&t.append("is_public",e.isPublic.toString()),e?.query&&t.append("query",e.query),this._getPaginated("/repos",t,e=>e.repos)))yield*r}async getPrompt(e){let[t,r,a]=$(e),i=await this.caller.call(O(),`${this.apiUrl}/repos/${t}/${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});if(404===i.status)return null;await N(i,"get prompt");let n=await i.json();return n.repo?n.repo:null}async createPrompt(e,t){let r=await this._getSettings();if(t?.isPublic&&!r.tenant_handle)throw Error(`Cannot create a public prompt without first

        creating a LangChain Hub handle. 
        You can add a handle by creating a public prompt at:

        https://smith.langchain.com/prompts`);let[a,i,n]=$(e);if(!await this._currentTenantIsOwner(a))throw await this._ownerConflictError("create a prompt",a);let s={repo_handle:i,...t?.description&&{description:t.description},...t?.readme&&{readme:t.readme},...t?.tags&&{tags:t.tags},is_public:!!t?.isPublic},o=await this.caller.call(O(),`${this.apiUrl}/repos/`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(s),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(o,"create prompt");let{repo:l}=await o.json();return l}async createCommit(e,t,r){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[a,i,n]=$(e),s=r?.parentCommitHash!=="latest"&&r?.parentCommitHash?r?.parentCommitHash:await this._getLatestCommitHash(`${a}/${i}`),o={manifest:JSON.parse(JSON.stringify(t)),parent_commit:s},l=await this.caller.call(O(),`${this.apiUrl}/commits/${a}/${i}`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(l,"create commit");let u=await l.json();return this._getPromptUrl(`${a}/${i}${u.commit_hash?`:${u.commit_hash}`:""}`)}async updatePrompt(e,t){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[r,a]=$(e);if(!await this._currentTenantIsOwner(r))throw await this._ownerConflictError("update a prompt",r);let i={};if(t?.description!==void 0&&(i.description=t.description),t?.readme!==void 0&&(i.readme=t.readme),t?.tags!==void 0&&(i.tags=t.tags),t?.isPublic!==void 0&&(i.is_public=t.isPublic),t?.isArchived!==void 0&&(i.is_archived=t.isArchived),0===Object.keys(i).length)throw Error("No valid update options provided");let n=await this.caller.call(O(),`${this.apiUrl}/repos/${r}/${a}`,{method:"PATCH",body:JSON.stringify(i),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await N(n,"update prompt"),n.json()}async deletePrompt(e){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[t,r,a]=$(e);if(!await this._currentTenantIsOwner(t))throw await this._ownerConflictError("delete a prompt",t);let i=await this.caller.call(O(),`${this.apiUrl}/repos/${t}/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await i.json()}async pullPromptCommit(e,t){let[r,a,i]=$(e),n=function(e,t){let r=(0,R.parse)(e),a=(0,R.parse)(t);if(!r||!a)throw Error("Invalid version format.");return r.compare(a)>=0}((await this._getServerInfo()).version,"0.5.23"),s=i;if(!n&&"latest"===i){let e=await this._getLatestCommitHash(`${r}/${a}`);if(e)s=e;else throw Error("No commits found")}let o=await this.caller.call(O(),`${this.apiUrl}/commits/${r}/${a}/${s}${t?.includeModel?"?include_model=true":""}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await N(o,"pull prompt commit");let l=await o.json();return{owner:r,repo:a,commit_hash:l.commit_hash,manifest:l.manifest,examples:l.examples}}async _pullPrompt(e,t){return JSON.stringify((await this.pullPromptCommit(e,{includeModel:t?.includeModel})).manifest)}async pushPrompt(e,t){return(await this.promptExists(e)?t&&Object.keys(t).some(e=>"object"!==e)&&await this.updatePrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}):await this.createPrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}),t?.object)?await this.createCommit(e,t?.object,{parentCommitHash:t?.parentCommitHash}):await this._getPromptUrl(e)}async clonePublicDataset(e,t={}){let{sourceApiUrl:r=this.apiUrl,datasetName:a}=t,[i,n]=this.parseTokenOrUrl(e,r),s=new W({apiUrl:i,apiKey:"placeholder"}),o=await s.readSharedDataset(n),l=a||o.name;try{if(await this.hasDataset({datasetId:l}))return void console.log(`Dataset ${l} already exists in your tenant. Skipping.`)}catch(e){}let u=await s.listSharedExamples(n),d=await this.createDataset(l,{description:o.description,dataType:o.data_type||"kv",inputsSchema:o.inputs_schema_definition??void 0,outputsSchema:o.outputs_schema_definition??void 0});try{await this.createExamples({inputs:u.map(e=>e.inputs),outputs:u.flatMap(e=>e.outputs?[e.outputs]:[]),datasetId:d.id})}catch(e){throw console.error(`An error occurred while creating dataset ${l}. You should delete it manually.`),e}}parseTokenOrUrl(e,t,r=2,a="dataset"){try{return A(e),[t,e]}catch(e){}try{let i=new URL(e).pathname.split("/").filter(e=>""!==e);if(i.length>=r){let e=i[i.length-r];return[t,e]}throw Error(`Invalid public ${a} URL: ${e}`)}catch(t){throw Error(`Invalid public ${a} URL or token: ${e}`)}}awaitPendingTraceBatches(){return Promise.all(this.autoBatchQueue.items.map(({itemPromise:e})=>e))}}let V="0.1.68",Q=()=>"undefined"!=typeof window&&void 0!==window.document,Y=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,Z=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),X=()=>"undefined"!=typeof Deno,ee=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!X(),et=()=>a||(a=Q()?"browser":ee()?"node":Y()?"webworker":Z()?"jsdom":X()?"deno":"other");function er(){return void 0===i&&(i={library:"langsmith",runtime:et(),sdk:"langsmith-js",sdk_version:V,...function(){if(void 0!==n)return n;let e={};for(let t of["VERCEL_GIT_COMMIT_SHA","NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA","COMMIT_REF","RENDER_GIT_COMMIT","CI_COMMIT_SHA","CIRCLE_SHA1","CF_PAGES_COMMIT_SHA","REACT_APP_GIT_SHA","SOURCE_VERSION","GITHUB_SHA","TRAVIS_COMMIT","GIT_COMMIT","BUILD_VCS_NUMBER","bamboo_planRepository_revision","Build.SourceVersion","BITBUCKET_COMMIT","DRONE_COMMIT_SHA","SEMAPHORE_GIT_SHA","BUILDKITE_COMMIT"]){let r=ea(t);void 0!==r&&(e[t]=r)}return n=e,e}()}),i}function ea(e){try{return"undefined"!=typeof process?process.env?.[e]:void 0}catch(e){return}}function ei(e){return ea(`LANGSMITH_${e}`)||ea(`LANGCHAIN_${e}`)}let en=e=>void 0!==e?e:!!["TRACING_V2","TRACING"].find(e=>"true"===ei(e)),es=Symbol.for("lc:context_variables");class eo{constructor(e,t){Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.metadata=e,this.tags=t}static fromHeader(e){let t=e.split(","),r={},a=[];for(let e of t){let[t,i]=e.split("="),n=decodeURIComponent(i);"langsmith-metadata"===t?r=JSON.parse(n):"langsmith-tags"===t&&(a=n.split(","))}return new eo(r,a)}toHeader(){let e=[];return this.metadata&&Object.keys(this.metadata).length>0&&e.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`),this.tags&&this.tags.length>0&&e.push(`langsmith-tags=${encodeURIComponent(this.tags.join(","))}`),e.join(",")}}class el{constructor(e){if(Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"run_type",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"project_name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"parent_run",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_runs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"start_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"end_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"extra",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"error",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"serialized",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"reference_example_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"events",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"trace_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"dotted_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingEnabled",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),eu(e))return void Object.assign(this,{...e});let t=el.getDefaultConfig(),{metadata:r,...a}=e,i=a.client??el.getSharedClient(),n={...r,...a?.extra?.metadata};if(a.extra={...a.extra,metadata:n},Object.assign(this,{...t,...a,client:i}),this.trace_id||(this.parent_run?this.trace_id=this.parent_run.trace_id??this.id:this.trace_id=this.id),this.execution_order??=1,this.child_execution_order??=1,!this.dotted_order){let e=function(e,t,r=1){let a=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${a}Z`.replace(/[-:.]/g,"")+t}(this.start_time,this.id,this.execution_order);this.parent_run?this.dotted_order=this.parent_run.dotted_order+"."+e:this.dotted_order=e}}static getDefaultConfig(){return{id:w.A(),run_type:"chain",project_name:ea("LANGCHAIN_PROJECT")??ea("LANGCHAIN_SESSION")??"default",child_runs:[],api_url:ea("LANGCHAIN_ENDPOINT")??"http://localhost:1984",api_key:ea("LANGCHAIN_API_KEY"),caller_options:{},start_time:Date.now(),serialized:{},inputs:{},extra:{}}}static getSharedClient(){return el.sharedClient||(el.sharedClient=new W),el.sharedClient}createChild(e){var t,r;let a=this.child_execution_order+1,i=new el({...e,parent_run:this,project_name:this.project_name,client:this.client,tracingEnabled:this.tracingEnabled,execution_order:a,child_execution_order:a});es in this&&(i[es]=this[es]);let n=Symbol.for("lc:child_config"),s=e.extra?.[n]??this.extra[n];if(void 0!==(t=s)&&"object"==typeof t.callbacks&&(ec(t.callbacks?.handlers)||ec(t.callbacks))){let e={...s},t="object"==typeof(r=e.callbacks)&&null!=r&&Array.isArray(r.handlers)?e.callbacks.copy?.():void 0;t&&(Object.assign(t,{_parentRunId:i.id}),t.handlers?.find(ed)?.updateFromRunTree?.(i),e.callbacks=t),i.extra[n]=e}let o=new Set,l=this;for(;null!=l&&!o.has(l.id);)o.add(l.id),l.child_execution_order=Math.max(l.child_execution_order,a),l=l.parent_run;return this.child_runs.push(i),i}async end(e,t,r=Date.now(),a){this.outputs=this.outputs??e,this.error=this.error??t,this.end_time=this.end_time??r,a&&Object.keys(a).length>0&&(this.extra=this.extra?{...this.extra,metadata:{...this.extra.metadata,...a}}:{metadata:a})}_convertToCreate(e,t,r=!0){let a,i,n=e.extra??{};if(n.runtime||(n.runtime={}),t)for(let[e,r]of Object.entries(t))n.runtime[e]||(n.runtime[e]=r);return r?(i=e.parent_run?.id,a=[]):(a=e.child_runs.map(e=>this._convertToCreate(e,t,r)),i=void 0),{id:e.id,name:e.name,start_time:e.start_time,end_time:e.end_time,run_type:e.run_type,reference_example_id:e.reference_example_id,extra:n,serialized:e.serialized,error:e.error,inputs:e.inputs,outputs:e.outputs,session_name:e.project_name,child_runs:a,parent_run_id:i,trace_id:e.trace_id,dotted_order:e.dotted_order,tags:e.tags}}async postRun(e=!0){try{let t=er(),r=await this._convertToCreate(this,t,!0);if(await this.client.createRun(r),!e)for(let e of(x("Posting with excludeChildRuns=false is deprecated and will be removed in a future version."),this.child_runs))await e.postRun(!1)}catch(e){console.error(`Error in postRun for run ${this.id}:`,e)}}async patchRun(){try{let e={end_time:this.end_time,error:this.error,inputs:this.inputs,outputs:this.outputs,parent_run_id:this.parent_run?.id,reference_example_id:this.reference_example_id,extra:this.extra,events:this.events,dotted_order:this.dotted_order,trace_id:this.trace_id,tags:this.tags};await this.client.updateRun(this.id,e)}catch(e){console.error(`Error in patchRun for run ${this.id}`,e)}}toJSON(){return this._convertToCreate(this,void 0,!1)}static fromRunnableConfig(e,t){let r,a,i,n=e?.callbacks,s=en();if(n){let e=n?.getParentRunId?.()??"",t=n?.handlers?.find(e=>e?.name=="langchain_tracer");r=t?.getRun?.(e),a=t?.projectName,i=t?.client,s=s||!!t}return r?new el({name:r.name,id:r.id,trace_id:r.trace_id,dotted_order:r.dotted_order,client:i,tracingEnabled:s,project_name:a,tags:[...new Set((r?.tags??[]).concat(e?.tags??[]))],extra:{metadata:{...r?.extra?.metadata,...e?.metadata}}}).createChild(t):new el({...t,client:i,tracingEnabled:s,project_name:a})}static fromDottedOrder(e){return this.fromHeaders({"langsmith-trace":e})}static fromHeaders(e,t){let r="get"in e&&"function"==typeof e.get?{"langsmith-trace":e.get("langsmith-trace"),baggage:e.get("baggage")}:e,a=r["langsmith-trace"];if(!a||"string"!=typeof a)return;let i=a.trim(),n=i.split(".").map(e=>{let[t,r]=e.split("Z");return{strTime:t,time:Date.parse(t+"Z"),uuid:r}}),s=n[0].uuid,o={...t,name:t?.name??"parent",run_type:t?.run_type??"chain",start_time:t?.start_time??Date.now(),id:n.at(-1)?.uuid,trace_id:s,dotted_order:i};if(r.baggage&&"string"==typeof r.baggage){let e=eo.fromHeader(r.baggage);o.metadata=e.metadata,o.tags=e.tags}return new el(o)}toHeaders(e){let t={"langsmith-trace":this.dotted_order,baggage:new eo(this.extra?.metadata,this.tags).toHeader()};if(e)for(let[r,a]of Object.entries(t))e.set(r,a);return t}}function eu(e){return void 0!==e&&"function"==typeof e.createChild&&"function"==typeof e.postRun}function ed(e){return"object"==typeof e&&null!=e&&"string"==typeof e.name&&"langchain_tracer"===e.name}function ec(e){return Array.isArray(e)&&e.some(e=>ed(e))}Object.defineProperty(el,"sharedClient",{enumerable:!0,configurable:!0,writable:!0,value:null});class eh{getStore(){}run(e,t){return t()}}let ep=Symbol.for("ls:tracing_async_local_storage"),em=new eh;class ef{getInstance(){return globalThis[ep]??em}initializeGlobalInstance(e){void 0===globalThis[ep]&&(globalThis[ep]=e)}}let eg=new ef,eb=()=>{let e=eg.getInstance().getStore();if(!eu(e))throw Error("Could not get the current run tree.\n\nPlease make sure you are calling this method within a traceable function or the tracing is enabled.");return e};function ew(e){return"function"==typeof e&&"langsmith:traceable"in e}Symbol.for("langsmith:traceable:root");let ey=Object.prototype.hasOwnProperty;function e_(e){switch(typeof e){case"object":return JSON.parse(JSON.stringify(e));case"undefined":return null;default:return e}}function ev(e){let t,r=0,a=e.length;for(;r<a;){if((t=e.charCodeAt(r))>=48&&t<=57){r++;continue}return!1}return!0}function eO(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function eE(e,t){let r=[e];for(let e in t){let a="object"==typeof t[e]?JSON.stringify(t[e],null,2):t[e];void 0!==a&&r.push(`${e}: ${a}`)}return r.join("\n")}class eS extends Error{constructor(e,t,r,a,i){super(eE(e,{name:t,index:r,operation:a,tree:i})),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"index",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"operation",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"tree",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.setPrototypeOf(this,new.target.prototype),this.message=eE(e,{name:t,index:r,operation:a,tree:i})}}let eI=eS,ej=e_,eP={add:function(e,t,r){return e[t]=this.value,{newDocument:r}},remove:function(e,t,r){var a=e[t];return delete e[t],{newDocument:r,removed:a}},replace:function(e,t,r){var a=e[t];return e[t]=this.value,{newDocument:r,removed:a}},move:function(e,t,r){let a=eA(r,this.path);a&&(a=e_(a));let i=ek(r,{op:"remove",path:this.from}).removed;return ek(r,{op:"add",path:this.path,value:i}),{newDocument:r,removed:a}},copy:function(e,t,r){let a=eA(r,this.from);return ek(r,{op:"add",path:this.path,value:e_(a)}),{newDocument:r}},test:function(e,t,r){return{newDocument:r,test:eN(e[t],this.value)}},_get:function(e,t,r){return this.value=e[t],{newDocument:r}}};var eT={add:function(e,t,r){return ev(t)?e.splice(t,0,this.value):e[t]=this.value,{newDocument:r,index:t}},remove:function(e,t,r){return{newDocument:r,removed:e.splice(t,1)[0]}},replace:function(e,t,r){var a=e[t];return e[t]=this.value,{newDocument:r,removed:a}},move:eP.move,copy:eP.copy,test:eP.test,_get:eP._get};function eA(e,t){if(""==t)return e;var r={op:"_get",path:t};return ek(e,r),r.value}function ek(e,t,r=!1,a=!0,i=!0,n=0){if(r&&("function"==typeof r?r(t,0,e,t.path):e$(t,0)),""===t.path){let a={newDocument:e};if("add"===t.op)return a.newDocument=t.value,a;if("replace"===t.op)return a.newDocument=t.value,a.removed=e,a;if("move"===t.op||"copy"===t.op)return a.newDocument=eA(e,t.from),"move"===t.op&&(a.removed=e),a;else if("test"===t.op){if(a.test=eN(e,t.value),!1===a.test)throw new eI("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return a.newDocument=e,a}else if("remove"===t.op)return a.removed=e,a.newDocument=null,a;else if("_get"===t.op)return t.value=e,a;else if(!r)return a;else throw new eI("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",n,t,e)}{let s,o,l;a||(e=e_(e));let u=(t.path||"").split("/"),d=e,c=1,h=u.length;for(o="function"==typeof r?r:e$;;){if((s=u[c])&&-1!=s.indexOf("~")&&(s=eO(s)),i&&("__proto__"==s||"prototype"==s&&c>0&&"constructor"==u[c-1]))throw TypeError("JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README");if(r&&void 0===l&&(void 0===d[s]?l=u.slice(0,c).join("/"):c==h-1&&(l=t.path),void 0!==l&&o(t,0,e,l)),c++,Array.isArray(d)){if("-"===s)s=d.length;else if(r&&!ev(s))throw new eI("Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index","OPERATION_PATH_ILLEGAL_ARRAY_INDEX",n,t,e);else ev(s)&&(s=~~s);if(c>=h){if(r&&"add"===t.op&&s>d.length)throw new eI("The specified index MUST NOT be greater than the number of elements in the array","OPERATION_VALUE_OUT_OF_BOUNDS",n,t,e);let a=eT[t.op].call(t,d,s,e);if(!1===a.test)throw new eI("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return a}}else if(c>=h){let r=eP[t.op].call(t,d,s,e);if(!1===r.test)throw new eI("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return r}if(d=d[s],r&&c<h&&(!d||"object"!=typeof d))throw new eI("Cannot perform operation at the desired path","OPERATION_PATH_UNRESOLVABLE",n,t,e)}}}function ex(e,t,r,a=!0,i=!0){if(r&&!Array.isArray(t))throw new eI("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");a||(e=e_(e));let n=Array(t.length);for(let a=0,s=t.length;a<s;a++)n[a]=ek(e,t[a],r,!0,i,a),e=n[a].newDocument;return n.newDocument=e,n}function eR(e,t,r){let a=ek(e,t);if(!1===a.test)throw new eI("Test operation failed","TEST_OPERATION_FAILED",r,t,e);return a.newDocument}function e$(e,t,r,a){if("object"!=typeof e||null===e||Array.isArray(e))throw new eI("Operation is not an object","OPERATION_NOT_AN_OBJECT",t,e,r);if(eP[e.op]){if("string"!=typeof e.path)throw new eI("Operation `path` property is not a string","OPERATION_PATH_INVALID",t,e,r);else if(0!==e.path.indexOf("/")&&e.path.length>0)throw new eI('Operation `path` property must start with "/"',"OPERATION_PATH_INVALID",t,e,r);else if(("move"===e.op||"copy"===e.op)&&"string"!=typeof e.from)throw new eI("Operation `from` property is not present (applicable in `move` and `copy` operations)","OPERATION_FROM_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&void 0===e.value)throw new eI("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&function e(t){if(void 0===t)return!0;if(t){if(Array.isArray(t)){for(let r=0,a=t.length;r<a;r++)if(e(t[r]))return!0}else if("object"==typeof t){let a=function(e){if(Array.isArray(e)){let t=Array(e.length);for(let e=0;e<t.length;e++)t[e]=""+e;return t}if(Object.keys)return Object.keys(e);let t=[];for(let r in e)ey.call(e,r)&&t.push(r);return t}(t),i=a.length;for(var r=0;r<i;r++)if(e(t[a[r]]))return!0}}return!1}(e.value))throw new eI("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED",t,e,r);else if(r){if("add"==e.op){var i=e.path.split("/").length,n=a.split("/").length;if(i!==n+1&&i!==n)throw new eI("Cannot perform an `add` operation at the desired path","OPERATION_PATH_CANNOT_ADD",t,e,r)}else if("replace"===e.op||"remove"===e.op||"_get"===e.op){if(e.path!==a)throw new eI("Cannot perform the operation at a path that does not exist","OPERATION_PATH_UNRESOLVABLE",t,e,r)}else if("move"===e.op||"copy"===e.op){var s=eC([{op:"_get",path:e.from,value:void 0}],r);if(s&&"OPERATION_PATH_UNRESOLVABLE"===s.name)throw new eI("Cannot perform the operation from a path that does not exist","OPERATION_FROM_UNRESOLVABLE",t,e,r)}}}else throw new eI("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",t,e,r)}function eC(e,t,r){try{if(!Array.isArray(e))throw new eI("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");if(t)ex(e_(t),e_(e),r||!0);else{r=r||e$;for(var a=0;a<e.length;a++)r(e[a],a,t,void 0)}}catch(e){if(e instanceof eI)return e;throw e}}function eN(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){var r,a,i,n=Array.isArray(e),s=Array.isArray(t);if(n&&s){if((a=e.length)!=t.length)return!1;for(r=a;0!=r--;)if(!eN(e[r],t[r]))return!1;return!0}if(n!=s)return!1;var o=Object.keys(e);if((a=o.length)!==Object.keys(t).length)return!1;for(r=a;0!=r--;)if(!t.hasOwnProperty(o[r]))return!1;for(r=a;0!=r--;)if(!eN(e[i=o[r]],t[i]))return!1;return!0}return e!=e&&t!=t}var eM=new WeakMap;({...l,JsonPatchError:eS,deepClone:e_,escapePathComponent:function(e){return -1===e.indexOf("/")&&-1===e.indexOf("~")?e:e.replace(/~/g,"~0").replace(/\//g,"~1")},unescapePathComponent:eO});var eL=r(57543);function eU(e,t){return t?.[e]||eL(e)}function eD(e){return Array.isArray(e)?[...e]:{...e}}function eH(e){let t=Object.getPrototypeOf(e);return"function"==typeof e.lc_name&&("function"!=typeof t.lc_name||e.lc_name()!==t.lc_name())?e.lc_name():e.name}r(51862);class eF{static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,eH(this.constructor)]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}constructor(e,...t){Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.lc_kwargs=e||{}}toJSON(){if(!this.lc_serializable||this.lc_kwargs instanceof eF||"object"!=typeof this.lc_kwargs||Array.isArray(this.lc_kwargs))return this.toJSONNotImplemented();let e={},t={},r=Object.keys(this.lc_kwargs).reduce((e,t)=>(e[t]=t in this?this[t]:this.lc_kwargs[t],e),{});for(let a=Object.getPrototypeOf(this);a;a=Object.getPrototypeOf(a))Object.assign(e,Reflect.get(a,"lc_aliases",this)),Object.assign(t,Reflect.get(a,"lc_secrets",this)),Object.assign(r,Reflect.get(a,"lc_attributes",this));return Object.keys(t).forEach(e=>{let t=this,a=r,[i,...n]=e.split(".").reverse();for(let e of n.reverse()){if(!(e in t)||void 0===t[e])return;e in a&&void 0!==a[e]||("object"==typeof t[e]&&null!=t[e]?a[e]={}:Array.isArray(t[e])&&(a[e]=[])),t=t[e],a=a[e]}i in t&&void 0!==t[i]&&(a[i]=a[i]||t[i])}),{lc:1,type:"constructor",id:this.lc_id,kwargs:function(e,t,r){let a={};for(let i in e)Object.hasOwn(e,i)&&(a[t(i,r)]=e[i]);return a}(Object.keys(t).length?function(e,t){let r=eD(e);for(let[e,a]of Object.entries(t)){let[t,...i]=e.split(".").reverse(),n=r;for(let e of i.reverse()){if(void 0===n[e])break;n[e]=eD(n[e]),n=n[e]}void 0!==n[t]&&(n[t]={lc:1,type:"secret",id:[a]})}return r}(r,t):r,eU,e)}}toJSONNotImplemented(){return{lc:1,type:"not_implemented",id:this.lc_id}}}let eB=()=>"undefined"!=typeof window&&void 0!==window.document,eJ=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,ez=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),eq=()=>"undefined"!=typeof Deno,eG=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!eq(),eK=()=>{let e;return eB()?"browser":eG()?"node":eJ()?"webworker":ez()?"jsdom":eq()?"deno":"other"};async function eW(){return void 0===s&&(s={library:"langchain-js",runtime:eK()}),s}function eV(e){try{return"undefined"!=typeof process?process.env?.[e]:void 0}catch(e){return}}class eQ{}class eY extends eQ{get lc_namespace(){return["langchain_core","callbacks",this.name]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,eH(this.constructor)]}constructor(e){super(),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ignoreLLM",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreChain",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreAgent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreRetriever",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreCustomEvent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"raiseError",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"awaitHandlers",{enumerable:!0,configurable:!0,writable:!0,value:"true"!==eV("LANGCHAIN_CALLBACKS_BACKGROUND")}),this.lc_kwargs=e||{},e&&(this.ignoreLLM=e.ignoreLLM??this.ignoreLLM,this.ignoreChain=e.ignoreChain??this.ignoreChain,this.ignoreAgent=e.ignoreAgent??this.ignoreAgent,this.ignoreRetriever=e.ignoreRetriever??this.ignoreRetriever,this.ignoreCustomEvent=e.ignoreCustomEvent??this.ignoreCustomEvent,this.raiseError=e.raiseError??this.raiseError,this.awaitHandlers=this.raiseError||(e._awaitHandler??this.awaitHandlers))}copy(){return new this.constructor(this)}toJSON(){return eF.prototype.toJSON.call(this)}toJSONNotImplemented(){return eF.prototype.toJSONNotImplemented.call(this)}static fromMethods(e){class t extends eY{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:w.A()}),Object.assign(this,e)}}return new t}}function eZ(e,t){return e&&!Array.isArray(e)&&"object"==typeof e?e:{[t]:e}}function eX(e){return"function"==typeof e._addRunToRunMap}class e0 extends eY{constructor(e){super(...arguments),Object.defineProperty(this,"runMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map})}copy(){return this}stringifyError(e){return e instanceof Error?e.message+(e?.stack?`

${e.stack}`:""):"string"==typeof e?e:`${e}`}_addChildRun(e,t){e.child_runs.push(t)}_addRunToRunMap(e){let t=function(e,t,r){let a=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${a}Z`.replace(/[-:.]/g,"")+t}(e.start_time,e.id,e.execution_order),r={...e};if(void 0!==r.parent_run_id){let e=this.runMap.get(r.parent_run_id);e&&(this._addChildRun(e,r),e.child_execution_order=Math.max(e.child_execution_order,r.child_execution_order),r.trace_id=e.trace_id,void 0!==e.dotted_order&&(r.dotted_order=[e.dotted_order,t].join(".")))}else r.trace_id=r.id,r.dotted_order=t;return this.runMap.set(r.id,r),r}async _endTrace(e){let t=void 0!==e.parent_run_id&&this.runMap.get(e.parent_run_id);t?t.child_execution_order=Math.max(t.child_execution_order,e.child_execution_order):await this.persistRun(e),this.runMap.delete(e.id),await this.onRunUpdate?.(e)}_getExecutionOrder(e){let t=void 0!==e&&this.runMap.get(e);return t?t.child_execution_order+1:1}_createRunForLLMStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),d=s?{...i,metadata:s}:i,c={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{prompts:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:d??{},tags:n||[]};return this._addRunToRunMap(c)}async handleLLMStart(e,t,r,a,i,n,s,o){let l=this.runMap.get(r)??this._createRunForLLMStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}_createRunForChatModelStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),d=s?{...i,metadata:s}:i,c={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{messages:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:d??{},tags:n||[]};return this._addRunToRunMap(c)}async handleChatModelStart(e,t,r,a,i,n,s,o){let l=this.runMap.get(r)??this._createRunForChatModelStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}async handleLLMEnd(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="llm")throw Error("No LLM run to end.");return r.end_time=Date.now(),r.outputs=e,r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onLLMEnd?.(r),await this._endTrace(r),r}async handleLLMError(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="llm")throw Error("No LLM run to end.");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onLLMError?.(r),await this._endTrace(r),r}_createRunForChainStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),d={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:t,execution_order:l,child_execution_order:l,run_type:s??"chain",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(d)}async handleChainStart(e,t,r,a,i,n,s,o){let l=this.runMap.get(r)??this._createRunForChainStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onChainStart?.(l),l}async handleChainEnd(e,t,r,a,i){let n=this.runMap.get(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.outputs=eZ(e,"output"),n.events.push({name:"end",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=eZ(i.inputs,"input")),await this.onChainEnd?.(n),await this._endTrace(n),n}async handleChainError(e,t,r,a,i){let n=this.runMap.get(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.error=this.stringifyError(e),n.events.push({name:"error",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=eZ(i.inputs,"input")),await this.onChainError?.(n),await this._endTrace(n),n}_createRunForToolStart(e,t,r,a,i,n,s){let o=this._getExecutionOrder(a),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:a,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{input:t},execution_order:o,child_execution_order:o,run_type:"tool",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(u)}async handleToolStart(e,t,r,a,i,n,s){let o=this.runMap.get(r)??this._createRunForToolStart(e,t,r,a,i,n,s);return await this.onRunCreate?.(o),await this.onToolStart?.(o),o}async handleToolEnd(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.outputs={output:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onToolEnd?.(r),await this._endTrace(r),r}async handleToolError(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onToolError?.(r),await this._endTrace(r),r}async handleAgentAction(e,t){let r=this.runMap.get(t);r&&r?.run_type==="chain"&&(r.actions=r.actions||[],r.actions.push(e),r.events.push({name:"agent_action",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentAction?.(r))}async handleAgentEnd(e,t){let r=this.runMap.get(t);r&&r?.run_type==="chain"&&(r.events.push({name:"agent_end",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentEnd?.(r))}_createRunForRetrieverStart(e,t,r,a,i,n,s){let o=this._getExecutionOrder(a),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:a,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{query:t},execution_order:o,child_execution_order:o,run_type:"retriever",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(u)}async handleRetrieverStart(e,t,r,a,i,n,s){let o=this.runMap.get(r)??this._createRunForRetrieverStart(e,t,r,a,i,n,s);return await this.onRunCreate?.(o),await this.onRetrieverStart?.(o),o}async handleRetrieverEnd(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.outputs={documents:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onRetrieverEnd?.(r),await this._endTrace(r),r}async handleRetrieverError(e,t){let r=this.runMap.get(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onRetrieverError?.(r),await this._endTrace(r),r}async handleText(e,t){let r=this.runMap.get(t);r&&r?.run_type==="chain"&&(r.events.push({name:"text",time:new Date().toISOString(),kwargs:{text:e}}),await this.onText?.(r))}async handleLLMNewToken(e,t,r,a,i,n){let s=this.runMap.get(r);if(!s||s?.run_type!=="llm")throw Error('Invalid "runId" provided to "handleLLMNewToken" callback.');return s.events.push({name:"new_token",time:new Date().toISOString(),kwargs:{token:e,idx:t,chunk:n?.chunk}}),await this.onLLMNewToken?.(s,e,{chunk:n?.chunk}),s}}var e1=r(73085);function e4(e,t){return`${e.open}${t}${e.close}`}function e3(e,t){try{return JSON.stringify(e,null,2)}catch(e){return t}}function e2(e){return"string"==typeof e?e.trim():null==e?e:e3(e,e.toString())}function e9(e){if(!e.end_time)return"";let t=e.end_time-e.start_time;return t<1e3?`${t}ms`:`${(t/1e3).toFixed(2)}s`}let{color:e5}=e1;class e6 extends e0{constructor(){super(...arguments),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"console_callback_handler"})}persistRun(e){return Promise.resolve()}getParents(e){let t=[],r=e;for(;r.parent_run_id;){let e=this.runMap.get(r.parent_run_id);if(e)t.push(e),r=e;else break}return t}getBreadcrumbs(e){let t=[...this.getParents(e).reverse(),e].map((e,t,r)=>{let a=`${e.execution_order}:${e.run_type}:${e.name}`;return t===r.length-1?e4(e1.bold,a):a}).join(" > ");return e4(e5.grey,t)}onChainStart(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.green,"[chain/start]")} [${t}] Entering Chain run with input: ${e3(e.inputs,"[inputs]")}`)}onChainEnd(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.cyan,"[chain/end]")} [${t}] [${e9(e)}] Exiting Chain run with output: ${e3(e.outputs,"[outputs]")}`)}onChainError(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.red,"[chain/error]")} [${t}] [${e9(e)}] Chain run errored with error: ${e3(e.error,"[error]")}`)}onLLMStart(e){let t=this.getBreadcrumbs(e),r="prompts"in e.inputs?{prompts:e.inputs.prompts.map(e=>e.trim())}:e.inputs;console.log(`${e4(e5.green,"[llm/start]")} [${t}] Entering LLM run with input: ${e3(r,"[inputs]")}`)}onLLMEnd(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.cyan,"[llm/end]")} [${t}] [${e9(e)}] Exiting LLM run with output: ${e3(e.outputs,"[response]")}`)}onLLMError(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.red,"[llm/error]")} [${t}] [${e9(e)}] LLM run errored with error: ${e3(e.error,"[error]")}`)}onToolStart(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.green,"[tool/start]")} [${t}] Entering Tool run with input: "${e2(e.inputs.input)}"`)}onToolEnd(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.cyan,"[tool/end]")} [${t}] [${e9(e)}] Exiting Tool run with output: "${e2(e.outputs?.output)}"`)}onToolError(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.red,"[tool/error]")} [${t}] [${e9(e)}] Tool run errored with error: ${e3(e.error,"[error]")}`)}onRetrieverStart(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.green,"[retriever/start]")} [${t}] Entering Retriever run with input: ${e3(e.inputs,"[inputs]")}`)}onRetrieverEnd(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.cyan,"[retriever/end]")} [${t}] [${e9(e)}] Exiting Retriever run with output: ${e3(e.outputs,"[outputs]")}`)}onRetrieverError(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.red,"[retriever/error]")} [${t}] [${e9(e)}] Retriever run errored with error: ${e3(e.error,"[error]")}`)}onAgentAction(e){let t=this.getBreadcrumbs(e);console.log(`${e4(e5.blue,"[agent/action]")} [${t}] Agent selected action: ${e3(e.actions[e.actions.length-1],"[action]")}`)}}class e8 extends Error{constructor(e,t){super(e),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.output=t}}class e7 extends eF{get lc_aliases(){return{additional_kwargs:"additional_kwargs",response_metadata:"response_metadata"}}get text(){return"string"==typeof this.content?this.content:""}constructor(e,t){"string"==typeof e&&(e={content:e,additional_kwargs:t,response_metadata:{}}),e.additional_kwargs||(e.additional_kwargs={}),e.response_metadata||(e.response_metadata={}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","messages"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"content",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"additional_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"response_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.content=e.content,this.additional_kwargs=e.additional_kwargs,this.response_metadata=e.response_metadata,this.id=e.id}toDict(){return{type:this._getType(),data:this.toJSON().kwargs}}static lc_name(){return"BaseMessage"}get _printableFields(){return{id:this.id,content:this.content,name:this.name,additional_kwargs:this.additional_kwargs,response_metadata:this.response_metadata}}_updateId(e){this.id=e,this.lc_kwargs.id=e}get[Symbol.toStringTag](){return this.constructor.lc_name()}[Symbol.for("nodejs.util.inspect.custom")](e){var t,r;if(null===e)return this;let a=(t=this._printableFields,r=Math.max(4,e),JSON.stringify(function e(t,a){if("object"!=typeof t||null==t)return t;if(a>=r)return Array.isArray(t)?"[Array]":"[Object]";if(Array.isArray(t))return t.map(t=>e(t,a+1));let i={};for(let r of Object.keys(t))i[r]=e(t[r],a+1);return i}(t,0),null,2));return`${this.constructor.lc_name()} ${a}`}}function te(e,t){let r={...e};for(let[e,a]of Object.entries(t))if(null==r[e])r[e]=a;else if(null==a)continue;else if(typeof r[e]!=typeof a||Array.isArray(r[e])!==Array.isArray(a))throw Error(`field[${e}] already exists in the message chunk, but with a different type.`);else if("string"==typeof r[e]){if("type"===e)continue;r[e]+=a}else if("object"!=typeof r[e]||Array.isArray(r[e]))if(Array.isArray(r[e]))r[e]=tt(r[e],a);else{if(r[e]===a)continue;console.warn(`field[${e}] already exists in this message chunk and value has unsupported type.`)}else r[e]=te(r[e],a);return r}function tt(e,t){if(void 0!==e||void 0!==t){if(void 0===e||void 0===t)return e||t;let r=[...e];for(let e of t)if("object"==typeof e&&"index"in e&&"number"==typeof e.index){let t=r.findIndex(t=>t.index===e.index);-1!==t?r[t]=te(r[t],e):r.push(e)}else{if("object"==typeof e&&"text"in e&&""===e.text)continue;r.push(e)}return r}}class tr extends e7{}class ta extends tr{constructor(e){let t;if("string"==typeof e)t={content:e,tool_calls:[],invalid_tool_calls:[],tool_call_chunks:[]};else if(void 0===e.tool_call_chunks)t={...e,tool_calls:e.tool_calls??[],invalid_tool_calls:[],tool_call_chunks:[]};else{let r=[],a=[];for(let t of e.tool_call_chunks){let e={};try{if(e=function(e){if(void 0===e)return null;try{return JSON.parse(e)}catch(e){}let t="",r=[],a=!1,i=!1;for(let n of e){if(a)'"'!==n||i?"\n"!==n||i?i="\\"===n&&!i:n="\\n":a=!1;else if('"'===n)a=!0,i=!1;else if("{"===n)r.push("}");else if("["===n)r.push("]");else if("}"===n||"]"===n)if(!r||r[r.length-1]!==n)return null;else r.pop();t+=n}a&&(t+='"');for(let e=r.length-1;e>=0;e-=1)t+=r[e];try{return JSON.parse(t)}catch(e){return null}}(t.args||"{}"),null===e||"object"!=typeof e||Array.isArray(e))throw Error("Malformed tool call chunk args.");r.push({name:t.name??"",args:e,id:t.id,type:"tool_call"})}catch(e){a.push({name:t.name,args:t.args,id:t.id,error:"Malformed args.",type:"invalid_tool_call"})}}t={...e,tool_calls:r,invalid_tool_calls:a}}super(t),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tool_call_chunks",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_chunks=t.tool_call_chunks??this.tool_call_chunks,this.tool_calls=t.tool_calls??this.tool_calls,this.invalid_tool_calls=t.invalid_tool_calls??this.invalid_tool_calls,this.usage_metadata=t.usage_metadata}get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls",tool_call_chunks:"tool_call_chunks"}}static lc_name(){return"AIMessageChunk"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,tool_call_chunks:this.tool_call_chunks,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}concat(e){let t={content:function(e,t){if("string"==typeof e)if("string"==typeof t)return e+t;else return[{type:"text",text:e},...t];return Array.isArray(t)?tt(e,t)??[...e,...t]:[...e,{type:"text",text:t}]}(this.content,e.content),additional_kwargs:te(this.additional_kwargs,e.additional_kwargs),response_metadata:te(this.response_metadata,e.response_metadata),tool_call_chunks:[],id:this.id??e.id};if(void 0!==this.tool_call_chunks||void 0!==e.tool_call_chunks){let r=tt(this.tool_call_chunks,e.tool_call_chunks);void 0!==r&&r.length>0&&(t.tool_call_chunks=r)}if(void 0!==this.usage_metadata||void 0!==e.usage_metadata){let r=this.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0},a=e.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0};t.usage_metadata={input_tokens:r.input_tokens+a.input_tokens,output_tokens:r.output_tokens+a.output_tokens,total_tokens:r.total_tokens+a.total_tokens}}return new ta(t)}}function ti(e){return _isToolCall(e)?e:"string"==typeof e.id&&"function"===e.type&&"object"==typeof e.function&&null!==e.function&&"arguments"in e.function&&"string"==typeof e.function.arguments&&"name"in e.function&&"string"==typeof e.function.name?{id:e.id,args:JSON.parse(e.function.arguments),name:e.function.name,type:"tool_call"}:e}class tn extends e0{constructor(e={}){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"langchain_tracer"}),Object.defineProperty(this,"projectName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{exampleId:t,projectName:r,client:a}=e;this.projectName=r??eV("LANGCHAIN_PROJECT")??eV("LANGCHAIN_SESSION"),this.exampleId=t,this.client=a??new W({});let i=tn.getTraceableRunTree();i&&this.updateFromRunTree(i)}async _convertToCreate(e,t){return{...e,extra:{...e.extra,runtime:await eW()},child_runs:void 0,session_name:this.projectName,reference_example_id:e.parent_run_id?void 0:t}}async persistRun(e){}async onRunCreate(e){let t=await this._convertToCreate(e,this.exampleId);await this.client.createRun(t)}async onRunUpdate(e){let t={end_time:e.end_time,error:e.error,outputs:e.outputs,events:e.events,inputs:e.inputs,trace_id:e.trace_id,dotted_order:e.dotted_order,parent_run_id:e.parent_run_id};await this.client.updateRun(e.id,t)}getRun(e){return this.runMap.get(e)}updateFromRunTree(e){let t=e,r=new Set;for(;t.parent_run&&!r.has(t.id)&&(r.add(t.id),t.parent_run);){;t=t.parent_run}r.clear();let a=[t];for(;a.length>0;){let e=a.shift();!(!e||r.has(e.id))&&(r.add(e.id),this.runMap.set(e.id,e),e.child_runs&&a.push(...e.child_runs))}this.client=e.client??this.client,this.projectName=e.project_name??this.projectName,this.exampleId=e.reference_example_id??this.exampleId}convertToRunTree(e){let t={},r=[];for(let[e,a]of this.runMap){let i=new el({...a,child_runs:[],parent_run:void 0,client:this.client,project_name:this.projectName,reference_example_id:this.exampleId,tracingEnabled:!0});t[e]=i,r.push([e,a.dotted_order])}for(let[e]of(r.sort((e,t)=>e[1]&&t[1]?e[1].localeCompare(t[1]):0),r)){let r=this.runMap.get(e),a=t[e];if(r&&a&&r.parent_run_id){let e=t[r.parent_run_id];e&&(e.child_runs.push(a),a.parent_run=e)}}return t[e]}static getTraceableRunTree(){try{return eb()}catch{return}}}async function ts(e,t){!0===t?await e():(void 0===o&&(o=new(0,y.default)({autoStart:!0,concurrency:1})),o.add(e))}let to=e=>void 0!==e?e:!!["LANGSMITH_TRACING_V2","LANGCHAIN_TRACING_V2","LANGSMITH_TRACING","LANGCHAIN_TRACING"].find(e=>"true"===eV(e));class tl{setHandler(e){return this.setHandlers([e])}}class tu{constructor(e,t,r,a,i,n,s,o){Object.defineProperty(this,"runId",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:s}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:o})}get parentRunId(){return this._parentRunId}async handleText(e){await Promise.all(this.handlers.map(t=>ts(async()=>{try{await t.handleText?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleText: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleCustomEvent(e,t,r,a,i){await Promise.all(this.handlers.map(r=>ts(async()=>{try{await r.handleCustomEvent?.(e,t,this.runId,this.tags,this.metadata)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleCustomEvent: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}}class td extends tu{getChild(e){let t=new tm(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleRetrieverEnd(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetriever`),t.raiseError)throw e}},t.awaitHandlers)))}async handleRetrieverError(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverError?.(e,this.runId,this._parentRunId,this.tags)}catch(r){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetrieverError: ${r}`),t.raiseError)throw e}},t.awaitHandlers)))}}class tc extends tu{async handleLLMNewToken(e,t,r,a,i,n){await Promise.all(this.handlers.map(r=>ts(async()=>{if(!r.ignoreLLM)try{await r.handleLLMNewToken?.(e,t??{prompt:0,completion:0},this.runId,this._parentRunId,this.tags,n)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMNewToken: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}async handleLLMError(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreLLM)try{await t.handleLLMError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleLLMEnd(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreLLM)try{await t.handleLLMEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class th extends tu{getChild(e){let t=new tm(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleChainError(e,t,r,a,i){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreChain)try{await t.handleChainError?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleChainEnd(e,t,r,a,i){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreChain)try{await t.handleChainEnd?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentAction(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreAgent)try{await t.handleAgentAction?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentAction: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentEnd(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreAgent)try{await t.handleAgentEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class tp extends tu{getChild(e){let t=new tm(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleToolError(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreAgent)try{await t.handleToolError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleToolEnd(e){await Promise.all(this.handlers.map(t=>ts(async()=>{if(!t.ignoreAgent)try{await t.handleToolEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class tm extends tl{constructor(e,t){super(),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"callback_manager"}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.handlers=t?.handlers??this.handlers,this.inheritableHandlers=t?.inheritableHandlers??this.inheritableHandlers,this.tags=t?.tags??this.tags,this.inheritableTags=t?.inheritableTags??this.inheritableTags,this.metadata=t?.metadata??this.metadata,this.inheritableMetadata=t?.inheritableMetadata??this.inheritableMetadata,this._parentRunId=e}getParentRunId(){return this._parentRunId}async handleLLMStart(e,t,r,a,i,n,s,o){return Promise.all(t.map(async(t,a)=>{let n=0===a&&r?r:(0,w.A)();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return eX(r)&&r._createRunForLLMStart(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o),ts(async()=>{try{await r.handleLLMStart?.(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new tc(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChatModelStart(e,t,r,a,i,n,s,o){return Promise.all(t.map(async(t,a)=>{let n=0===a&&r?r:(0,w.A)();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return eX(r)&&r._createRunForChatModelStart(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o),ts(async()=>{try{if(r.handleChatModelStart)await r.handleChatModelStart?.(e,[t],n,this._parentRunId,i,this.tags,this.metadata,o);else if(r.handleLLMStart){let a=function(e,t="Human",r="AI"){let a=[];for(let i of e){let e;if("human"===i._getType())e=t;else if("ai"===i._getType())e=r;else if("system"===i._getType())e="System";else if("function"===i._getType())e="Function";else if("tool"===i._getType())e="Tool";else if("generic"===i._getType())e=i.role;else throw Error(`Got unsupported message type: ${i._getType()}`);let n=i.name?`${i.name}, `:"",s="string"==typeof i.content?i.content:JSON.stringify(i.content,null,2);a.push(`${e}: ${n}${s}`)}return a.join("\n")}(t);await r.handleLLMStart?.(e,[a],n,this._parentRunId,i,this.tags,this.metadata,o)}}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new tc(n,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChainStart(e,t,r=(0,w.A)(),a,i,n,s){return await Promise.all(this.handlers.map(i=>{if(!i.ignoreChain)return eX(i)&&i._createRunForChainStart(e,t,r,this._parentRunId,this.tags,this.metadata,a,s),ts(async()=>{try{await i.handleChainStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,a,s)}catch(e){if((i.raiseError?console.error:console.warn)(`Error in handler ${i.constructor.name}, handleChainStart: ${e}`),i.raiseError)throw e}},i.awaitHandlers)})),new th(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleToolStart(e,t,r=(0,w.A)(),a,i,n,s){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreAgent)return eX(a)&&a._createRunForToolStart(e,t,r,this._parentRunId,this.tags,this.metadata,s),ts(async()=>{try{await a.handleToolStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleToolStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new tp(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleRetrieverStart(e,t,r=(0,w.A)(),a,i,n,s){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreRetriever)return eX(a)&&a._createRunForRetrieverStart(e,t,r,this._parentRunId,this.tags,this.metadata,s),ts(async()=>{try{await a.handleRetrieverStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,s)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleRetrieverStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new td(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleCustomEvent(e,t,r,a,i){await Promise.all(this.handlers.map(a=>ts(async()=>{if(!a.ignoreCustomEvent)try{await a.handleCustomEvent?.(e,t,r,this.tags,this.metadata)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleCustomEvent: ${e}`),a.raiseError)throw e}},a.awaitHandlers)))}addHandler(e,t=!0){this.handlers.push(e),t&&this.inheritableHandlers.push(e)}removeHandler(e){this.handlers=this.handlers.filter(t=>t!==e),this.inheritableHandlers=this.inheritableHandlers.filter(t=>t!==e)}setHandlers(e,t=!0){for(let r of(this.handlers=[],this.inheritableHandlers=[],e))this.addHandler(r,t)}addTags(e,t=!0){this.removeTags(e),this.tags.push(...e),t&&this.inheritableTags.push(...e)}removeTags(e){this.tags=this.tags.filter(t=>!e.includes(t)),this.inheritableTags=this.inheritableTags.filter(t=>!e.includes(t))}addMetadata(e,t=!0){this.metadata={...this.metadata,...e},t&&(this.inheritableMetadata={...this.inheritableMetadata,...e})}removeMetadata(e){for(let t of Object.keys(e))delete this.metadata[t],delete this.inheritableMetadata[t]}copy(e=[],t=!0){let r=new tm(this._parentRunId);for(let e of this.handlers){let t=this.inheritableHandlers.includes(e);r.addHandler(e,t)}for(let e of this.tags){let t=this.inheritableTags.includes(e);r.addTags([e],t)}for(let e of Object.keys(this.metadata)){let t=Object.keys(this.inheritableMetadata).includes(e);r.addMetadata({[e]:this.metadata[e]},t)}for(let a of e)r.handlers.filter(e=>"console_callback_handler"===e.name).some(e=>e.name===a.name)||r.addHandler(a,t);return r}static fromHandlers(e){class t extends eY{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:(0,w.A)()}),Object.assign(this,e)}}let r=new this;return r.addHandler(new t),r}static async configure(e,t,r,a,i,n,s){return this._configureSync(e,t,r,a,i,n,s)}static _configureSync(e,t,r,a,i,n,s){let o;(e||t)&&(Array.isArray(e)||!e?(o=new tm).setHandlers(e?.map(tf)??[],!0):o=e,o=o.copy(Array.isArray(t)?t.map(tf):t?.handlers,!1));let l="true"===eV("LANGCHAIN_VERBOSE")||s?.verbose,u=tn.getTraceableRunTree()?.tracingEnabled||to(),d=u||(eV("LANGCHAIN_TRACING")??!1);if(l||d){if(o||(o=new tm),l&&!o.handlers.some(e=>e.name===e6.prototype.name)){let e=new e6;o.addHandler(e,!0)}if(d&&!o.handlers.some(e=>"langchain_tracer"===e.name)&&u){let e=new tn;o.addHandler(e,!0),o._parentRunId=tn.getTraceableRunTree()?.id??o._parentRunId}}return(r||a)&&o&&(o.addTags(r??[]),o.addTags(a??[],!1)),(i||n)&&o&&(o.addMetadata(i??{}),o.addMetadata(n??{},!1)),o}}function tf(e){return"name"in e?e:eY.fromMethods(e)}class tg{getStore(){}run(e,t){return t()}}let tb=new tg,tw=Symbol.for("ls:tracing_async_local_storage"),ty=Symbol.for("lc:child_config");class t_{getInstance(){return globalThis[tw]??tb}getRunnableConfig(){let e=this.getInstance();return e.getStore()?.extra?.[ty]}runWithConfig(e,t,r){let a,i=tm._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata),n=this.getInstance(),s=i?.getParentRunId(),o=i?.handlers?.find(e=>e?.name==="langchain_tracer");return o&&s?a=o.convertToRunTree(s):r||(a=new el({name:"<runnable_lambda>",tracingEnabled:!1})),a&&(a.extra={...a.extra,[ty]:e}),n.run(a,t)}initializeGlobalInstance(e){void 0===globalThis[tw]&&(globalThis[tw]=e)}}let tv=new t_;async function tO(e,t){let r;return void 0===t?e:Promise.race([e.catch(e=>{if(!t?.aborted)throw e}),new Promise((e,a)=>{r=()=>{a(Error("Aborted"))},t.addEventListener("abort",r),t.aborted&&a(Error("Aborted"))})]).finally(()=>t.removeEventListener("abort",r))}class tE extends ReadableStream{constructor(){super(...arguments),Object.defineProperty(this,"reader",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}ensureReader(){this.reader||(this.reader=this.getReader())}async next(){this.ensureReader();try{let e=await this.reader.read();if(e.done)return this.reader.releaseLock(),{done:!0,value:void 0};return{done:!1,value:e.value}}catch(e){throw this.reader.releaseLock(),e}}async return(){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}return{done:!0,value:void 0}}async throw(e){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}throw e}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}static fromReadableStream(e){let t=e.getReader();return new tE({start:e=>(function r(){return t.read().then(({done:t,value:a})=>t?void e.close():(e.enqueue(a),r()))})(),cancel(){t.releaseLock()}})}static fromAsyncGenerator(e){return new tE({async pull(t){let{value:r,done:a}=await e.next();a&&t.close(),t.enqueue(r)},async cancel(t){await e.return(t)}})}}function tS(e,t=2){let r=Array.from({length:t},()=>[]);return r.map(async function*(t){for(;;)if(0===t.length){let t=await e.next();for(let e of r)e.push(t)}else{if(t[0].done)return;yield t.shift().value}})}function tI(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.concat(t);if("string"==typeof e&&"string"==typeof t)return e+t;if("number"==typeof e&&"number"==typeof t)return e+t;if("concat"in e&&"function"==typeof e.concat)return e.concat(t);if("object"==typeof e&&"object"==typeof t){let r={...e};for(let[e,a]of Object.entries(t))e in r&&!Array.isArray(r[e])?r[e]=tI(r[e],a):r[e]=a;return r}else throw Error(`Cannot concat ${typeof e} and ${typeof t}`)}class tj{constructor(e){Object.defineProperty(this,"generator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"setup",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"signal",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResult",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResultUsed",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.generator=e.generator,this.config=e.config,this.signal=e.signal??this.config?.signal,this.setup=new Promise((t,r)=>{tv.runWithConfig(e.config,async()=>{this.firstResult=e.generator.next(),e.startSetup?this.firstResult.then(e.startSetup).then(t,r):this.firstResult.then(e=>t(void 0),r)},!0)})}async next(...e){return(this.signal?.throwIfAborted(),this.firstResultUsed)?tv.runWithConfig(this.config,this.signal?async()=>tO(this.generator.next(...e),this.signal):async()=>this.generator.next(...e),!0):(this.firstResultUsed=!0,this.firstResult)}async return(e){return this.generator.return(e)}async throw(e){return this.generator.throw(e)}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}}async function tP(e,t,r,a,...i){let n=new tj({generator:t,startSetup:r,signal:a}),s=await n.setup;return{output:e(n,s,...i),setup:s}}class tT{constructor(e){Object.defineProperty(this,"ops",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.ops=e.ops??[]}concat(e){let t=this.ops.concat(e.ops),r=ex({},t);return new tA({ops:t,state:r[r.length-1].newDocument})}}class tA extends tT{constructor(e){super(e),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.state=e.state}concat(e){let t=this.ops.concat(e.ops),r=ex(this.state,e.ops);return new tA({ops:t,state:r[r.length-1].newDocument})}static fromRunLogPatch(e){let t=ex({},e.ops);return new tA({ops:e.ops,state:t[t.length-1].newDocument})}}let tk=e=>"log_stream_tracer"===e.name;async function tx(e,t){if("original"===t)throw Error("Do not assign inputs with original schema drop the key for now. When inputs are added to streamLog they should be added with standardized schema for streaming events.");let{inputs:r}=e;return["retriever","llm","prompt"].includes(e.run_type)?r:1!==Object.keys(r).length||r?.input!==""?r.input:void 0}async function tR(e,t){let{outputs:r}=e;return"original"===t||["retriever","llm","prompt"].includes(e.run_type)?r:void 0!==r&&1===Object.keys(r).length&&r?.output!==void 0?r.output:r}class t$ extends e0{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaFormat",{enumerable:!0,configurable:!0,writable:!0,value:"original"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"keyMapByRunId",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"counterMapByRunName",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"log_stream_tracer"}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this._schemaFormat=e?._schemaFormat??this._schemaFormat,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=tE.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){if(e.id===this.rootId)return!1;let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.run_type)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.run_type)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){for await(let r of t){if(e!==this.rootId){let t=this.keyMapByRunId[e];t&&await this.writer.write(new tT({ops:[{op:"add",path:`/logs/${t}/streamed_output/-`,value:r}]}))}yield r}}async onRunCreate(e){if(void 0===this.rootId&&(this.rootId=e.id,await this.writer.write(new tT({ops:[{op:"replace",path:"",value:{id:e.id,name:e.name,type:e.run_type,streamed_output:[],final_output:void 0,logs:{}}}]}))),!this._includeRun(e))return;void 0===this.counterMapByRunName[e.name]&&(this.counterMapByRunName[e.name]=0),this.counterMapByRunName[e.name]+=1;let t=this.counterMapByRunName[e.name];this.keyMapByRunId[e.id]=1===t?e.name:`${e.name}:${t}`;let r={id:e.id,name:e.name,type:e.run_type,tags:e.tags??[],metadata:e.extra?.metadata??{},start_time:new Date(e.start_time).toISOString(),streamed_output:[],streamed_output_str:[],final_output:void 0,end_time:void 0};"streaming_events"===this._schemaFormat&&(r.inputs=await tx(e,this._schemaFormat)),await this.writer.write(new tT({ops:[{op:"add",path:`/logs/${this.keyMapByRunId[e.id]}`,value:r}]}))}async onRunUpdate(e){try{let t=this.keyMapByRunId[e.id];if(void 0===t)return;let r=[];"streaming_events"===this._schemaFormat&&r.push({op:"replace",path:`/logs/${t}/inputs`,value:await tx(e,this._schemaFormat)}),r.push({op:"add",path:`/logs/${t}/final_output`,value:await tR(e,this._schemaFormat)}),void 0!==e.end_time&&r.push({op:"add",path:`/logs/${t}/end_time`,value:new Date(e.end_time).toISOString()});let a=new tT({ops:r});await this.writer.write(a)}finally{if(e.id===this.rootId){let t=new tT({ops:[{op:"replace",path:"/final_output",value:await tR(e,this._schemaFormat)}]});await this.writer.write(t),this.autoClose&&await this.writer.close()}}}async onLLMNewToken(e,t,r){let a,i=this.keyMapByRunId[e.id];if(void 0===i)return;if(void 0!==e.inputs.messages){var n;a=void 0!==(n=r?.chunk)&&void 0!==n.message?r?.chunk:new ta({id:`run-${e.id}`,content:t})}else a=t;let s=new tT({ops:[{op:"add",path:`/logs/${i}/streamed_output_str/-`,value:t},{op:"add",path:`/logs/${i}/streamed_output/-`,value:a}]});await this.writer.write(s)}}class tC{constructor(e){Object.defineProperty(this,"text",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"generationInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.text=e.text,this.generationInfo=e.generationInfo}concat(e){return new tC({text:this.text+e.text,generationInfo:{...this.generationInfo,...e.generationInfo}})}}function tN({name:e,serialized:t}){return void 0!==e?e:t?.name!==void 0?t.name:t?.id!==void 0&&Array.isArray(t?.id)?t.id[t.id.length-1]:"Unnamed"}let tM=e=>"event_stream_tracer"===e.name;class tL extends e0{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"runInfoMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"tappedPromises",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"event_stream_tracer"}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=tE.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.runType)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.runType)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){let r=await t.next();if(r.done)return;let a=this.runInfoMap.get(e);if(void 0===a)return void(yield r.value);function i(e,t){return"llm"===e&&"string"==typeof t?new tC({text:t}):t}let n=this.tappedPromises.get(e);if(void 0===n){let s;n=new Promise(e=>{s=e}),this.tappedPromises.set(e,n);try{let n={event:`on_${a.runType}_stream`,run_id:e,name:a.name,tags:a.tags,metadata:a.metadata,data:{}};for await(let e of(await this.send({...n,data:{chunk:i(a.runType,r.value)}},a),yield r.value,t))"tool"!==a.runType&&"retriever"!==a.runType&&await this.send({...n,data:{chunk:i(a.runType,e)}},a),yield e}finally{s()}}else for await(let e of(yield r.value,t))yield e}async send(e,t){this._includeRun(t)&&await this.writer.write(e)}async sendEndEvent(e,t){let r=this.tappedPromises.get(e.run_id);void 0!==r?r.then(()=>{this.send(e,t)}):await this.send(e,t)}async onLLMStart(e){let t=tN(e),r=void 0!==e.inputs.messages?"chat_model":"llm",a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:r,inputs:e.inputs};this.runInfoMap.set(e.id,a);let i=`on_${r}_start`;await this.send({event:i,data:{input:e.inputs},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},a)}async onLLMNewToken(e,t,r){let a,i,n=this.runInfoMap.get(e.id);if(void 0===n)throw Error(`onLLMNewToken: Run ID ${e.id} not found in run map.`);if(1!==this.runInfoMap.size){if("chat_model"===n.runType)i="on_chat_model_stream",a=r?.chunk===void 0?new ta({content:t,id:`run-${e.id}`}):r.chunk.message;else if("llm"===n.runType)i="on_llm_stream",a=r?.chunk===void 0?new tC({text:t}):r.chunk;else throw Error(`Unexpected run type ${n.runType}`);await this.send({event:i,data:{chunk:a},run_id:e.id,name:n.name,tags:n.tags,metadata:n.metadata},n)}}async onLLMEnd(e){let t,r,a=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===a)throw Error(`onLLMEnd: Run ID ${e.id} not found in run map.`);let i=e.outputs?.generations;if("chat_model"===a.runType){for(let e of i??[]){if(void 0!==r)break;r=e[0]?.message}t="on_chat_model_end"}else if("llm"===a.runType)r={generations:i?.map(e=>e.map(e=>({text:e.text,generationInfo:e.generationInfo}))),llmOutput:e.outputs?.llmOutput??{}},t="on_llm_end";else throw Error(`onLLMEnd: Unexpected run type: ${a.runType}`);await this.sendEndEvent({event:t,data:{output:r,input:a.inputs},run_id:e.id,name:a.name,tags:a.tags,metadata:a.metadata},a)}async onChainStart(e){let t=tN(e),r=e.run_type??"chain",a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:e.run_type},i={};""===e.inputs.input&&1===Object.keys(e.inputs).length?(i={},a.inputs={}):void 0!==e.inputs.input?(i.input=e.inputs.input,a.inputs=e.inputs.input):(i.input=e.inputs,a.inputs=e.inputs),this.runInfoMap.set(e.id,a),await this.send({event:`on_${r}_start`,data:i,name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},a)}async onChainEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onChainEnd: Run ID ${e.id} not found in run map.`);let r=`on_${e.run_type}_end`,a=e.inputs??t.inputs??{},i={output:e.outputs?.output??e.outputs,input:a};a.input&&1===Object.keys(a).length&&(i.input=a.input,t.inputs=a.input),await this.sendEndEvent({event:r,data:i,run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata??{}},t)}async onToolStart(e){let t=tN(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"tool",inputs:e.inputs??{}};this.runInfoMap.set(e.id,r),await this.send({event:"on_tool_start",data:{input:e.inputs??{}},name:t,run_id:e.id,tags:e.tags??[],metadata:e.extra?.metadata??{}},r)}async onToolEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onToolEnd: Run ID ${e.id} not found in run map.`);if(void 0===t.inputs)throw Error(`onToolEnd: Run ID ${e.id} is a tool call, and is expected to have traced inputs.`);let r=e.outputs?.output===void 0?e.outputs:e.outputs.output;await this.sendEndEvent({event:"on_tool_end",data:{output:r,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async onRetrieverStart(e){let t=tN(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"retriever",inputs:{query:e.inputs.query}};this.runInfoMap.set(e.id,r),await this.send({event:"on_retriever_start",data:{input:{query:e.inputs.query}},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},r)}async onRetrieverEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onRetrieverEnd: Run ID ${e.id} not found in run map.`);await this.sendEndEvent({event:"on_retriever_end",data:{output:e.outputs?.documents??e.outputs,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async handleCustomEvent(e,t,r){let a=this.runInfoMap.get(r);if(void 0===a)throw Error(`handleCustomEvent: Run ID ${r} not found in run map.`);await this.send({event:"on_custom_event",run_id:r,name:e,tags:a.tags,metadata:a.metadata,data:t},a)}async finish(){Promise.all([...this.tappedPromises.values()]).finally(()=>{this.writer.close()})}}async function tU(e){return tm._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata)}function tD(...e){let t={};for(let r of e.filter(e=>!!e))for(let e of Object.keys(r))if("metadata"===e)t[e]={...t[e],...r[e]};else if("tags"===e){let a=t[e]??[];t[e]=[...new Set(a.concat(r[e]??[]))]}else if("configurable"===e)t[e]={...t[e],...r[e]};else if("timeout"===e)void 0===t.timeout?t.timeout=r.timeout:void 0!==r.timeout&&(t.timeout=Math.min(t.timeout,r.timeout));else if("signal"===e)void 0===t.signal?t.signal=r.signal:void 0!==r.signal&&("any"in AbortSignal?t.signal=AbortSignal.any([t.signal,r.signal]):t.signal=r.signal);else if("callbacks"===e){let e=t.callbacks,a=r.callbacks;if(Array.isArray(a))if(e)if(Array.isArray(e))t.callbacks=e.concat(a);else{let r=e.copy();for(let e of a)r.addHandler(tf(e),!0);t.callbacks=r}else t.callbacks=a;else if(a)if(e)if(Array.isArray(e)){let r=a.copy();for(let t of e)r.addHandler(tf(t),!0);t.callbacks=r}else t.callbacks=new tm(a._parentRunId,{handlers:e.handlers.concat(a.handlers),inheritableHandlers:e.inheritableHandlers.concat(a.inheritableHandlers),tags:Array.from(new Set(e.tags.concat(a.tags))),inheritableTags:Array.from(new Set(e.inheritableTags.concat(a.inheritableTags))),metadata:{...e.metadata,...a.metadata}});else t.callbacks=a}else t[e]=r[e]??t[e];return t}let tH=new Set(["string","number","boolean"]);function tF(e){let t=tv.getRunnableConfig(),r={tags:[],metadata:{},recursionLimit:25,runId:void 0};if(t){let{runId:e,runName:a,...i}=t;r=Object.entries(i).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)}if(e&&(r=Object.entries(e).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)),r?.configurable)for(let e of Object.keys(r.configurable))tH.has(typeof r.configurable[e])&&!r.metadata?.[e]&&(r.metadata||(r.metadata={}),r.metadata[e]=r.configurable[e]);if(void 0!==r.timeout){if(r.timeout<=0)throw Error("Timeout must be a positive number");let e=AbortSignal.timeout(r.timeout);void 0!==r.signal?"any"in AbortSignal&&(r.signal=AbortSignal.any([r.signal,e])):r.signal=e,delete r.timeout}return r}function tB(e={},{callbacks:t,maxConcurrency:r,recursionLimit:a,runName:i,configurable:n,runId:s}={}){let o=tF(e);return void 0!==t&&(delete o.runName,o.callbacks=t),void 0!==a&&(o.recursionLimit=a),void 0!==r&&(o.maxConcurrency=r),void 0!==i&&(o.runName=i),void 0!==n&&(o.configurable={...o.configurable,...n}),void 0!==s&&delete o.runId,o}let tJ=[400,401,402,403,404,405,406,407,409],tz=e=>{if(e.message.startsWith("Cancel")||e.message.startsWith("AbortError")||"AbortError"===e.name||e?.code==="ECONNABORTED")throw e;let t=e?.response?.status??e?.status;if(t&&tJ.includes(+t))throw e;if(e?.error?.code==="insufficient_quota"){let t=Error(e?.message);throw t.name="InsufficientQuotaError",t}};class tq{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.onFailedAttempt=e.onFailedAttempt??tz;let t=y.default;this.queue=new t({concurrency:this.maxConcurrency})}call(e,...t){return this.queue.add(()=>b(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{onFailedAttempt:this.onFailedAttempt,retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>fetch(...e).then(e=>e.ok?e:Promise.reject(e)))}}class tG extends e0{constructor({config:e,onStart:t,onEnd:r,onError:a}){super({_awaitHandler:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"RootListenersTracer"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnStart",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnEnd",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnError",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.config=e,this.argOnStart=t,this.argOnEnd=r,this.argOnError=a}persistRun(e){return Promise.resolve()}async onRunCreate(e){!this.rootId&&(this.rootId=e.id,this.argOnStart&&(1===this.argOnStart.length?await this.argOnStart(e):2===this.argOnStart.length&&await this.argOnStart(e,this.config)))}async onRunUpdate(e){e.id===this.rootId&&(e.error?this.argOnError&&(1===this.argOnError.length?await this.argOnError(e):2===this.argOnError.length&&await this.argOnError(e,this.config)):this.argOnEnd&&(1===this.argOnEnd.length?await this.argOnEnd(e):2===this.argOnEnd.length&&await this.argOnEnd(e,this.config)))}}function tK(e){return!!e&&e.lc_runnable}class tW{constructor(e){Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.includeNames=e.includeNames,this.includeTypes=e.includeTypes,this.includeTags=e.includeTags,this.excludeNames=e.excludeNames,this.excludeTypes=e.excludeTypes,this.excludeTags=e.excludeTags}includeEvent(e,t){let r=void 0===this.includeNames&&void 0===this.includeTypes&&void 0===this.includeTags,a=e.tags??[];return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(t)),void 0!==this.includeTags&&(r=r||a.some(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(t)),void 0!==this.excludeTags&&(r=r&&a.every(e=>!this.excludeTags?.includes(e))),r}}var tV=r(3576);function tQ(e){return e.replace(/[^a-zA-Z-_0-9]/g,"_")}async function tY(e,t){let{backgroundColor:r="white"}=t??{},a=btoa(e);void 0!==r&&(/^#(?:[0-9a-fA-F]{3}){1,2}$/.test(r)||(r=`!${r}`));let i=`https://mermaid.ink/img/${a}?bgColor=${r}`,n=await fetch(i);if(!n.ok)throw Error(`Failed to render the graph using the Mermaid.INK API.
Status code: ${n.status}
Status text: ${n.statusText}`);return await n.blob()}function tZ(e){if(!(0,T.A)(e.id))return e.id;if(!tK(e.data))return e.data.name??"UnknownSchema";try{let t=e.data.getName();return(t=t.startsWith("Runnable")?t.slice(8):t).length>42&&(t=`${t.substring(0,42)}...`),t}catch(t){return e.data.getName()}}class tX{constructor(){Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"edges",{enumerable:!0,configurable:!0,writable:!0,value:[]})}toJSON(){let e={};return Object.values(this.nodes).forEach((t,r)=>{e[t.id]=(0,T.A)(t.id)?r:t.id}),{nodes:Object.values(this.nodes).map(t=>({id:e[t.id],...tK(t.data)?{type:"runnable",data:{id:t.data.lc_id,name:t.data.getName()}}:{type:"schema",data:{...(0,tV.Ik)(t.data.schema),title:t.data.name}}})),edges:this.edges.map(t=>{let r={source:e[t.source],target:e[t.target]};return void 0!==t.data&&(r.data=t.data),void 0!==t.conditional&&(r.conditional=t.conditional),r})}}addNode(e,t){if(void 0!==t&&void 0!==this.nodes[t])throw Error(`Node with id ${t} already exists`);let r=t||(0,w.A)(),a={id:r,data:e};return this.nodes[r]=a,a}removeNode(e){delete this.nodes[e.id],this.edges=this.edges.filter(t=>t.source!==e.id&&t.target!==e.id)}addEdge(e,t,r,a){if(void 0===this.nodes[e.id])throw Error(`Source node ${e.id} not in graph`);if(void 0===this.nodes[t.id])throw Error(`Target node ${t.id} not in graph`);let i={source:e.id,target:t.id,data:r,conditional:a};return this.edges.push(i),i}firstNode(){let e=new Set(this.edges.map(e=>e.target)),t=[];return Object.values(this.nodes).forEach(r=>{e.has(r.id)||t.push(r)}),t[0]}lastNode(){let e=new Set(this.edges.map(e=>e.source)),t=[];return Object.values(this.nodes).forEach(r=>{e.has(r.id)||t.push(r)}),t[0]}extend(e,t=""){let r=t;Object.values(e.nodes).map(e=>e.id).every(T.A)&&(r="");let a=e=>r?`${r}:${e}`:e;Object.entries(e.nodes).forEach(([e,t])=>{this.nodes[a(e)]={...t,id:a(e)}});let i=e.edges.map(e=>({...e,source:a(e.source),target:a(e.target)}));this.edges=[...this.edges,...i];let n=e.firstNode(),s=e.lastNode();return[n?{id:a(n.id),data:n.data}:void 0,s?{id:a(s.id),data:s.data}:void 0]}trimFirstNode(){let e=this.firstNode();if(e){let t=this.edges.filter(t=>t.source===e.id);(1===Object.keys(this.nodes).length||1===t.length)&&this.removeNode(e)}}trimLastNode(){let e=this.lastNode();if(e){let t=this.edges.filter(t=>t.target===e.id);(1===Object.keys(this.nodes).length||1===t.length)&&this.removeNode(e)}}drawMermaid(e){let{withStyles:t,curveStyle:r,nodeColors:a={start:"#ffdfba",end:"#baffc9",other:"#fad7de"},wrapLabelNWords:i}=e??{},n={};for(let e of Object.values(this.nodes))n[e.id]=tZ(e);let s=this.firstNode(),o=s?tZ(s):void 0,l=this.lastNode(),u=l?tZ(l):void 0;return function(e,t,r){let{firstNodeLabel:a,lastNodeLabel:i,nodeColors:n,withStyles:s=!0,curveStyle:o="linear",wrapLabelNWords:l=9}=r??{},u=s?`%%{init: {'flowchart': {'curve': '${o}'}}}%%
graph TD;
`:"graph TD;\n";if(s){let t="default",r={[t]:"{0}([{1}]):::otherclass"};for(let n of(void 0!==a&&(r[a]="{0}[{0}]:::startclass"),void 0!==i&&(r[i]="{0}[{0}]:::endclass"),Object.values(e))){let e=r[n]??r[t],a=tQ(n),i=n.split(":"),s=i[i.length-1];u+=`	${e.replace(/\{0\}/g,a).replace(/\{1\}/g,s)};
`}}let d="";for(let r of t){var c,h;let t=r.source.includes(":")?r.source.split(":")[0]:void 0,a=r.target.includes(":")?r.target.split(":")[0]:void 0;""!==d&&(d!==t||d!==a)&&(u+="	end\n",d=""),""===d&&void 0!==t&&t===a&&(u=`	subgraph ${t}
`,d=t);let[i,n]=(c=r,[(h=e)[c.source]??c.source,h[c.target]??c.target]),s="";if(void 0!==r.data){let e=r.data,t=e.split(" ");t.length>l&&(e=t.reduce((e,t,r)=>(r%l==0&&e.push(""),e[e.length-1]+=` ${t}`,e),[]).join("<br>")),s=r.conditional?` -. ${e} .-> `:` -- ${e} --> `}else s=r.conditional?" -.-> ":" --\x3e ";u+=`	${tQ(i)}${s}${tQ(n)};
`}return""!==d&&(u+="end\n"),s&&void 0!==n&&(u+=function(e){let t="";for(let[r,a]of Object.entries(e))t+=`	classDef ${r}class fill:${a};
`;return t}(n)),u}(n,this.edges,{firstNodeLabel:o,lastNodeLabel:u,withStyles:t,curveStyle:r,nodeColors:a,wrapLabelNWords:i})}async drawMermaidPng(e){return tY(this.drawMermaid(e),{backgroundColor:e?.backgroundColor})}}function t0(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.iterator]&&"function"==typeof e.next}let t1=e=>null!=e&&"object"==typeof e&&"next"in e&&"function"==typeof e.next;function t4(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.asyncIterator]}function*t3(e,t){for(;;){let{value:r,done:a}=tv.runWithConfig(e,t.next.bind(t),!0);if(a)break;yield r}}async function*t2(e,t){let r=t[Symbol.asyncIterator]();for(;;){let{value:a,done:i}=await tv.runWithConfig(e,r.next.bind(t),!0);if(i)break;yield a}}function t9(e,t){return!e||Array.isArray(e)||e instanceof Date||"object"!=typeof e?{[t]:e}:e}class t5 extends eF{constructor(){super(...arguments),Object.defineProperty(this,"lc_runnable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}getName(e){let t=this.name??this.constructor.lc_name()??this.constructor.name;return e?`${t}${e}`:t}bind(e){return new t6({bound:this,kwargs:e,config:{}})}map(){return new t8({bound:this})}withRetry(e){return new t7({bound:this,kwargs:{},config:{},maxAttemptNumber:e?.stopAfterAttempt,...e})}withConfig(e){return new t6({bound:this,config:e,kwargs:{}})}withFallbacks(e){return new ri({runnable:this,fallbacks:Array.isArray(e)?e:e.fallbacks})}_getOptionsList(e,t=0){if(Array.isArray(e)&&e.length!==t)throw Error(`Passed "options" must be an array with the same length as the inputs, but got ${e.length} options for ${t} inputs`);if(Array.isArray(e))return e.map(tF);if(t>1&&!Array.isArray(e)&&e.runId){console.warn("Provided runId will be used only for the first element of the batch.");let r=Object.fromEntries(Object.entries(e).filter(([e])=>"runId"!==e));return Array.from({length:t},(t,a)=>tF(0===a?e:r))}return Array.from({length:t},()=>tF(e))}async batch(e,t,r){let a=this._getOptionsList(t??{},e.length),i=new tq({maxConcurrency:a[0]?.maxConcurrency??r?.maxConcurrency,onFailedAttempt:e=>{throw e}});return Promise.all(e.map((e,t)=>i.call(async()=>{try{return await this.invoke(e,a[t])}catch(e){if(r?.returnExceptions)return e;throw e}})))}async *_streamIterator(e,t){yield this.invoke(e,t)}async stream(e,t){let r=tF(t),a=new tj({generator:this._streamIterator(e,r),config:r});return await a.setup,tE.fromAsyncGenerator(a)}_separateRunnableConfigFromCallOptions(e){let t;t=void 0===e?tF(e):tF({callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,runName:e.runName,configurable:e.configurable,recursionLimit:e.recursionLimit,maxConcurrency:e.maxConcurrency,runId:e.runId,timeout:e.timeout,signal:e.signal});let r={...e};return delete r.callbacks,delete r.tags,delete r.metadata,delete r.runName,delete r.configurable,delete r.recursionLimit,delete r.maxConcurrency,delete r.runId,delete r.timeout,delete r.signal,[t,r]}async _callWithConfig(e,t,r){let a,i=tF(r),n=await tU(i),s=await n?.handleChainStart(this.toJSON(),t9(t,"input"),i.runId,i?.runType,void 0,void 0,i?.runName??this.getName());delete i.runId;try{let n=e.call(this,t,i,s);a=await tO(n,r?.signal)}catch(e){throw await s?.handleChainError(e),e}return await s?.handleChainEnd(t9(a,"output")),a}async _batchWithConfig(e,t,r,a){let i,n=this._getOptionsList(r??{},t.length),s=await Promise.all(n.map(tU)),o=await Promise.all(s.map(async(e,r)=>{let a=await e?.handleChainStart(this.toJSON(),t9(t[r],"input"),n[r].runId,n[r].runType,void 0,void 0,n[r].runName??this.getName());return delete n[r].runId,a}));try{let r=e.call(this,t,n,o,a);i=await tO(r,n?.[0]?.signal)}catch(e){throw await Promise.all(o.map(t=>t?.handleChainError(e))),e}return await Promise.all(o.map(e=>e?.handleChainEnd(t9(i,"output")))),i}async *_transformStreamWithConfig(e,t,r){let a,i,n,s=!0,o=!0,l=tF(r),u=await tU(l);async function*d(){for await(let t of e){if(s)if(void 0===a)a=t;else try{a=tI(a,t)}catch{a=void 0,s=!1}yield t}}try{let e=await tP(t.bind(this),d(),async()=>u?.handleChainStart(this.toJSON(),{input:""},l.runId,l.runType,void 0,void 0,l.runName??this.getName()),r?.signal,l);delete l.runId,n=e.setup;let a=n?.handlers.find(tM),s=e.output;void 0!==a&&void 0!==n&&(s=a.tapOutputIterable(n.runId,s));let c=n?.handlers.find(tk);for await(let e of(void 0!==c&&void 0!==n&&(s=c.tapOutputIterable(n.runId,s)),s))if(yield e,o)if(void 0===i)i=e;else try{i=tI(i,e)}catch{i=void 0,o=!1}}catch(e){throw await n?.handleChainError(e,void 0,void 0,void 0,{inputs:t9(a,"input")}),e}await n?.handleChainEnd(i??{},void 0,void 0,void 0,{inputs:t9(a,"input")})}getGraph(e){let t=new tX,r=t.addNode({name:`${this.getName()}Input`,schema:g.z.any()}),a=t.addNode(this),i=t.addNode({name:`${this.getName()}Output`,schema:g.z.any()});return t.addEdge(r,a),t.addEdge(a,i),t}pipe(e){return new re({first:this,last:rn(e)})}pick(e){return this.pipe(new ro(e))}assign(e){return this.pipe(new rs(new rt({steps:e})))}async *transform(e,t){let r;for await(let t of e)r=void 0===r?t:tI(r,t);yield*this._streamIterator(r,tF(t))}async *streamLog(e,t,r){let a=new t$({...r,autoClose:!1,_schemaFormat:"original"}),i=tF(t);yield*this._streamLog(e,a,i)}async *_streamLog(e,t,r){let{callbacks:a}=r;if(void 0===a)r.callbacks=[t];else if(Array.isArray(a))r.callbacks=a.concat([t]);else{let e=a.copy();e.addHandler(t,!0),r.callbacks=e}let i=this.stream(e,r),n=async function(){try{for await(let e of(await i)){let r=new tT({ops:[{op:"add",path:"/streamed_output/-",value:e}]});await t.writer.write(r)}}finally{await t.writer.close()}}();try{for await(let e of t)yield e}finally{await n}}streamEvents(e,t,r){let a;if("v1"===t.version)a=this._streamEventsV1(e,t,r);else if("v2"===t.version)a=this._streamEventsV2(e,t,r);else throw Error('Only versions "v1" and "v2" of the schema are currently supported.');if("text/event-stream"!==t.encoding)return tE.fromAsyncGenerator(a);var i=a;let n=new TextEncoder,s=new ReadableStream({async start(e){for await(let t of i)e.enqueue(n.encode(`event: data
data: ${JSON.stringify(t)}

`));e.enqueue(n.encode("event: end\n\n")),e.close()}});return tE.fromReadableStream(s)}async *_streamEventsV2(e,t,r){let a,i=new tL({...r,autoClose:!1}),n=tF(t),s=n.runId??(0,w.A)();n.runId=s;let o=n.callbacks;if(void 0===o)n.callbacks=[i];else if(Array.isArray(o))n.callbacks=o.concat(i);else{let e=o.copy();e.addHandler(i,!0),n.callbacks=e}let l=this,u=async function(){try{let t=await l.stream(e,n);for await(let e of i.tapOutputIterable(s,t));}finally{await i.finish()}}(),d=!1;try{for await(let t of i){if(!d){t.data.input=e,d=!0,a=t.run_id,yield t;continue}t.run_id===a&&t.event.endsWith("_end")&&t.data?.input&&delete t.data.input,yield t}}finally{await u}}async *_streamEventsV1(e,t,r){let a,i=!1,n=tF(t),s=n.tags??[],o=n.metadata??{},l=n.runName??this.getName(),u=new t$({...r,autoClose:!1,_schemaFormat:"streaming_events"}),d=new tW({...r});for await(let t of this._streamLog(e,u,n)){if(void 0===(a=a?a.concat(t):tA.fromRunLogPatch(t)).state)throw Error('Internal error: "streamEvents" state is missing. Please open a bug report.');if(!i){i=!0;let t={...a.state},r={run_id:t.id,event:`on_${t.type}_start`,name:l,tags:s,metadata:o,data:{input:e}};d.includeEvent(r,t.type)&&(yield r)}for(let e of[...new Set(t.ops.filter(e=>e.path.startsWith("/logs/")).map(e=>e.path.split("/")[2]))]){let t,r={},i=a.state.logs[e];if("start"==(t=void 0===i.end_time?i.streamed_output.length>0?"stream":"start":"end"))void 0!==i.inputs&&(r.input=i.inputs);else if("end"===t)void 0!==i.inputs&&(r.input=i.inputs),r.output=i.final_output;else if("stream"===t){let e=i.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${i.name}"`);r={chunk:i.streamed_output[0]},i.streamed_output=[]}yield{event:`on_${i.type}_${t}`,name:i.name,run_id:i.id,tags:i.tags,metadata:i.metadata,data:r}}let{state:r}=a;if(r.streamed_output.length>0){let e=r.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${r.name}"`);let t={chunk:r.streamed_output[0]};r.streamed_output=[];let a={event:`on_${r.type}_stream`,run_id:r.id,tags:s,metadata:o,name:l,data:t};d.includeEvent(a,r.type)&&(yield a)}}let c=a?.state;if(void 0!==c){let e={event:`on_${c.type}_end`,name:l,run_id:c.id,tags:s,metadata:o,data:{output:c.final_output}};d.includeEvent(e,c.type)&&(yield e)}}static isRunnable(e){return tK(e)}withListeners({onStart:e,onEnd:t,onError:r}){return new t6({bound:this,config:{},configFactories:[a=>({callbacks:[new tG({config:a,onStart:e,onEnd:t,onError:r})]})]})}asTool(e){var t=this,r=e;let a=r.name??t.getName(),i=r.description??r.schema?.description;return new rl(r.schema.constructor===g.z.ZodString?{name:a,description:i,schema:g.z.object({input:g.z.string()}).transform(e=>e.input),bound:t}:{name:a,description:i,schema:r.schema,bound:t})}}class t6 extends t5{static lc_name(){return"RunnableBinding"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"configFactories",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound,this.kwargs=e.kwargs,this.config=e.config,this.configFactories=e.configFactories}getName(e){return this.bound.getName(e)}async _mergeConfig(...e){let t=tD(this.config,...e);return tD(t,...this.configFactories?await Promise.all(this.configFactories.map(async e=>await e(t))):[])}bind(e){return new this.constructor({bound:this.bound,kwargs:{...this.kwargs,...e},config:this.config})}withConfig(e){return new this.constructor({bound:this.bound,kwargs:this.kwargs,config:{...this.config,...e}})}withRetry(e){return new this.constructor({bound:this.bound.withRetry(e),kwargs:this.kwargs,config:this.config})}async invoke(e,t){return this.bound.invoke(e,await this._mergeConfig(tF(t),this.kwargs))}async batch(e,t,r){let a=Array.isArray(t)?await Promise.all(t.map(async e=>this._mergeConfig(tF(e),this.kwargs))):await this._mergeConfig(tF(t),this.kwargs);return this.bound.batch(e,a,r)}async *_streamIterator(e,t){yield*this.bound._streamIterator(e,await this._mergeConfig(tF(t),this.kwargs))}async stream(e,t){return this.bound.stream(e,await this._mergeConfig(tF(t),this.kwargs))}async *transform(e,t){yield*this.bound.transform(e,await this._mergeConfig(tF(t),this.kwargs))}streamEvents(e,t,r){let a=this,i=async function*(){yield*a.bound.streamEvents(e,{...await a._mergeConfig(tF(t),a.kwargs),version:t.version},r)};return tE.fromAsyncGenerator(i())}static isRunnableBinding(e){return e.bound&&t5.isRunnable(e.bound)}withListeners({onStart:e,onEnd:t,onError:r}){return new t6({bound:this.bound,kwargs:this.kwargs,config:this.config,configFactories:[a=>({callbacks:[new tG({config:a,onStart:e,onEnd:t,onError:r})]})]})}}class t8 extends t5{static lc_name(){return"RunnableEach"}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound}bind(e){return new t8({bound:this.bound.bind(e)})}async invoke(e,t){return this._callWithConfig(this._invoke,e,t)}async _invoke(e,t,r){return this.bound.batch(e,tB(t,{callbacks:r?.getChild()}))}withListeners({onStart:e,onEnd:t,onError:r}){return new t8({bound:this.bound.withListeners({onStart:e,onEnd:t,onError:r})})}}class t7 extends t6{static lc_name(){return"RunnableRetry"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"maxAttemptNumber",{enumerable:!0,configurable:!0,writable:!0,value:3}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:()=>{}}),this.maxAttemptNumber=e.maxAttemptNumber??this.maxAttemptNumber,this.onFailedAttempt=e.onFailedAttempt??this.onFailedAttempt}_patchConfigForRetry(e,t,r){let a=e>1?`retry:attempt:${e}`:void 0;return tB(t,{callbacks:r?.getChild(a)})}async _invoke(e,t,r){return b(a=>super.invoke(e,this._patchConfigForRetry(a,t,r)),{onFailedAttempt:t=>this.onFailedAttempt(t,e),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}async invoke(e,t){return this._callWithConfig(this._invoke,e,t)}async _batch(e,t,r,a){let i={};try{await b(async n=>{let s,o=e.map((e,t)=>t).filter(e=>void 0===i[e.toString()]||i[e.toString()]instanceof Error),l=o.map(t=>e[t]),u=o.map(e=>this._patchConfigForRetry(n,t?.[e],r?.[e])),d=await super.batch(l,u,{...a,returnExceptions:!0});for(let e=0;e<d.length;e+=1){let t=d[e],r=o[e];t instanceof Error&&void 0===s&&((s=t).input=l[e]),i[r.toString()]=t}if(s)throw s;return d},{onFailedAttempt:e=>this.onFailedAttempt(e,e.input),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}catch(e){if(a?.returnExceptions!==!0)throw e}return Object.keys(i).sort((e,t)=>parseInt(e,10)-parseInt(t,10)).map(e=>i[parseInt(e,10)])}async batch(e,t,r){return this._batchWithConfig(this._batch.bind(this),e,t,r)}}class re extends t5{static lc_name(){return"RunnableSequence"}constructor(e){super(e),Object.defineProperty(this,"first",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"middle",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"last",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),this.first=e.first,this.middle=e.middle??this.middle,this.last=e.last,this.name=e.name}get steps(){return[this.first,...this.middle,this.last]}async invoke(e,t){let r,a=tF(t),i=await tU(a),n=await i?.handleChainStart(this.toJSON(),t9(e,"input"),a.runId,void 0,void 0,void 0,a?.runName);delete a.runId;let s=e;try{let e=[this.first,...this.middle];for(let r=0;r<e.length;r+=1){let i=e[r].invoke(s,tB(a,{callbacks:n?.getChild(`seq:step:${r+1}`)}));s=await tO(i,t?.signal)}if(t?.signal?.aborted)throw Error("Aborted");r=await this.last.invoke(s,tB(a,{callbacks:n?.getChild(`seq:step:${this.steps.length}`)}))}catch(e){throw await n?.handleChainError(e),e}return await n?.handleChainEnd(t9(r,"output")),r}async batch(e,t,r){let a=this._getOptionsList(t??{},e.length),i=await Promise.all(a.map(tU)),n=await Promise.all(i.map(async(t,r)=>{let i=await t?.handleChainStart(this.toJSON(),t9(e[r],"input"),a[r].runId,void 0,void 0,void 0,a[r].runName);return delete a[r].runId,i})),s=e;try{for(let e=0;e<this.steps.length;e+=1){let t=this.steps[e].batch(s,n.map((t,r)=>{let i=t?.getChild(`seq:step:${e+1}`);return tB(a[r],{callbacks:i})}),r);s=await tO(t,a[0]?.signal)}}catch(e){throw await Promise.all(n.map(t=>t?.handleChainError(e))),e}return await Promise.all(n.map(e=>e?.handleChainEnd(t9(s,"output")))),s}async *_streamIterator(e,t){let r,a=await tU(t),{runId:i,...n}=t??{},s=await a?.handleChainStart(this.toJSON(),t9(e,"input"),i,void 0,void 0,void 0,n?.runName),o=[this.first,...this.middle,this.last],l=!0;async function*u(){yield e}try{let e=o[0].transform(u(),tB(n,{callbacks:s?.getChild("seq:step:1")}));for(let t=1;t<o.length;t+=1){let r=o[t];e=await r.transform(e,tB(n,{callbacks:s?.getChild(`seq:step:${t+1}`)}))}for await(let a of e)if(t?.signal?.throwIfAborted(),yield a,l)if(void 0===r)r=a;else try{r=tI(r,a)}catch(e){r=void 0,l=!1}}catch(e){throw await s?.handleChainError(e),e}await s?.handleChainEnd(t9(r,"output"))}getGraph(e){let t=new tX,r=null;return this.steps.forEach((a,i)=>{let n=a.getGraph(e);0!==i&&n.trimFirstNode(),i!==this.steps.length-1&&n.trimLastNode(),t.extend(n);let s=n.firstNode();if(!s)throw Error(`Runnable ${a} has no first node`);r&&t.addEdge(r,s),r=n.lastNode()}),t}pipe(e){return new re(re.isRunnableSequence(e)?{first:this.first,middle:this.middle.concat([this.last,e.first,...e.middle]),last:e.last,name:this.name??e.name}:{first:this.first,middle:[...this.middle,this.last],last:rn(e),name:this.name})}static isRunnableSequence(e){return Array.isArray(e.middle)&&t5.isRunnable(e)}static from([e,...t],r){return new re({first:rn(e),middle:t.slice(0,-1).map(rn),last:rn(t[t.length-1]),name:r})}}class rt extends t5{static lc_name(){return"RunnableMap"}getStepsKeys(){return Object.keys(this.steps)}constructor(e){for(let[t,r]of(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"steps",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.steps={},Object.entries(e.steps)))this.steps[t]=rn(r)}static from(e){return new rt({steps:e})}async invoke(e,t){let r=tF(t),a=await tU(r),i=await a?.handleChainStart(this.toJSON(),{input:e},r.runId,void 0,void 0,void 0,r?.runName);delete r.runId;let n={};try{let a=Object.entries(this.steps).map(async([t,a])=>{n[t]=await a.invoke(e,tB(r,{callbacks:i?.getChild(`map:key:${t}`)}))});await tO(Promise.all(a),t?.signal)}catch(e){throw await i?.handleChainError(e),e}return await i?.handleChainEnd(n),n}async *_transform(e,t,r){let a={...this.steps},i=tS(e,Object.keys(a).length),n=new Map(Object.entries(a).map(([e,a],n)=>{let s=a.transform(i[n],tB(r,{callbacks:t?.getChild(`map:key:${e}`)}));return[e,s.next().then(t=>({key:e,gen:s,result:t}))]}));for(;n.size;){let e=Promise.race(n.values()),{key:t,result:a,gen:i}=await tO(e,r?.signal);n.delete(t),a.done||(yield{[t]:a.value},n.set(t,i.next().then(e=>({key:t,gen:i,result:e}))))}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=tF(t),i=new tj({generator:this.transform(r(),a),config:a});return await i.setup,tE.fromAsyncGenerator(i)}}class rr extends t5{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),!ew(e.func))throw Error("RunnableTraceable requires a function that is wrapped in traceable higher-order function");this.func=e.func}async invoke(e,t){let[r]=this._getOptionsList(t??{},1),a=await tU(r);return tO(this.func(tB(r,{callbacks:a}),e),r?.signal)}async *_streamIterator(e,t){let[r]=this._getOptionsList(t??{},1),a=await this.invoke(e,t);if(t4(a)){for await(let e of a)r?.signal?.throwIfAborted(),yield e;return}if(t1(a)){for(;;){r?.signal?.throwIfAborted();let e=a.next();if(e.done)break;yield e.value}return}yield a}static from(e){return new rr({func:e})}}class ra extends t5{static lc_name(){return"RunnableLambda"}constructor(e){if(ew(e.func))return rr.from(e.func);if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),ew(e.func))throw Error("RunnableLambda requires a function that is not wrapped in traceable higher-order function. This shouldn't happen.");this.func=e.func}static from(e){return new ra({func:e})}async _invoke(e,t,r){return new Promise((a,i)=>{let n=tB(t,{callbacks:r?.getChild(),recursionLimit:(t?.recursionLimit??25)-1});tv.runWithConfig(n,async()=>{try{let r=await this.func(e,{...n,config:n});if(r&&t5.isRunnable(r)){if(t?.recursionLimit===0)throw Error("Recursion limit reached.");r=await r.invoke(e,{...n,recursionLimit:(n.recursionLimit??25)-1})}else if(t4(r)){let e;for await(let a of t2(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=a;else try{e=tI(e,a)}catch(t){e=a}r=e}else if(t0(r)){let e;for(let a of t3(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=a;else try{e=tI(e,a)}catch(t){e=a}r=e}a(r)}catch(e){i(e)}})})}async invoke(e,t){return this._callWithConfig(this._invoke,e,t)}async *_transform(e,t,r){let a;for await(let t of e)if(void 0===a)a=t;else try{a=tI(a,t)}catch(e){a=t}let i=tB(r,{callbacks:t?.getChild(),recursionLimit:(r?.recursionLimit??25)-1}),n=await new Promise((e,t)=>{tv.runWithConfig(i,async()=>{try{let t=await this.func(a,{...i,config:i});e(t)}catch(e){t(e)}})});if(n&&t5.isRunnable(n)){if(r?.recursionLimit===0)throw Error("Recursion limit reached.");for await(let e of(await n.stream(a,i)))yield e}else if(t4(n))for await(let e of t2(i,n))r?.signal?.throwIfAborted(),yield e;else if(t0(n))for(let e of t3(i,n))r?.signal?.throwIfAborted(),yield e;else yield n}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=tF(t),i=new tj({generator:this.transform(r(),a),config:a});return await i.setup,tE.fromAsyncGenerator(i)}}class ri extends t5{static lc_name(){return"RunnableWithFallbacks"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"runnable",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fallbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnable=e.runnable,this.fallbacks=e.fallbacks}*runnables(){for(let e of(yield this.runnable,this.fallbacks))yield e}async invoke(e,t){let r,a=tF(t),i=await tU(t),{runId:n,...s}=a,o=await i?.handleChainStart(this.toJSON(),t9(e,"input"),n,void 0,void 0,void 0,s?.runName);for(let t of this.runnables()){a?.signal?.throwIfAborted();try{let r=await t.invoke(e,tB(s,{callbacks:o?.getChild()}));return await o?.handleChainEnd(t9(r,"output")),r}catch(e){void 0===r&&(r=e)}}if(void 0===r)throw Error("No error stored at end of fallback.");throw await o?.handleChainError(r),r}async *_streamIterator(e,t){let r,a,i,n=tF(t),s=await tU(t),{runId:o,...l}=n,u=await s?.handleChainStart(this.toJSON(),t9(e,"input"),o,void 0,void 0,void 0,l?.runName);for(let t of this.runnables()){n?.signal?.throwIfAborted();let i=tB(l,{callbacks:u?.getChild()});try{a=await t.stream(e,i);break}catch(e){void 0===r&&(r=e)}}if(void 0===a){let e=r??Error("No error stored at end of fallback.");throw await u?.handleChainError(e),e}try{for await(let e of a){yield e;try{i=void 0===i?i:tI(i,e)}catch(e){i=void 0}}}catch(e){throw await u?.handleChainError(e),e}await u?.handleChainEnd(t9(i,"output"))}async batch(e,t,r){let a;if(r?.returnExceptions)throw Error("Not implemented.");let i=this._getOptionsList(t??{},e.length),n=await Promise.all(i.map(e=>tU(e))),s=await Promise.all(n.map(async(t,r)=>{let a=await t?.handleChainStart(this.toJSON(),t9(e[r],"input"),i[r].runId,void 0,void 0,void 0,i[r].runName);return delete i[r].runId,a}));for(let t of this.runnables()){i[0].signal?.throwIfAborted();try{let a=await t.batch(e,s.map((e,t)=>tB(i[t],{callbacks:e?.getChild()})),r);return await Promise.all(s.map((e,t)=>e?.handleChainEnd(t9(a[t],"output")))),a}catch(e){void 0===a&&(a=e)}}if(!a)throw Error("No error stored at end of fallbacks.");throw await Promise.all(s.map(e=>e?.handleChainError(a))),a}}function rn(e){if("function"==typeof e)return new ra({func:e});if(t5.isRunnable(e))return e;if(Array.isArray(e)||"object"!=typeof e)throw Error(`Expected a Runnable, function or object.
Instead got an unsupported type.`);{let t={};for(let[r,a]of Object.entries(e))t[r]=rn(a);return new rt({steps:t})}}class rs extends t5{static lc_name(){return"RunnableAssign"}constructor(e){e instanceof rt&&(e={mapper:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.mapper=e.mapper}async invoke(e,t){let r=await this.mapper.invoke(e,t);return{...e,...r}}async *_transform(e,t,r){let a=this.mapper.getStepsKeys(),[i,n]=tS(e),s=this.mapper.transform(n,tB(r,{callbacks:t?.getChild()})),o=s.next();for await(let e of i){if("object"!=typeof e||Array.isArray(e))throw Error(`RunnableAssign can only be used with objects as input, got ${typeof e}`);let t=Object.fromEntries(Object.entries(e).filter(([e])=>!a.includes(e)));Object.keys(t).length>0&&(yield t)}for await(let e of(yield(await o).value,s))yield e}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=tF(t),i=new tj({generator:this.transform(r(),a),config:a});return await i.setup,tE.fromAsyncGenerator(i)}}class ro extends t5{static lc_name(){return"RunnablePick"}constructor(e){("string"==typeof e||Array.isArray(e))&&(e={keys:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"keys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.keys=e.keys}async _pick(e){if("string"==typeof this.keys)return e[this.keys];{let t=this.keys.map(t=>[t,e[t]]).filter(e=>void 0!==e[1]);return 0===t.length?void 0:Object.fromEntries(t)}}async invoke(e,t){return this._callWithConfig(this._pick.bind(this),e,t)}async *_transform(e){for await(let t of e){let e=await this._pick(t);void 0!==e&&(yield e)}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=tF(t),i=new tj({generator:this.transform(r(),a),config:a});return await i.setup,tE.fromAsyncGenerator(i)}}class rl extends t6{constructor(e){super({bound:re.from([ra.from(async e=>{let t;if(function(e){return!!(e&&"object"==typeof e&&"type"in e&&"tool_call"===e.type)}(e))try{t=await this.schema.parseAsync(e.args)}catch(t){throw new e8("Received tool input did not match expected schema",JSON.stringify(e.args))}else t=e;return t}).withConfig({runName:`${e.name}:parse_input`}),e.bound]).withConfig({runName:e.name}),config:e.config??{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.schema=e.schema}static lc_name(){return"RunnableToolLike"}}class ru extends t5{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","documents","transformers"]})}invoke(e,t){return this.transformDocuments(e)}}var rd=r(85780);let rc={},rh=new tq({});class rp extends ru{constructor(e){if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","document_transformers","text_splitters"]}),Object.defineProperty(this,"chunkSize",{enumerable:!0,configurable:!0,writable:!0,value:1e3}),Object.defineProperty(this,"chunkOverlap",{enumerable:!0,configurable:!0,writable:!0,value:200}),Object.defineProperty(this,"keepSeparator",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lengthFunction",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.chunkSize=e?.chunkSize??this.chunkSize,this.chunkOverlap=e?.chunkOverlap??this.chunkOverlap,this.keepSeparator=e?.keepSeparator??this.keepSeparator,this.lengthFunction=e?.lengthFunction??(e=>e.length),this.chunkOverlap>=this.chunkSize)throw Error("Cannot have chunkOverlap >= chunkSize")}async transformDocuments(e,t={}){return this.splitDocuments(e,t)}splitOnSeparator(e,t){let r;if(t)if(this.keepSeparator){let a=t.replace(/[/\-\\^$*+?.()|[\]{}]/g,"\\$&");r=e.split(RegExp(`(?=${a})`))}else r=e.split(t);else r=e.split("");return r.filter(e=>""!==e)}async createDocuments(e,t=[],r={}){let a=t.length>0?t:[...Array(e.length)].map(()=>({})),{chunkHeader:i="",chunkOverlapHeader:n="(cont'd) ",appendChunkOverlapHeader:s=!1}=r,o=[];for(let t=0;t<e.length;t+=1){let r=e[t],l=1,u=null,d=-1;for(let e of(await this.splitText(r))){let c=i,h=r.indexOf(e,d+1);if(null===u)l+=this.numberOfNewLines(r,0,h);else{let e=d+await this.lengthFunction(u);e<h?l+=this.numberOfNewLines(r,e,h):e>h&&(l-=this.numberOfNewLines(r,h,e)),s&&(c+=n)}let p=this.numberOfNewLines(e),m=a[t].loc&&"object"==typeof a[t].loc?{...a[t].loc}:{};m.lines={from:l,to:l+p};let g={...a[t],loc:m};c+=e,o.push(new f({pageContent:c,metadata:g})),l+=p,u=e,d=h}}return o}numberOfNewLines(e,t,r){return(e.slice(t,r).match(/\n/g)||[]).length}async splitDocuments(e,t={}){let r=e.filter(e=>void 0!==e.pageContent),a=r.map(e=>e.pageContent),i=r.map(e=>e.metadata);return this.createDocuments(a,i,t)}joinDocs(e,t){let r=e.join(t).trim();return""===r?null:r}async mergeSplits(e,t){let r=[],a=[],i=0;for(let n of e){let e=await this.lengthFunction(n);if(i+e+a.length*t.length>this.chunkSize&&(i>this.chunkSize&&console.warn(`Created a chunk of size ${i}, +
which is longer than the specified ${this.chunkSize}`),a.length>0)){let n=this.joinDocs(a,t);for(null!==n&&r.push(n);i>this.chunkOverlap||i+e+a.length*t.length>this.chunkSize&&i>0;)i-=await this.lengthFunction(a[0]),a.shift()}a.push(n),i+=e}let n=this.joinDocs(a,t);return null!==n&&r.push(n),r}}class rm extends rp{static lc_name(){return"RecursiveCharacterTextSplitter"}constructor(e){super(e),Object.defineProperty(this,"separators",{enumerable:!0,configurable:!0,writable:!0,value:["\n\n","\n"," ",""]}),this.separators=e?.separators??this.separators,this.keepSeparator=e?.keepSeparator??!0}async _splitText(e,t){let r,a=[],i=t[t.length-1];for(let a=0;a<t.length;a+=1){let n=t[a];if(""===n){i=n;break}if(e.includes(n)){i=n,r=t.slice(a+1);break}}let n=this.splitOnSeparator(e,i),s=[],o=this.keepSeparator?"":i;for(let e of n)if(await this.lengthFunction(e)<this.chunkSize)s.push(e);else{if(s.length){let e=await this.mergeSplits(s,o);a.push(...e),s=[]}if(r){let t=await this._splitText(e,r);a.push(...t)}else a.push(e)}if(s.length){let e=await this.mergeSplits(s,o);a.push(...e)}return a}async splitText(e){return this._splitText(e,this.separators)}static fromLanguage(e,t){return new rm({...t,separators:rm.getSeparatorsForLanguage(e)})}static getSeparatorsForLanguage(e){if("cpp"===e)return["\nclass ","\nvoid ","\nint ","\nfloat ","\ndouble ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("go"===e)return["\nfunc ","\nvar ","\nconst ","\ntype ","\nif ","\nfor ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("java"===e)return["\nclass ","\npublic ","\nprotected ","\nprivate ","\nstatic ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("js"===e)return["\nfunction ","\nconst ","\nlet ","\nvar ","\nclass ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\ndefault ","\n\n","\n"," ",""];if("php"===e)return["\nfunction ","\nclass ","\nif ","\nforeach ","\nwhile ","\ndo ","\nswitch ","\ncase ","\n\n","\n"," ",""];else if("proto"===e)return["\nmessage ","\nservice ","\nenum ","\noption ","\nimport ","\nsyntax ","\n\n","\n"," ",""];else if("python"===e)return["\nclass ","\ndef ","\n	def ","\n\n","\n"," ",""];else if("rst"===e)return["\n===\n","\n---\n","\n***\n","\n.. ","\n\n","\n"," ",""];else if("ruby"===e)return["\ndef ","\nclass ","\nif ","\nunless ","\nwhile ","\nfor ","\ndo ","\nbegin ","\nrescue ","\n\n","\n"," ",""];else if("rust"===e)return["\nfn ","\nconst ","\nlet ","\nif ","\nwhile ","\nfor ","\nloop ","\nmatch ","\nconst ","\n\n","\n"," ",""];else if("scala"===e)return["\nclass ","\nobject ","\ndef ","\nval ","\nvar ","\nif ","\nfor ","\nwhile ","\nmatch ","\ncase ","\n\n","\n"," ",""];else if("swift"===e)return["\nfunc ","\nclass ","\nstruct ","\nenum ","\nif ","\nfor ","\nwhile ","\ndo ","\nswitch ","\ncase ","\n\n","\n"," ",""];else if("markdown"===e)return["\n## ","\n### ","\n#### ","\n##### ","\n###### ","```\n\n","\n\n***\n\n","\n\n---\n\n","\n\n___\n\n","\n\n","\n"," ",""];else if("latex"===e)return["\n\\chapter{","\n\\section{","\n\\subsection{","\n\\subsubsection{","\n\\begin{enumerate}","\n\\begin{itemize}","\n\\begin{description}","\n\\begin{list}","\n\\begin{quote}","\n\\begin{quotation}","\n\\begin{verse}","\n\\begin{verbatim}","\n\\begin{align}","$$","$","\n\n","\n"," ",""];else if("html"===e)return["<body>","<div>","<p>","<br>","<li>","<h1>","<h2>","<h3>","<h4>","<h5>","<h6>","<span>","<table>","<tr>","<td>","<th>","<ul>","<ol>","<header>","<footer>","<nav>","<head>","<style>","<script>","<meta>","<title>"," ",""];else if("sol"===e)return["\npragma ","\nusing ","\ncontract ","\ninterface ","\nlibrary ","\nconstructor ","\ntype ","\nfunction ","\nevent ","\nmodifier ","\nerror ","\nstruct ","\nenum ","\nif ","\nfor ","\nwhile ","\ndo while ","\nassembly ","\n\n","\n"," ",""];else throw Error(`Language ${e} is not supported.`)}}var rf=r(88108);let rg=require("fs/promises");var rb=r(33873);let rw=require("os"),ry=require("pdf-parse");var r_=r.n(ry);let rv=new rm({chunkSize:1500,chunkOverlap:300,separators:["\n\n","\n",". ","! ","? ","; ",", "," ",""]});async function rO(e){try{let t=await (0,m.createSupabaseServerClientOnRequest)(),{data:{user:a},error:i}=await t.auth.getUser();if(i||!a)return p.NextResponse.json({error:"Unauthorized"},{status:401});let n=await e.formData(),s=n.get("file"),o=n.get("configId");if(!s||!o)return p.NextResponse.json({error:"File and configId are required"},{status:400});if(!["application/pdf","text/plain","text/markdown"].includes(s.type))return p.NextResponse.json({error:"Unsupported file type. Please upload PDF, TXT, or MD files."},{status:400});if(s.size>0xa00000)return p.NextResponse.json({error:"File size too large. Maximum size is 10MB."},{status:400});let l=await s.arrayBuffer(),u=Buffer.from(l),d=(0,rb.join)((0,rw.tmpdir)(),`upload_${Date.now()}_${s.name}`);await (0,rg.writeFile)(d,u);let c="";try{switch(s.type){case"application/pdf":let e=console.warn;console.warn=t=>{"string"==typeof t&&t.includes("Ran out of space in font private use area")||e(t)};try{if((!(c=(await r_()(u,{max:0})).text)||0===c.trim().length)&&(!(c=(await r_()(u,{max:0})).text)||0===c.trim().length))throw Error("No text could be extracted from this PDF. The file may be image-based, password-protected, or corrupted.")}catch(e){throw Error(`Failed to process PDF: ${e.message||"Unknown error"}`)}finally{console.warn=e}break;case"application/vnd.openxmlformats-officedocument.wordprocessingml.document":throw Error("DOCX support temporarily disabled. Please use PDF, TXT, or MD files.");case"text/plain":case"text/markdown":c=await (0,rg.readFile)(d,"utf-8");break;default:throw Error("Unsupported file type")}let i={pageContent:c,metadata:{source:s.name,type:s.type}},n=await rv.splitDocuments([i]),{data:l,error:h}=await t.from("documents").insert({user_id:a.id,custom_api_config_id:o,filename:s.name,file_type:s.type,file_size:s.size,content:c,metadata:{chunks_count:n.length,processing_started_at:new Date().toISOString()},status:"processing"}).select().single();if(h)throw Error("Failed to store document metadata");try{let e=await rf.jinaEmbeddings.embedQuery("test");if(1024!==e.length)throw Error(`Dimension mismatch: Database expects 1024 dimensions but Jina v3 produces ${e.length} dimensions`)}catch(e){throw Error(`Jina embedding generation failed: ${e.message}`)}let m=n.map(async(e,r)=>{try{let i=await rf.jinaEmbeddings.embedQuery(e.pageContent),n={document_id:l.id,user_id:a.id,custom_api_config_id:o,content:e.pageContent,metadata:{...e.metadata,chunk_index:r,chunk_size:e.pageContent.length},embedding:i},{data:s,error:u}=await t.from("document_chunks").insert(n).select().single();if(u)throw u;return{success:!0,index:r,chunkId:s.id}}catch(e){return{success:!1,index:r,error:e.message||e}}}),f=await Promise.all(m),g=f.filter(e=>e.success).length,b=f.filter(e=>!e.success).length,w=f.filter(e=>!e.success),y=0===b?"completed":"failed",{error:_}=await t.from("documents").update({status:y,metadata:{...l.metadata,chunks_processed:g,chunks_failed:b,processing_completed_at:new Date().toISOString(),...b>0&&{failed_chunk_errors:w.slice(0,5)}},updated_at:new Date().toISOString()}).eq("id",l.id);await (0,rg.unlink)(d);try{let{trainingDataCache:e}=await r.e(2842).then(r.bind(r,2842));e.invalidate(o)}catch(e){}return p.NextResponse.json({success:!0,document:{id:l.id,filename:s.name,status:y,chunks_processed:g,chunks_total:n.length}})}catch(e){try{await (0,rg.unlink)(d)}catch(e){}throw e}}catch(e){return p.NextResponse.json({error:"Failed to process document",details:e.message},{status:500})}}let rE=new d.AppRouteRouteModule({definition:{kind:c.RouteKind.APP_ROUTE,page:"/api/documents/upload/route",pathname:"/api/documents/upload",filename:"route",bundlePath:"app/api/documents/upload/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\documents\\upload\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:rS,workUnitAsyncStorage:rI,serverHooks:rj}=rE;function rP(){return(0,h.patchFetch)({workAsyncStorage:rS,workUnitAsyncStorage:rI})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73085:(e,t,r)=>{"use strict";e=r.nmd(e);let a=(e=0)=>t=>`\u001B[${38+e};5;${t}m`,i=(e=0)=>(t,r,a)=>`\u001B[${38+e};2;${t};${r};${a}m`;Object.defineProperty(e,"exports",{enumerable:!0,get:function(){let e=new Map,t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};for(let[r,a]of(t.color.gray=t.color.blackBright,t.bgColor.bgGray=t.bgColor.bgBlackBright,t.color.grey=t.color.blackBright,t.bgColor.bgGrey=t.bgColor.bgBlackBright,Object.entries(t))){for(let[r,i]of Object.entries(a))t[r]={open:`\u001B[${i[0]}m`,close:`\u001B[${i[1]}m`},a[r]=t[r],e.set(i[0],i[1]);Object.defineProperty(t,r,{value:a,enumerable:!1})}return Object.defineProperty(t,"codes",{value:e,enumerable:!1}),t.color.close="\x1b[39m",t.bgColor.close="\x1b[49m",t.color.ansi256=a(),t.color.ansi16m=i(),t.bgColor.ansi256=a(10),t.bgColor.ansi16m=i(10),Object.defineProperties(t,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{let t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map(e=>e+e).join(""));let a=Number.parseInt(r,16);return[a>>16&255,a>>8&255,255&a]},enumerable:!1},hexToAnsi256:{value:e=>t.rgbToAnsi256(...t.hexToRgb(e)),enumerable:!1}}),t}})},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88108:(e,t,r)=>{"use strict";r.d(t,{jinaEmbeddings:()=>i});class a{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/embeddings",this.model="jina-embeddings-v3",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date,errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,r=!1){let a=this.keyUsage.get(e);a&&(a.requests++,a.tokens+=t,a.lastUsed=new Date,r&&(a.errors++,a.lastError=new Date))}async embedQuery(e){let t=this.apiKeys.length,r=null;for(let a=0;a<t;a++)try{let t=this.getBestKey(),r=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,input:[e],normalized:!0,embedding_type:"float"})});if(!r.ok){let e=await r.text();if(429===r.status){this.updateKeyUsage(t,0,!0);continue}throw Error(`HTTP ${r.status}: ${e}`)}let a=await r.json();if(!a.data||0===a.data.length)throw Error("No embedding data returned from Jina API");let i=a.data[0].embedding;return this.updateKeyUsage(t,a.usage?.total_tokens||e.length),i}catch(e){if(r=e,a===t-1)break}throw Error(`All Jina API keys failed. Last error: ${r?.message||"Unknown error"}`)}async embedDocuments(e){let t=[];for(let r=0;r<e.length;r++){let a=await this.embedQuery(e[r]);t.push(a),r<e.length-1&&await new Promise(e=>setTimeout(e,100))}return t}getUsageStats(){let e={};return this.apiKeys.forEach((t,r)=>{let a=this.keyUsage.get(t);a&&(e[`key_${r+1}`]={...a})}),e}getTotalCapacity(){return{totalKeys:this.apiKeys.length,estimatedRPM:500*this.apiKeys.length,estimatedTokensPerMonth:1e6*this.apiKeys.length}}}let i=new a},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,9398,3410,5697,4703],()=>r(59490));module.exports=a})();
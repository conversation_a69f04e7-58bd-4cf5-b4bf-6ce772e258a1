"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx":
/*!***************************************************!*\
  !*** ./src/components/DynamicStatusIndicator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DynamicStatusIndicator),\n/* harmony export */   useStatusProgression: () => (/* binding */ useStatusProgression)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CommandLineIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default,useStatusProgression auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst STATUS_CONFIGS = {\n    initializing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        text: 'Initializing',\n        description: 'Starting up systems',\n        bgColor: 'bg-gradient-to-r from-slate-50 to-gray-50',\n        iconColor: 'text-slate-600',\n        borderColor: 'border-slate-200/60',\n        glowColor: 'shadow-slate-200/50',\n        gradientFrom: 'from-slate-400',\n        gradientTo: 'to-gray-400',\n        duration: 200\n    },\n    analyzing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        text: 'Analyzing',\n        description: 'Understanding your request',\n        bgColor: 'bg-gradient-to-r from-cyan-50 to-blue-50',\n        iconColor: 'text-cyan-600',\n        borderColor: 'border-cyan-200/60',\n        glowColor: 'shadow-cyan-200/50',\n        gradientFrom: 'from-cyan-400',\n        gradientTo: 'to-blue-400',\n        duration: 300\n    },\n    routing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        text: 'Smart routing',\n        description: 'Finding optimal path',\n        bgColor: 'bg-gradient-to-r from-indigo-50 to-purple-50',\n        iconColor: 'text-indigo-600',\n        borderColor: 'border-indigo-200/60',\n        glowColor: 'shadow-indigo-200/50',\n        gradientFrom: 'from-indigo-400',\n        gradientTo: 'to-purple-400',\n        duration: 400\n    },\n    complexity_analysis: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        text: 'Analyzing complexity',\n        description: 'Evaluating request depth',\n        bgColor: 'bg-gradient-to-r from-amber-50 to-yellow-50',\n        iconColor: 'text-amber-600',\n        borderColor: 'border-amber-200/60',\n        glowColor: 'shadow-amber-200/50',\n        gradientFrom: 'from-amber-400',\n        gradientTo: 'to-yellow-400',\n        duration: 500\n    },\n    role_classification: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        text: 'Assembling specialists',\n        description: 'Building expert team',\n        bgColor: 'bg-gradient-to-r from-violet-50 to-purple-50',\n        iconColor: 'text-violet-600',\n        borderColor: 'border-violet-200/60',\n        glowColor: 'shadow-violet-200/50',\n        gradientFrom: 'from-violet-400',\n        gradientTo: 'to-purple-400',\n        duration: 600\n    },\n    preparing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        text: 'Preparing',\n        description: 'Setting up processing',\n        bgColor: 'bg-gradient-to-r from-orange-50 to-amber-50',\n        iconColor: 'text-orange-600',\n        borderColor: 'border-orange-200/60',\n        glowColor: 'shadow-orange-200/50',\n        gradientFrom: 'from-orange-400',\n        gradientTo: 'to-amber-400',\n        duration: 300\n    },\n    connecting: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        text: 'Connecting',\n        description: 'Establishing AI link',\n        bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',\n        iconColor: 'text-rose-600',\n        borderColor: 'border-rose-200/60',\n        glowColor: 'shadow-rose-200/50',\n        gradientFrom: 'from-rose-400',\n        gradientTo: 'to-pink-400',\n        duration: 400\n    },\n    generating: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        text: 'Thinking deeply',\n        description: 'AI processing in progress',\n        bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n        iconColor: 'text-emerald-600',\n        borderColor: 'border-emerald-200/60',\n        glowColor: 'shadow-emerald-200/50',\n        gradientFrom: 'from-emerald-400',\n        gradientTo: 'to-teal-400',\n        duration: 800\n    },\n    typing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        text: 'Streaming response',\n        description: 'Delivering your answer',\n        bgColor: 'bg-gradient-to-r from-green-50 to-emerald-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-emerald-400'\n    },\n    finalizing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        text: 'Finalizing',\n        description: 'Adding finishing touches',\n        bgColor: 'bg-gradient-to-r from-teal-50 to-cyan-50',\n        iconColor: 'text-teal-600',\n        borderColor: 'border-teal-200/60',\n        glowColor: 'shadow-teal-200/50',\n        gradientFrom: 'from-teal-400',\n        gradientTo: 'to-cyan-400',\n        duration: 200\n    },\n    complete: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        text: 'Complete',\n        description: 'Response delivered',\n        bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-lime-400',\n        duration: 100\n    }\n};\nfunction DynamicStatusIndicator(param) {\n    let { currentStage, isStreaming = false, className = '', onStageChange, orchestrationStatus } = param;\n    _s();\n    const [displayStage, setDisplayStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(currentStage);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [orchestrationColorIndex, setOrchestrationColorIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastOrchestrationStatus, setLastOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    // Color palette for orchestration progress cycling\n    const orchestrationColors = [\n        {\n            bg: 'bg-gradient-to-r from-blue-50 to-indigo-50',\n            icon: 'text-blue-600',\n            border: 'border-blue-200/60',\n            glow: 'shadow-blue-200/50',\n            gradient: 'from-blue-400 to-indigo-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-purple-50 to-violet-50',\n            icon: 'text-purple-600',\n            border: 'border-purple-200/60',\n            glow: 'shadow-purple-200/50',\n            gradient: 'from-purple-400 to-violet-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-indigo-50 to-blue-50',\n            icon: 'text-indigo-600',\n            border: 'border-indigo-200/60',\n            glow: 'shadow-indigo-200/50',\n            gradient: 'from-indigo-400 to-blue-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-cyan-50 to-teal-50',\n            icon: 'text-cyan-600',\n            border: 'border-cyan-200/60',\n            glow: 'shadow-cyan-200/50',\n            gradient: 'from-cyan-400 to-teal-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-teal-50 to-emerald-50',\n            icon: 'text-teal-600',\n            border: 'border-teal-200/60',\n            glow: 'shadow-teal-200/50',\n            gradient: 'from-teal-400 to-emerald-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-green-50 to-lime-50',\n            icon: 'text-green-600',\n            border: 'border-green-200/60',\n            glow: 'shadow-green-200/50',\n            gradient: 'from-green-400 to-lime-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-yellow-50 to-amber-50',\n            icon: 'text-yellow-600',\n            border: 'border-yellow-200/60',\n            glow: 'shadow-yellow-200/50',\n            gradient: 'from-yellow-400 to-amber-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-orange-50 to-red-50',\n            icon: 'text-orange-600',\n            border: 'border-orange-200/60',\n            glow: 'shadow-orange-200/50',\n            gradient: 'from-orange-400 to-red-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-rose-50 to-pink-50',\n            icon: 'text-rose-600',\n            border: 'border-rose-200/60',\n            glow: 'shadow-rose-200/50',\n            gradient: 'from-rose-400 to-pink-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n            icon: 'text-emerald-600',\n            border: 'border-emerald-200/60',\n            glow: 'shadow-emerald-200/50',\n            gradient: 'from-emerald-400 to-teal-400'\n        }\n    ];\n    // Use orchestration colors if in orchestration mode, otherwise use default config\n    const isOrchestrationMode = !!orchestrationStatus;\n    const currentOrchestrationColor = orchestrationColors[orchestrationColorIndex % orchestrationColors.length];\n    const config = isOrchestrationMode ? {\n        ...STATUS_CONFIGS[displayStage],\n        ...currentOrchestrationColor\n    } : STATUS_CONFIGS[displayStage];\n    const Icon = config.icon;\n    // Handle stage transitions with speed-up animation\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (currentStage !== displayStage) {\n                console.log(\"\\uD83C\\uDFAF DynamicStatusIndicator: \".concat(displayStage, \" → \").concat(currentStage));\n                setIsTransitioning(true);\n                // Speed up animation during transition\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setDisplayStage(currentStage);\n                        setIsTransitioning(false);\n                        onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange(currentStage);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 200); // Brief transition period\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        currentStage,\n        displayStage,\n        onStageChange\n    ]);\n    // Handle orchestration status changes with color cycling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (orchestrationStatus && orchestrationStatus !== lastOrchestrationStatus) {\n                console.log(\"\\uD83C\\uDFA8 Orchestration status changed: \".concat(orchestrationStatus));\n                setLastOrchestrationStatus(orchestrationStatus);\n                setOrchestrationColorIndex({\n                    \"DynamicStatusIndicator.useEffect\": (prev)=>prev + 1\n                }[\"DynamicStatusIndicator.useEffect\"]);\n                setIsTransitioning(true);\n                // Brief transition animation\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setIsTransitioning(false);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 300);\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        orchestrationStatus,\n        lastOrchestrationStatus\n    ]);\n    // Remove auto-progression - let the hook handle stage management\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex justify-start \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.6s linear infinite' : 'spin 1.2s linear infinite',\n                            borderImage: \"conic-gradient(from 0deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 25%, transparent 50%, \").concat(config.iconColor.replace('text-', ''), \" 75%, transparent 100%) 1\"),\n                            filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.8s linear infinite reverse' : 'spin 1.6s linear infinite reverse',\n                            borderImage: \"conic-gradient(from 180deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 30%, transparent 60%, \").concat(config.iconColor.replace('text-', ''), \" 90%, transparent 100%) 1\"),\n                            opacity: 0.8\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            borderColor: config.iconColor.replace('text-', ''),\n                            opacity: 0.6,\n                            animation: 'pulse 2s ease-in-out infinite'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            boxShadow: \"0 0 12px \".concat(config.iconColor.replace('text-', ''), \"40, 0 0 24px \").concat(config.iconColor.replace('text-', ''), \"20\")\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 \".concat(config.bgColor, \" border-2 \").concat(config.borderColor, \" shadow-lg backdrop-blur-sm\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"w-3.5 h-3.5 transition-all duration-500 \".concat(config.iconColor, \" \").concat(isTransitioning ? 'scale-125 rotate-12' : 'scale-100', \" drop-shadow-lg\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 \".concat(config.bgColor, \" \").concat(config.borderColor, \" border \").concat(config.glowColor, \" shadow-sm backdrop-blur-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex items-center space-x-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"transition-all duration-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"text-xs font-semibold transition-colors duration-500 \".concat(config.iconColor, \" tracking-wide\"),\n                                    children: orchestrationStatus || config.text\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                isStreaming && displayStage === 'typing' && !orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Live\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Orchestrating\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    (displayStage === 'generating' || displayStage === 'typing') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: displayStage === 'typing' ? '100%' : '60%',\n                                    animation: displayStage === 'typing' ? 'progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite' : 'progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite'\n                                },\n                                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-full rounded-full transition-all duration-1000 bg-gradient-to-r \".concat(config.gradientFrom, \" \").concat(config.gradientTo, \" relative overflow-hidden\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        animation: 'progressShine 2s linear infinite',\n                                        transform: 'skewX(-20deg)'\n                                    },\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"f56d70faa8a01b64\",\n                children: \"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(DynamicStatusIndicator, \"1p3xKMLQ9O9Eig1VCjGbm6b6hME=\");\n_c = DynamicStatusIndicator;\n// Hook for managing status progression\nfunction useStatusProgression() {\n    _s1();\n    const [currentStage, setCurrentStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('initializing');\n    const [stageHistory, setStageHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        'initializing'\n    ]);\n    const updateStage = (stage)=>{\n        setCurrentStage(stage);\n        setStageHistory((prev)=>[\n                ...prev,\n                stage\n            ]);\n    };\n    const reset = ()=>{\n        setCurrentStage('initializing');\n        setStageHistory([\n            'initializing'\n        ]);\n    };\n    return {\n        currentStage,\n        stageHistory,\n        updateStage,\n        reset\n    };\n}\n_s1(useStatusProgression, \"7mqPvLO7RfuzcLNJcZduGbqc5yY=\");\nvar _c;\n$RefreshReg$(_c, \"DynamicStatusIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0R5bmFtaWNTdGF0dXNJbmRpY2F0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQWtCZDtBQTRCckMsTUFBTWMsaUJBQXdEO0lBQzVEQyxjQUFjO1FBQ1pDLE1BQU1QLDZPQUFnQkE7UUFDdEJRLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGNBQWM7UUFDZEMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQUMsV0FBVztRQUNUVixNQUFNViw2T0FBbUJBO1FBQ3pCVyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0FFLFNBQVM7UUFDUFgsTUFBTVQsNk9BQWFBO1FBQ25CVSxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0FHLHFCQUFxQjtRQUNuQlosTUFBTVIsNk9BQWFBO1FBQ25CUyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0FJLHFCQUFxQjtRQUNuQmIsTUFBTUosNk9BQVFBO1FBQ2RLLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGNBQWM7UUFDZEMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQUssV0FBVztRQUNUZCxNQUFNYiw2T0FBUUE7UUFDZGMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsY0FBYztRQUNkQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBTSxZQUFZO1FBQ1ZmLE1BQU1MLDZPQUFlQTtRQUNyQk0sTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsY0FBYztRQUNkQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBTyxZQUFZO1FBQ1ZoQixNQUFNTiw4T0FBUUE7UUFDZE8sTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsY0FBYztRQUNkQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBUSxRQUFRO1FBQ05qQixNQUFNWiw4T0FBVUE7UUFDaEJhLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGNBQWM7UUFDZEMsWUFBWTtJQUNkO0lBQ0FVLFlBQVk7UUFDVmxCLE1BQU1YLDhPQUFZQTtRQUNsQlksTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsY0FBYztRQUNkQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBVSxVQUFVO1FBQ1JuQixNQUFNSCw4T0FBZUE7UUFDckJJLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGNBQWM7UUFDZEMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7QUFDRjtBQVVlLFNBQVNXLHVCQUF1QixLQU1qQjtRQU5pQixFQUM3Q0MsWUFBWSxFQUNaQyxjQUFjLEtBQUssRUFDbkJDLFlBQVksRUFBRSxFQUNkQyxhQUFhLEVBQ2JDLG1CQUFtQixFQUNTLEdBTmlCOztJQU83QyxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHMUMsK0NBQVFBLENBQWtCb0M7SUFDbEUsTUFBTSxDQUFDTyxpQkFBaUJDLG1CQUFtQixHQUFHNUMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDNkMseUJBQXlCQywyQkFBMkIsR0FBRzlDLCtDQUFRQSxDQUFDO0lBQ3ZFLE1BQU0sQ0FBQytDLHlCQUF5QkMsMkJBQTJCLEdBQUdoRCwrQ0FBUUEsQ0FBQztJQUV2RSxtREFBbUQ7SUFDbkQsTUFBTWlELHNCQUFzQjtRQUMxQjtZQUFFQyxJQUFJO1lBQThDbkMsTUFBTTtZQUFpQm9DLFFBQVE7WUFBc0JDLE1BQU07WUFBc0JDLFVBQVU7UUFBOEI7UUFDN0s7WUFBRUgsSUFBSTtZQUFnRG5DLE1BQU07WUFBbUJvQyxRQUFRO1lBQXdCQyxNQUFNO1lBQXdCQyxVQUFVO1FBQWdDO1FBQ3ZMO1lBQUVILElBQUk7WUFBOENuQyxNQUFNO1lBQW1Cb0MsUUFBUTtZQUF3QkMsTUFBTTtZQUF3QkMsVUFBVTtRQUE4QjtRQUNuTDtZQUFFSCxJQUFJO1lBQTRDbkMsTUFBTTtZQUFpQm9DLFFBQVE7WUFBc0JDLE1BQU07WUFBc0JDLFVBQVU7UUFBNEI7UUFDeks7WUFBRUgsSUFBSTtZQUErQ25DLE1BQU07WUFBaUJvQyxRQUFRO1lBQXNCQyxNQUFNO1lBQXNCQyxVQUFVO1FBQStCO1FBQy9LO1lBQUVILElBQUk7WUFBNkNuQyxNQUFNO1lBQWtCb0MsUUFBUTtZQUF1QkMsTUFBTTtZQUF1QkMsVUFBVTtRQUE2QjtRQUM5SztZQUFFSCxJQUFJO1lBQStDbkMsTUFBTTtZQUFtQm9DLFFBQVE7WUFBd0JDLE1BQU07WUFBd0JDLFVBQVU7UUFBK0I7UUFDckw7WUFBRUgsSUFBSTtZQUE2Q25DLE1BQU07WUFBbUJvQyxRQUFRO1lBQXdCQyxNQUFNO1lBQXdCQyxVQUFVO1FBQTZCO1FBQ2pMO1lBQUVILElBQUk7WUFBNENuQyxNQUFNO1lBQWlCb0MsUUFBUTtZQUFzQkMsTUFBTTtZQUFzQkMsVUFBVTtRQUE0QjtRQUN6SztZQUFFSCxJQUFJO1lBQStDbkMsTUFBTTtZQUFvQm9DLFFBQVE7WUFBeUJDLE1BQU07WUFBeUJDLFVBQVU7UUFBK0I7S0FDekw7SUFFRCxrRkFBa0Y7SUFDbEYsTUFBTUMsc0JBQXNCLENBQUMsQ0FBQ2Q7SUFDOUIsTUFBTWUsNEJBQTRCTixtQkFBbUIsQ0FBQ0osMEJBQTBCSSxvQkFBb0JPLE1BQU0sQ0FBQztJQUUzRyxNQUFNQyxTQUFTSCxzQkFBc0I7UUFDbkMsR0FBR3pDLGNBQWMsQ0FBQzRCLGFBQWE7UUFDL0IsR0FBR2MseUJBQXlCO0lBQzlCLElBQUkxQyxjQUFjLENBQUM0QixhQUFhO0lBRWhDLE1BQU1pQixPQUFPRCxPQUFPMUMsSUFBSTtJQUV4QixtREFBbUQ7SUFDbkRkLGdEQUFTQTs0Q0FBQztZQUNSLElBQUltQyxpQkFBaUJLLGNBQWM7Z0JBQ2pDa0IsUUFBUUMsR0FBRyxDQUFDLHdDQUFnRHhCLE9BQWxCSyxjQUFhLE9BQWtCLE9BQWJMO2dCQUM1RFEsbUJBQW1CO2dCQUVuQix1Q0FBdUM7Z0JBQ3ZDaUI7d0RBQVc7d0JBQ1RuQixnQkFBZ0JOO3dCQUNoQlEsbUJBQW1CO3dCQUNuQkwsMEJBQUFBLG9DQUFBQSxjQUFnQkg7b0JBQ2xCO3VEQUFHLE1BQU0sMEJBQTBCO1lBQ3JDO1FBQ0Y7MkNBQUc7UUFBQ0E7UUFBY0s7UUFBY0Y7S0FBYztJQUU5Qyx5REFBeUQ7SUFDekR0QyxnREFBU0E7NENBQUM7WUFDUixJQUFJdUMsdUJBQXVCQSx3QkFBd0JPLHlCQUF5QjtnQkFDMUVZLFFBQVFDLEdBQUcsQ0FBQyw4Q0FBd0QsT0FBcEJwQjtnQkFDaERRLDJCQUEyQlI7Z0JBQzNCTTt3REFBMkJnQixDQUFBQSxPQUFRQSxPQUFPOztnQkFDMUNsQixtQkFBbUI7Z0JBRW5CLDZCQUE2QjtnQkFDN0JpQjt3REFBVzt3QkFDVGpCLG1CQUFtQjtvQkFDckI7dURBQUc7WUFDTDtRQUNGOzJDQUFHO1FBQUNKO1FBQXFCTztLQUF3QjtJQUVqRCxpRUFBaUU7SUFFakUscUJBQ0UsOERBQUNnQjtrREFBZSxzQkFBZ0MsT0FBVnpCOzswQkFFcEMsOERBQUN5QjswREFBYzs7a0NBRWIsOERBQUNBO3dCQUVDQyxPQUFPOzRCQUNMQyxXQUFXdEIsa0JBQ1AsOEJBQ0E7NEJBQ0p1QixhQUFhLDZDQUE0R1QsT0FBL0RBLE9BQU90QyxTQUFTLENBQUNnRCxPQUFPLENBQUMsU0FBUyxLQUFJLDJCQUErRCxPQUF0Q1YsT0FBT3RDLFNBQVMsQ0FBQ2dELE9BQU8sQ0FBQyxTQUFTLEtBQUk7NEJBQy9KQyxRQUFRO3dCQUNWO2tFQVBZOzs7Ozs7a0NBV2QsOERBQUNMO3dCQUVDQyxPQUFPOzRCQUNMQyxXQUFXdEIsa0JBQ1Asc0NBQ0E7NEJBQ0p1QixhQUFhLCtDQUE4R1QsT0FBL0RBLE9BQU90QyxTQUFTLENBQUNnRCxPQUFPLENBQUMsU0FBUyxLQUFJLDJCQUErRCxPQUF0Q1YsT0FBT3RDLFNBQVMsQ0FBQ2dELE9BQU8sQ0FBQyxTQUFTLEtBQUk7NEJBQ2pLRSxTQUFTO3dCQUNYO2tFQVBZOzs7Ozs7a0NBV2QsOERBQUNOO3dCQUVDQyxPQUFPOzRCQUNMNUMsYUFBYXFDLE9BQU90QyxTQUFTLENBQUNnRCxPQUFPLENBQUMsU0FBUzs0QkFDL0NFLFNBQVM7NEJBQ1RKLFdBQVc7d0JBQ2I7a0VBTFk7Ozs7OztrQ0FTZCw4REFBQ0Y7d0JBQ0lDLE9BQU87NEJBQ0xNLFdBQVcsWUFBaUViLE9BQXJEQSxPQUFPdEMsU0FBUyxDQUFDZ0QsT0FBTyxDQUFDLFNBQVMsS0FBSSxpQkFBcUQsT0FBdENWLE9BQU90QyxTQUFTLENBQUNnRCxPQUFPLENBQUMsU0FBUyxLQUFJO3dCQUNwSDtrRUFIVyxvR0FBK0hWLE9BQTNCQSxPQUFPdkMsT0FBTyxFQUFDLGNBQStCLE9BQW5CdUMsT0FBT3JDLFdBQVcsRUFBQztrQ0FJaEssNEVBQUNzQztzRUFBZ0IsMkNBQStEZixPQUFwQmMsT0FBT3RDLFNBQVMsRUFBQyxLQUF5RCxPQUF0RHdCLGtCQUFrQix3QkFBd0IsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSzFJLDhEQUFDb0I7MERBQWUsZ0VBQWtGTixPQUFsQkEsT0FBT3ZDLE9BQU8sRUFBQyxLQUFnQ3VDLE9BQTdCQSxPQUFPckMsV0FBVyxFQUFDLFlBQTJCLE9BQWpCcUMsT0FBT3BDLFNBQVMsRUFBQzs7a0NBQzlJLDhEQUFDMEM7a0VBQWM7a0NBRWIsNEVBQUNBO3NFQUFjOzs4Q0FDYiw4REFBQ1E7OEVBQWdCLHdEQUF5RSxPQUFqQmQsT0FBT3RDLFNBQVMsRUFBQzs4Q0FDdkZxQix1QkFBdUJpQixPQUFPekMsSUFBSTs7Ozs7O2dDQUVwQ3FCLGVBQWVJLGlCQUFpQixZQUFZLENBQUNELHFDQUM1Qyw4REFBQytCOzhFQUFnQixpQ0FBa0QsT0FBakJkLE9BQU90QyxTQUFTLEVBQUM7OENBQWU7Ozs7OztnQ0FJbkZxQixxQ0FDQyw4REFBQytCOzhFQUFnQixpQ0FBa0QsT0FBakJkLE9BQU90QyxTQUFTLEVBQUM7OENBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVF0RnNCLENBQUFBLGlCQUFpQixnQkFBZ0JBLGlCQUFpQixRQUFPLG1CQUN6RCw4REFBQ3NCO2tFQUFjO2tDQUNiLDRFQUFDQTtzRUFBYztzQ0FDYiw0RUFBQ0E7Z0NBRUNDLE9BQU87b0NBQ0xRLE9BQU8vQixpQkFBaUIsV0FBVyxTQUFTO29DQUM1Q3dCLFdBQVd4QixpQkFBaUIsV0FDeEIsZ0ZBQ0E7Z0NBQ047MEVBTlcscUVBQTRGZ0IsT0FBdkJBLE9BQU9uQyxZQUFZLEVBQUMsS0FBcUIsT0FBbEJtQyxPQUFPbEMsVUFBVSxFQUFDOzBDQVN6SCw0RUFBQ3dDO29DQUVDQyxPQUFPO3dDQUNMQyxXQUFXO3dDQUNYUSxXQUFXO29DQUNiOzhFQUpVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBb0M1QjtHQTVMd0J0QztLQUFBQTtBQThMeEIsdUNBQXVDO0FBQ2hDLFNBQVN1Qzs7SUFDZCxNQUFNLENBQUN0QyxjQUFjdUMsZ0JBQWdCLEdBQUczRSwrQ0FBUUEsQ0FBa0I7SUFDbEUsTUFBTSxDQUFDNEUsY0FBY0MsZ0JBQWdCLEdBQUc3RSwrQ0FBUUEsQ0FBb0I7UUFBQztLQUFlO0lBRXBGLE1BQU04RSxjQUFjLENBQUNDO1FBQ25CSixnQkFBZ0JJO1FBQ2hCRixnQkFBZ0JmLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNaUI7YUFBTTtJQUMxQztJQUVBLE1BQU1DLFFBQVE7UUFDWkwsZ0JBQWdCO1FBQ2hCRSxnQkFBZ0I7WUFBQztTQUFlO0lBQ2xDO0lBRUEsT0FBTztRQUNMekM7UUFDQXdDO1FBQ0FFO1FBQ0FFO0lBQ0Y7QUFDRjtJQXBCZ0JOIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcRHluYW1pY1N0YXR1c0luZGljYXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIENwdUNoaXBJY29uLFxuICBCb2x0SWNvbixcbiAgQ29nSWNvbixcbiAgQ2hlY2tDaXJjbGVJY29uLFxuICBQZW5jaWxJY29uLFxuICBTcGFya2xlc0ljb24sXG4gIENsb2NrSWNvbixcbiAgQmVha2VySWNvbixcbiAgTWFnbmlmeWluZ0dsYXNzSWNvbixcbiAgQXJyb3dQYXRoSWNvbixcbiAgTGlnaHRCdWxiSWNvbixcbiAgUm9ja2V0TGF1bmNoSWNvbixcbiAgRmlyZUljb24sXG4gIENvbW1hbmRMaW5lSWNvbixcbiAgU3Rhckljb24sXG4gIFNoaWVsZENoZWNrSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG5leHBvcnQgdHlwZSBQcm9jZXNzaW5nU3RhZ2UgPSBcbiAgfCAnaW5pdGlhbGl6aW5nJ1xuICB8ICdhbmFseXppbmcnXG4gIHwgJ3JvdXRpbmcnXG4gIHwgJ2NvbXBsZXhpdHlfYW5hbHlzaXMnXG4gIHwgJ3JvbGVfY2xhc3NpZmljYXRpb24nXG4gIHwgJ3ByZXBhcmluZydcbiAgfCAnY29ubmVjdGluZydcbiAgfCAnZ2VuZXJhdGluZydcbiAgfCAndHlwaW5nJ1xuICB8ICdmaW5hbGl6aW5nJ1xuICB8ICdjb21wbGV0ZSc7XG5cbmludGVyZmFjZSBTdGF0dXNDb25maWcge1xuICBpY29uOiBSZWFjdC5Db21wb25lbnRUeXBlPHsgY2xhc3NOYW1lPzogc3RyaW5nIH0+O1xuICB0ZXh0OiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGJnQ29sb3I6IHN0cmluZztcbiAgaWNvbkNvbG9yOiBzdHJpbmc7XG4gIGJvcmRlckNvbG9yOiBzdHJpbmc7XG4gIGdsb3dDb2xvcjogc3RyaW5nO1xuICBncmFkaWVudEZyb206IHN0cmluZztcbiAgZ3JhZGllbnRUbzogc3RyaW5nO1xuICBkdXJhdGlvbj86IG51bWJlcjsgLy8gT3B0aW9uYWwgbWluaW11bSBkdXJhdGlvbiBpbiBtc1xufVxuXG5jb25zdCBTVEFUVVNfQ09ORklHUzogUmVjb3JkPFByb2Nlc3NpbmdTdGFnZSwgU3RhdHVzQ29uZmlnPiA9IHtcbiAgaW5pdGlhbGl6aW5nOiB7XG4gICAgaWNvbjogUm9ja2V0TGF1bmNoSWNvbixcbiAgICB0ZXh0OiAnSW5pdGlhbGl6aW5nJyxcbiAgICBkZXNjcmlwdGlvbjogJ1N0YXJ0aW5nIHVwIHN5c3RlbXMnLFxuICAgIGJnQ29sb3I6ICdiZy1ncmFkaWVudC10by1yIGZyb20tc2xhdGUtNTAgdG8tZ3JheS01MCcsXG4gICAgaWNvbkNvbG9yOiAndGV4dC1zbGF0ZS02MDAnLFxuICAgIGJvcmRlckNvbG9yOiAnYm9yZGVyLXNsYXRlLTIwMC82MCcsXG4gICAgZ2xvd0NvbG9yOiAnc2hhZG93LXNsYXRlLTIwMC81MCcsXG4gICAgZ3JhZGllbnRGcm9tOiAnZnJvbS1zbGF0ZS00MDAnLFxuICAgIGdyYWRpZW50VG86ICd0by1ncmF5LTQwMCcsXG4gICAgZHVyYXRpb246IDIwMFxuICB9LFxuICBhbmFseXppbmc6IHtcbiAgICBpY29uOiBNYWduaWZ5aW5nR2xhc3NJY29uLFxuICAgIHRleHQ6ICdBbmFseXppbmcnLFxuICAgIGRlc2NyaXB0aW9uOiAnVW5kZXJzdGFuZGluZyB5b3VyIHJlcXVlc3QnLFxuICAgIGJnQ29sb3I6ICdiZy1ncmFkaWVudC10by1yIGZyb20tY3lhbi01MCB0by1ibHVlLTUwJyxcbiAgICBpY29uQ29sb3I6ICd0ZXh0LWN5YW4tNjAwJyxcbiAgICBib3JkZXJDb2xvcjogJ2JvcmRlci1jeWFuLTIwMC82MCcsXG4gICAgZ2xvd0NvbG9yOiAnc2hhZG93LWN5YW4tMjAwLzUwJyxcbiAgICBncmFkaWVudEZyb206ICdmcm9tLWN5YW4tNDAwJyxcbiAgICBncmFkaWVudFRvOiAndG8tYmx1ZS00MDAnLFxuICAgIGR1cmF0aW9uOiAzMDBcbiAgfSxcbiAgcm91dGluZzoge1xuICAgIGljb246IEFycm93UGF0aEljb24sXG4gICAgdGV4dDogJ1NtYXJ0IHJvdXRpbmcnLFxuICAgIGRlc2NyaXB0aW9uOiAnRmluZGluZyBvcHRpbWFsIHBhdGgnLFxuICAgIGJnQ29sb3I6ICdiZy1ncmFkaWVudC10by1yIGZyb20taW5kaWdvLTUwIHRvLXB1cnBsZS01MCcsXG4gICAgaWNvbkNvbG9yOiAndGV4dC1pbmRpZ28tNjAwJyxcbiAgICBib3JkZXJDb2xvcjogJ2JvcmRlci1pbmRpZ28tMjAwLzYwJyxcbiAgICBnbG93Q29sb3I6ICdzaGFkb3ctaW5kaWdvLTIwMC81MCcsXG4gICAgZ3JhZGllbnRGcm9tOiAnZnJvbS1pbmRpZ28tNDAwJyxcbiAgICBncmFkaWVudFRvOiAndG8tcHVycGxlLTQwMCcsXG4gICAgZHVyYXRpb246IDQwMFxuICB9LFxuICBjb21wbGV4aXR5X2FuYWx5c2lzOiB7XG4gICAgaWNvbjogTGlnaHRCdWxiSWNvbixcbiAgICB0ZXh0OiAnQW5hbHl6aW5nIGNvbXBsZXhpdHknLFxuICAgIGRlc2NyaXB0aW9uOiAnRXZhbHVhdGluZyByZXF1ZXN0IGRlcHRoJyxcbiAgICBiZ0NvbG9yOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWFtYmVyLTUwIHRvLXllbGxvdy01MCcsXG4gICAgaWNvbkNvbG9yOiAndGV4dC1hbWJlci02MDAnLFxuICAgIGJvcmRlckNvbG9yOiAnYm9yZGVyLWFtYmVyLTIwMC82MCcsXG4gICAgZ2xvd0NvbG9yOiAnc2hhZG93LWFtYmVyLTIwMC81MCcsXG4gICAgZ3JhZGllbnRGcm9tOiAnZnJvbS1hbWJlci00MDAnLFxuICAgIGdyYWRpZW50VG86ICd0by15ZWxsb3ctNDAwJyxcbiAgICBkdXJhdGlvbjogNTAwXG4gIH0sXG4gIHJvbGVfY2xhc3NpZmljYXRpb246IHtcbiAgICBpY29uOiBTdGFySWNvbixcbiAgICB0ZXh0OiAnQXNzZW1ibGluZyBzcGVjaWFsaXN0cycsXG4gICAgZGVzY3JpcHRpb246ICdCdWlsZGluZyBleHBlcnQgdGVhbScsXG4gICAgYmdDb2xvcjogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS12aW9sZXQtNTAgdG8tcHVycGxlLTUwJyxcbiAgICBpY29uQ29sb3I6ICd0ZXh0LXZpb2xldC02MDAnLFxuICAgIGJvcmRlckNvbG9yOiAnYm9yZGVyLXZpb2xldC0yMDAvNjAnLFxuICAgIGdsb3dDb2xvcjogJ3NoYWRvdy12aW9sZXQtMjAwLzUwJyxcbiAgICBncmFkaWVudEZyb206ICdmcm9tLXZpb2xldC00MDAnLFxuICAgIGdyYWRpZW50VG86ICd0by1wdXJwbGUtNDAwJyxcbiAgICBkdXJhdGlvbjogNjAwXG4gIH0sXG4gIHByZXBhcmluZzoge1xuICAgIGljb246IEJvbHRJY29uLFxuICAgIHRleHQ6ICdQcmVwYXJpbmcnLFxuICAgIGRlc2NyaXB0aW9uOiAnU2V0dGluZyB1cCBwcm9jZXNzaW5nJyxcbiAgICBiZ0NvbG9yOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLW9yYW5nZS01MCB0by1hbWJlci01MCcsXG4gICAgaWNvbkNvbG9yOiAndGV4dC1vcmFuZ2UtNjAwJyxcbiAgICBib3JkZXJDb2xvcjogJ2JvcmRlci1vcmFuZ2UtMjAwLzYwJyxcbiAgICBnbG93Q29sb3I6ICdzaGFkb3ctb3JhbmdlLTIwMC81MCcsXG4gICAgZ3JhZGllbnRGcm9tOiAnZnJvbS1vcmFuZ2UtNDAwJyxcbiAgICBncmFkaWVudFRvOiAndG8tYW1iZXItNDAwJyxcbiAgICBkdXJhdGlvbjogMzAwXG4gIH0sXG4gIGNvbm5lY3Rpbmc6IHtcbiAgICBpY29uOiBDb21tYW5kTGluZUljb24sXG4gICAgdGV4dDogJ0Nvbm5lY3RpbmcnLFxuICAgIGRlc2NyaXB0aW9uOiAnRXN0YWJsaXNoaW5nIEFJIGxpbmsnLFxuICAgIGJnQ29sb3I6ICdiZy1ncmFkaWVudC10by1yIGZyb20tcm9zZS01MCB0by1waW5rLTUwJyxcbiAgICBpY29uQ29sb3I6ICd0ZXh0LXJvc2UtNjAwJyxcbiAgICBib3JkZXJDb2xvcjogJ2JvcmRlci1yb3NlLTIwMC82MCcsXG4gICAgZ2xvd0NvbG9yOiAnc2hhZG93LXJvc2UtMjAwLzUwJyxcbiAgICBncmFkaWVudEZyb206ICdmcm9tLXJvc2UtNDAwJyxcbiAgICBncmFkaWVudFRvOiAndG8tcGluay00MDAnLFxuICAgIGR1cmF0aW9uOiA0MDBcbiAgfSxcbiAgZ2VuZXJhdGluZzoge1xuICAgIGljb246IEZpcmVJY29uLFxuICAgIHRleHQ6ICdUaGlua2luZyBkZWVwbHknLFxuICAgIGRlc2NyaXB0aW9uOiAnQUkgcHJvY2Vzc2luZyBpbiBwcm9ncmVzcycsXG4gICAgYmdDb2xvcjogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1lbWVyYWxkLTUwIHRvLXRlYWwtNTAnLFxuICAgIGljb25Db2xvcjogJ3RleHQtZW1lcmFsZC02MDAnLFxuICAgIGJvcmRlckNvbG9yOiAnYm9yZGVyLWVtZXJhbGQtMjAwLzYwJyxcbiAgICBnbG93Q29sb3I6ICdzaGFkb3ctZW1lcmFsZC0yMDAvNTAnLFxuICAgIGdyYWRpZW50RnJvbTogJ2Zyb20tZW1lcmFsZC00MDAnLFxuICAgIGdyYWRpZW50VG86ICd0by10ZWFsLTQwMCcsXG4gICAgZHVyYXRpb246IDgwMFxuICB9LFxuICB0eXBpbmc6IHtcbiAgICBpY29uOiBQZW5jaWxJY29uLFxuICAgIHRleHQ6ICdTdHJlYW1pbmcgcmVzcG9uc2UnLFxuICAgIGRlc2NyaXB0aW9uOiAnRGVsaXZlcmluZyB5b3VyIGFuc3dlcicsXG4gICAgYmdDb2xvcjogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MCB0by1lbWVyYWxkLTUwJyxcbiAgICBpY29uQ29sb3I6ICd0ZXh0LWdyZWVuLTYwMCcsXG4gICAgYm9yZGVyQ29sb3I6ICdib3JkZXItZ3JlZW4tMjAwLzYwJyxcbiAgICBnbG93Q29sb3I6ICdzaGFkb3ctZ3JlZW4tMjAwLzUwJyxcbiAgICBncmFkaWVudEZyb206ICdmcm9tLWdyZWVuLTQwMCcsXG4gICAgZ3JhZGllbnRUbzogJ3RvLWVtZXJhbGQtNDAwJ1xuICB9LFxuICBmaW5hbGl6aW5nOiB7XG4gICAgaWNvbjogU3BhcmtsZXNJY29uLFxuICAgIHRleHQ6ICdGaW5hbGl6aW5nJyxcbiAgICBkZXNjcmlwdGlvbjogJ0FkZGluZyBmaW5pc2hpbmcgdG91Y2hlcycsXG4gICAgYmdDb2xvcjogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS10ZWFsLTUwIHRvLWN5YW4tNTAnLFxuICAgIGljb25Db2xvcjogJ3RleHQtdGVhbC02MDAnLFxuICAgIGJvcmRlckNvbG9yOiAnYm9yZGVyLXRlYWwtMjAwLzYwJyxcbiAgICBnbG93Q29sb3I6ICdzaGFkb3ctdGVhbC0yMDAvNTAnLFxuICAgIGdyYWRpZW50RnJvbTogJ2Zyb20tdGVhbC00MDAnLFxuICAgIGdyYWRpZW50VG86ICd0by1jeWFuLTQwMCcsXG4gICAgZHVyYXRpb246IDIwMFxuICB9LFxuICBjb21wbGV0ZToge1xuICAgIGljb246IFNoaWVsZENoZWNrSWNvbixcbiAgICB0ZXh0OiAnQ29tcGxldGUnLFxuICAgIGRlc2NyaXB0aW9uOiAnUmVzcG9uc2UgZGVsaXZlcmVkJyxcbiAgICBiZ0NvbG9yOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTUwIHRvLWxpbWUtNTAnLFxuICAgIGljb25Db2xvcjogJ3RleHQtZ3JlZW4tNjAwJyxcbiAgICBib3JkZXJDb2xvcjogJ2JvcmRlci1ncmVlbi0yMDAvNjAnLFxuICAgIGdsb3dDb2xvcjogJ3NoYWRvdy1ncmVlbi0yMDAvNTAnLFxuICAgIGdyYWRpZW50RnJvbTogJ2Zyb20tZ3JlZW4tNDAwJyxcbiAgICBncmFkaWVudFRvOiAndG8tbGltZS00MDAnLFxuICAgIGR1cmF0aW9uOiAxMDBcbiAgfVxufTtcblxuaW50ZXJmYWNlIER5bmFtaWNTdGF0dXNJbmRpY2F0b3JQcm9wcyB7XG4gIGN1cnJlbnRTdGFnZTogUHJvY2Vzc2luZ1N0YWdlO1xuICBpc1N0cmVhbWluZz86IGJvb2xlYW47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgb25TdGFnZUNoYW5nZT86IChzdGFnZTogUHJvY2Vzc2luZ1N0YWdlKSA9PiB2b2lkO1xuICBvcmNoZXN0cmF0aW9uU3RhdHVzPzogc3RyaW5nOyAvLyBDdXN0b20gc3RhdHVzIHRleHQgZm9yIG9yY2hlc3RyYXRpb25cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRHluYW1pY1N0YXR1c0luZGljYXRvcih7XG4gIGN1cnJlbnRTdGFnZSxcbiAgaXNTdHJlYW1pbmcgPSBmYWxzZSxcbiAgY2xhc3NOYW1lID0gJycsXG4gIG9uU3RhZ2VDaGFuZ2UsXG4gIG9yY2hlc3RyYXRpb25TdGF0dXNcbn06IER5bmFtaWNTdGF0dXNJbmRpY2F0b3JQcm9wcykge1xuICBjb25zdCBbZGlzcGxheVN0YWdlLCBzZXREaXNwbGF5U3RhZ2VdID0gdXNlU3RhdGU8UHJvY2Vzc2luZ1N0YWdlPihjdXJyZW50U3RhZ2UpO1xuICBjb25zdCBbaXNUcmFuc2l0aW9uaW5nLCBzZXRJc1RyYW5zaXRpb25pbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbb3JjaGVzdHJhdGlvbkNvbG9ySW5kZXgsIHNldE9yY2hlc3RyYXRpb25Db2xvckluZGV4XSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbbGFzdE9yY2hlc3RyYXRpb25TdGF0dXMsIHNldExhc3RPcmNoZXN0cmF0aW9uU3RhdHVzXSA9IHVzZVN0YXRlKCcnKTtcblxuICAvLyBDb2xvciBwYWxldHRlIGZvciBvcmNoZXN0cmF0aW9uIHByb2dyZXNzIGN5Y2xpbmdcbiAgY29uc3Qgb3JjaGVzdHJhdGlvbkNvbG9ycyA9IFtcbiAgICB7IGJnOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTUwJywgaWNvbjogJ3RleHQtYmx1ZS02MDAnLCBib3JkZXI6ICdib3JkZXItYmx1ZS0yMDAvNjAnLCBnbG93OiAnc2hhZG93LWJsdWUtMjAwLzUwJywgZ3JhZGllbnQ6ICdmcm9tLWJsdWUtNDAwIHRvLWluZGlnby00MDAnIH0sXG4gICAgeyBiZzogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNTAgdG8tdmlvbGV0LTUwJywgaWNvbjogJ3RleHQtcHVycGxlLTYwMCcsIGJvcmRlcjogJ2JvcmRlci1wdXJwbGUtMjAwLzYwJywgZ2xvdzogJ3NoYWRvdy1wdXJwbGUtMjAwLzUwJywgZ3JhZGllbnQ6ICdmcm9tLXB1cnBsZS00MDAgdG8tdmlvbGV0LTQwMCcgfSxcbiAgICB7IGJnOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWluZGlnby01MCB0by1ibHVlLTUwJywgaWNvbjogJ3RleHQtaW5kaWdvLTYwMCcsIGJvcmRlcjogJ2JvcmRlci1pbmRpZ28tMjAwLzYwJywgZ2xvdzogJ3NoYWRvdy1pbmRpZ28tMjAwLzUwJywgZ3JhZGllbnQ6ICdmcm9tLWluZGlnby00MDAgdG8tYmx1ZS00MDAnIH0sXG4gICAgeyBiZzogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1jeWFuLTUwIHRvLXRlYWwtNTAnLCBpY29uOiAndGV4dC1jeWFuLTYwMCcsIGJvcmRlcjogJ2JvcmRlci1jeWFuLTIwMC82MCcsIGdsb3c6ICdzaGFkb3ctY3lhbi0yMDAvNTAnLCBncmFkaWVudDogJ2Zyb20tY3lhbi00MDAgdG8tdGVhbC00MDAnIH0sXG4gICAgeyBiZzogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS10ZWFsLTUwIHRvLWVtZXJhbGQtNTAnLCBpY29uOiAndGV4dC10ZWFsLTYwMCcsIGJvcmRlcjogJ2JvcmRlci10ZWFsLTIwMC82MCcsIGdsb3c6ICdzaGFkb3ctdGVhbC0yMDAvNTAnLCBncmFkaWVudDogJ2Zyb20tdGVhbC00MDAgdG8tZW1lcmFsZC00MDAnIH0sXG4gICAgeyBiZzogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi01MCB0by1saW1lLTUwJywgaWNvbjogJ3RleHQtZ3JlZW4tNjAwJywgYm9yZGVyOiAnYm9yZGVyLWdyZWVuLTIwMC82MCcsIGdsb3c6ICdzaGFkb3ctZ3JlZW4tMjAwLzUwJywgZ3JhZGllbnQ6ICdmcm9tLWdyZWVuLTQwMCB0by1saW1lLTQwMCcgfSxcbiAgICB7IGJnOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy01MCB0by1hbWJlci01MCcsIGljb246ICd0ZXh0LXllbGxvdy02MDAnLCBib3JkZXI6ICdib3JkZXIteWVsbG93LTIwMC82MCcsIGdsb3c6ICdzaGFkb3cteWVsbG93LTIwMC81MCcsIGdyYWRpZW50OiAnZnJvbS15ZWxsb3ctNDAwIHRvLWFtYmVyLTQwMCcgfSxcbiAgICB7IGJnOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLW9yYW5nZS01MCB0by1yZWQtNTAnLCBpY29uOiAndGV4dC1vcmFuZ2UtNjAwJywgYm9yZGVyOiAnYm9yZGVyLW9yYW5nZS0yMDAvNjAnLCBnbG93OiAnc2hhZG93LW9yYW5nZS0yMDAvNTAnLCBncmFkaWVudDogJ2Zyb20tb3JhbmdlLTQwMCB0by1yZWQtNDAwJyB9LFxuICAgIHsgYmc6ICdiZy1ncmFkaWVudC10by1yIGZyb20tcm9zZS01MCB0by1waW5rLTUwJywgaWNvbjogJ3RleHQtcm9zZS02MDAnLCBib3JkZXI6ICdib3JkZXItcm9zZS0yMDAvNjAnLCBnbG93OiAnc2hhZG93LXJvc2UtMjAwLzUwJywgZ3JhZGllbnQ6ICdmcm9tLXJvc2UtNDAwIHRvLXBpbmstNDAwJyB9LFxuICAgIHsgYmc6ICdiZy1ncmFkaWVudC10by1yIGZyb20tZW1lcmFsZC01MCB0by10ZWFsLTUwJywgaWNvbjogJ3RleHQtZW1lcmFsZC02MDAnLCBib3JkZXI6ICdib3JkZXItZW1lcmFsZC0yMDAvNjAnLCBnbG93OiAnc2hhZG93LWVtZXJhbGQtMjAwLzUwJywgZ3JhZGllbnQ6ICdmcm9tLWVtZXJhbGQtNDAwIHRvLXRlYWwtNDAwJyB9XG4gIF07XG5cbiAgLy8gVXNlIG9yY2hlc3RyYXRpb24gY29sb3JzIGlmIGluIG9yY2hlc3RyYXRpb24gbW9kZSwgb3RoZXJ3aXNlIHVzZSBkZWZhdWx0IGNvbmZpZ1xuICBjb25zdCBpc09yY2hlc3RyYXRpb25Nb2RlID0gISFvcmNoZXN0cmF0aW9uU3RhdHVzO1xuICBjb25zdCBjdXJyZW50T3JjaGVzdHJhdGlvbkNvbG9yID0gb3JjaGVzdHJhdGlvbkNvbG9yc1tvcmNoZXN0cmF0aW9uQ29sb3JJbmRleCAlIG9yY2hlc3RyYXRpb25Db2xvcnMubGVuZ3RoXTtcblxuICBjb25zdCBjb25maWcgPSBpc09yY2hlc3RyYXRpb25Nb2RlID8ge1xuICAgIC4uLlNUQVRVU19DT05GSUdTW2Rpc3BsYXlTdGFnZV0sXG4gICAgLi4uY3VycmVudE9yY2hlc3RyYXRpb25Db2xvclxuICB9IDogU1RBVFVTX0NPTkZJR1NbZGlzcGxheVN0YWdlXTtcblxuICBjb25zdCBJY29uID0gY29uZmlnLmljb247XG5cbiAgLy8gSGFuZGxlIHN0YWdlIHRyYW5zaXRpb25zIHdpdGggc3BlZWQtdXAgYW5pbWF0aW9uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGN1cnJlbnRTdGFnZSAhPT0gZGlzcGxheVN0YWdlKSB7XG4gICAgICBjb25zb2xlLmxvZyhg8J+OryBEeW5hbWljU3RhdHVzSW5kaWNhdG9yOiAke2Rpc3BsYXlTdGFnZX0g4oaSICR7Y3VycmVudFN0YWdlfWApO1xuICAgICAgc2V0SXNUcmFuc2l0aW9uaW5nKHRydWUpO1xuXG4gICAgICAvLyBTcGVlZCB1cCBhbmltYXRpb24gZHVyaW5nIHRyYW5zaXRpb25cbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBzZXREaXNwbGF5U3RhZ2UoY3VycmVudFN0YWdlKTtcbiAgICAgICAgc2V0SXNUcmFuc2l0aW9uaW5nKGZhbHNlKTtcbiAgICAgICAgb25TdGFnZUNoYW5nZT8uKGN1cnJlbnRTdGFnZSk7XG4gICAgICB9LCAyMDApOyAvLyBCcmllZiB0cmFuc2l0aW9uIHBlcmlvZFxuICAgIH1cbiAgfSwgW2N1cnJlbnRTdGFnZSwgZGlzcGxheVN0YWdlLCBvblN0YWdlQ2hhbmdlXSk7XG5cbiAgLy8gSGFuZGxlIG9yY2hlc3RyYXRpb24gc3RhdHVzIGNoYW5nZXMgd2l0aCBjb2xvciBjeWNsaW5nXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG9yY2hlc3RyYXRpb25TdGF0dXMgJiYgb3JjaGVzdHJhdGlvblN0YXR1cyAhPT0gbGFzdE9yY2hlc3RyYXRpb25TdGF0dXMpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn46oIE9yY2hlc3RyYXRpb24gc3RhdHVzIGNoYW5nZWQ6ICR7b3JjaGVzdHJhdGlvblN0YXR1c31gKTtcbiAgICAgIHNldExhc3RPcmNoZXN0cmF0aW9uU3RhdHVzKG9yY2hlc3RyYXRpb25TdGF0dXMpO1xuICAgICAgc2V0T3JjaGVzdHJhdGlvbkNvbG9ySW5kZXgocHJldiA9PiBwcmV2ICsgMSk7XG4gICAgICBzZXRJc1RyYW5zaXRpb25pbmcodHJ1ZSk7XG5cbiAgICAgIC8vIEJyaWVmIHRyYW5zaXRpb24gYW5pbWF0aW9uXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgc2V0SXNUcmFuc2l0aW9uaW5nKGZhbHNlKTtcbiAgICAgIH0sIDMwMCk7XG4gICAgfVxuICB9LCBbb3JjaGVzdHJhdGlvblN0YXR1cywgbGFzdE9yY2hlc3RyYXRpb25TdGF0dXNdKTtcblxuICAvLyBSZW1vdmUgYXV0by1wcm9ncmVzc2lvbiAtIGxldCB0aGUgaG9vayBoYW5kbGUgc3RhZ2UgbWFuYWdlbWVudFxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGp1c3RpZnktc3RhcnQgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7LyogQ29tcGFjdCBtb2Rlcm4gaWNvbiB3aXRoIEhJR0hMWSBWSVNJQkxFIHNwaW5uaW5nIGFuaW1hdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy02IGgtNiByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXItMi41IG10LTAuNSBmbGV4LXNocmluay0wXCI+XG4gICAgICAgIHsvKiBPdXRlciBzcGlubmluZyByaW5nIC0gVkVSWSBWSVNJQkxFICovfVxuICAgICAgICA8ZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgLWluc2V0LTEuNSB3LTkgaC05IHJvdW5kZWQtZnVsbCBib3JkZXItWzNweF0gYm9yZGVyLXRyYW5zcGFyZW50IGFuaW1hdGUtc3BpbmB9XG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIGFuaW1hdGlvbjogaXNUcmFuc2l0aW9uaW5nXG4gICAgICAgICAgICAgID8gJ3NwaW4gMC42cyBsaW5lYXIgaW5maW5pdGUnXG4gICAgICAgICAgICAgIDogJ3NwaW4gMS4ycyBsaW5lYXIgaW5maW5pdGUnLFxuICAgICAgICAgICAgYm9yZGVySW1hZ2U6IGBjb25pYy1ncmFkaWVudChmcm9tIDBkZWcsIHRyYW5zcGFyZW50IDAlLCAke2NvbmZpZy5pY29uQ29sb3IucmVwbGFjZSgndGV4dC0nLCAnJyl9IDI1JSwgdHJhbnNwYXJlbnQgNTAlLCAke2NvbmZpZy5pY29uQ29sb3IucmVwbGFjZSgndGV4dC0nLCAnJyl9IDc1JSwgdHJhbnNwYXJlbnQgMTAwJSkgMWAsXG4gICAgICAgICAgICBmaWx0ZXI6ICdkcm9wLXNoYWRvdygwIDAgNHB4IHJnYmEoNTksIDEzMCwgMjQ2LCAwLjUpKSdcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBNaWRkbGUgc3Bpbm5pbmcgcmluZyAtIENPVU5URVIgUk9UQVRJT04gKi99XG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSAtaW5zZXQtMSB3LTggaC04IHJvdW5kZWQtZnVsbCBib3JkZXItWzJweF0gYm9yZGVyLXRyYW5zcGFyZW50IGFuaW1hdGUtc3BpbmB9XG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIGFuaW1hdGlvbjogaXNUcmFuc2l0aW9uaW5nXG4gICAgICAgICAgICAgID8gJ3NwaW4gMC44cyBsaW5lYXIgaW5maW5pdGUgcmV2ZXJzZSdcbiAgICAgICAgICAgICAgOiAnc3BpbiAxLjZzIGxpbmVhciBpbmZpbml0ZSByZXZlcnNlJyxcbiAgICAgICAgICAgIGJvcmRlckltYWdlOiBgY29uaWMtZ3JhZGllbnQoZnJvbSAxODBkZWcsIHRyYW5zcGFyZW50IDAlLCAke2NvbmZpZy5pY29uQ29sb3IucmVwbGFjZSgndGV4dC0nLCAnJyl9IDMwJSwgdHJhbnNwYXJlbnQgNjAlLCAke2NvbmZpZy5pY29uQ29sb3IucmVwbGFjZSgndGV4dC0nLCAnJyl9IDkwJSwgdHJhbnNwYXJlbnQgMTAwJSkgMWAsXG4gICAgICAgICAgICBvcGFjaXR5OiAwLjhcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBJbm5lciBwdWxzaW5nIHJpbmcgKi99XG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9e2BhYnNvbHV0ZSAtaW5zZXQtMC41IHctNyBoLTcgcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgYW5pbWF0ZS1wdWxzZWB9XG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIGJvcmRlckNvbG9yOiBjb25maWcuaWNvbkNvbG9yLnJlcGxhY2UoJ3RleHQtJywgJycpLFxuICAgICAgICAgICAgb3BhY2l0eTogMC42LFxuICAgICAgICAgICAgYW5pbWF0aW9uOiAncHVsc2UgMnMgZWFzZS1pbi1vdXQgaW5maW5pdGUnXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cblxuICAgICAgICB7LyogQ29yZSBjaXJjbGUgd2l0aCBlbmhhbmNlZCBnbG93ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHJlbGF0aXZlIHctZnVsbCBoLWZ1bGwgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCAke2NvbmZpZy5iZ0NvbG9yfSBib3JkZXItMiAke2NvbmZpZy5ib3JkZXJDb2xvcn0gc2hhZG93LWxnIGJhY2tkcm9wLWJsdXItc21gfVxuICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICBib3hTaGFkb3c6IGAwIDAgMTJweCAke2NvbmZpZy5pY29uQ29sb3IucmVwbGFjZSgndGV4dC0nLCAnJyl9NDAsIDAgMCAyNHB4ICR7Y29uZmlnLmljb25Db2xvci5yZXBsYWNlKCd0ZXh0LScsICcnKX0yMGBcbiAgICAgICAgICAgICB9fT5cbiAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9e2B3LTMuNSBoLTMuNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgJHtjb25maWcuaWNvbkNvbG9yfSAke2lzVHJhbnNpdGlvbmluZyA/ICdzY2FsZS0xMjUgcm90YXRlLTEyJyA6ICdzY2FsZS0xMDAnfSBkcm9wLXNoYWRvdy1sZ2B9IC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb21wYWN0IHN0YXR1cyBidWJibGUgd2l0aCBtb2Rlcm4gZGVzaWduICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BtYXgtdy1bNjUlXSByb3VuZGVkLXhsIHB4LTMgcHktMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgJHtjb25maWcuYmdDb2xvcn0gJHtjb25maWcuYm9yZGVyQ29sb3J9IGJvcmRlciAke2NvbmZpZy5nbG93Q29sb3J9IHNoYWRvdy1zbSBiYWNrZHJvcC1ibHVyLXNtYH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xLjVcIj5cbiAgICAgICAgICB7LyogU3RhdHVzIHRleHQgd2l0aCBlbmhhbmNlZCB0eXBvZ3JhcGh5ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tNTAwICR7Y29uZmlnLmljb25Db2xvcn0gdHJhY2tpbmctd2lkZWB9PlxuICAgICAgICAgICAgICB7b3JjaGVzdHJhdGlvblN0YXR1cyB8fCBjb25maWcudGV4dH1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIHtpc1N0cmVhbWluZyAmJiBkaXNwbGF5U3RhZ2UgPT09ICd0eXBpbmcnICYmICFvcmNoZXN0cmF0aW9uU3RhdHVzICYmIChcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgbWwtMS41IHRleHQtWzEwcHhdIG9wYWNpdHktODAgJHtjb25maWcuaWNvbkNvbG9yfSBmb250LW1lZGl1bWB9PlxuICAgICAgICAgICAgICAgIOKAoiBMaXZlXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7b3JjaGVzdHJhdGlvblN0YXR1cyAmJiAoXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YG1sLTEuNSB0ZXh0LVsxMHB4XSBvcGFjaXR5LTgwICR7Y29uZmlnLmljb25Db2xvcn0gZm9udC1tZWRpdW1gfT5cbiAgICAgICAgICAgICAgICDigKIgT3JjaGVzdHJhdGluZ1xuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogSElHSExZIFZJU0lCTEUgYW5pbWF0ZWQgcHJvZ3Jlc3MgYmFyICovfVxuICAgICAgICB7KGRpc3BsYXlTdGFnZSA9PT0gJ2dlbmVyYXRpbmcnIHx8IGRpc3BsYXlTdGFnZSA9PT0gJ3R5cGluZycpICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0xIHJvdW5kZWQtZnVsbCBvdmVyZmxvdy1oaWRkZW4gYmctd2hpdGUvMzAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLWZ1bGwgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTEwMDAgYmctZ3JhZGllbnQtdG8tciAke2NvbmZpZy5ncmFkaWVudEZyb219ICR7Y29uZmlnLmdyYWRpZW50VG99IHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbmB9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiBkaXNwbGF5U3RhZ2UgPT09ICd0eXBpbmcnID8gJzEwMCUnIDogJzYwJScsXG4gICAgICAgICAgICAgICAgICBhbmltYXRpb246IGRpc3BsYXlTdGFnZSA9PT0gJ3R5cGluZydcbiAgICAgICAgICAgICAgICAgICAgPyAncHJvZ3Jlc3NQdWxzZSAxLjVzIGVhc2UtaW4tb3V0IGluZmluaXRlLCBwcm9ncmVzc1NoaW1tZXIgMnMgbGluZWFyIGluZmluaXRlJ1xuICAgICAgICAgICAgICAgICAgICA6ICdwcm9ncmVzc1NoaW1tZXIgMnMgbGluZWFyIGluZmluaXRlLCBwcm9ncmVzc0dsb3cgM3MgZWFzZS1pbi1vdXQgaW5maW5pdGUnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHsvKiBBbmltYXRlZCBzaGluZSBlZmZlY3QgKi99XG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tdHJhbnNwYXJlbnQgdmlhLXdoaXRlLzQwIHRvLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGFuaW1hdGlvbjogJ3Byb2dyZXNzU2hpbmUgMnMgbGluZWFyIGluZmluaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAnc2tld1goLTIwZGVnKSdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRW5oYW5jZWQgYW5pbWF0aW9uIHN0eWxlcyAqL31cbiAgICAgIDxzdHlsZSBqc3g+e2BcbiAgICAgICAgQGtleWZyYW1lcyBwcm9ncmVzc1NoaW1tZXIge1xuICAgICAgICAgIDAlIHsgYmFja2dyb3VuZC1wb3NpdGlvbjogLTIwMCUgMDsgfVxuICAgICAgICAgIDEwMCUgeyBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAyMDAlIDA7IH1cbiAgICAgICAgfVxuXG4gICAgICAgIEBrZXlmcmFtZXMgcHJvZ3Jlc3NTaGluZSB7XG4gICAgICAgICAgMCUgeyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTEwMCUpIHNrZXdYKC0yMGRlZyk7IH1cbiAgICAgICAgICAxMDAlIHsgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDMwMCUpIHNrZXdYKC0yMGRlZyk7IH1cbiAgICAgICAgfVxuXG4gICAgICAgIEBrZXlmcmFtZXMgcHJvZ3Jlc3NQdWxzZSB7XG4gICAgICAgICAgMCUsIDEwMCUgeyBvcGFjaXR5OiAxOyB0cmFuc2Zvcm06IHNjYWxlWSgxKTsgfVxuICAgICAgICAgIDUwJSB7IG9wYWNpdHk6IDAuODsgdHJhbnNmb3JtOiBzY2FsZVkoMS4xKTsgfVxuICAgICAgICB9XG5cbiAgICAgICAgQGtleWZyYW1lcyBwcm9ncmVzc0dsb3cge1xuICAgICAgICAgIDAlLCAxMDAlIHsgZmlsdGVyOiBicmlnaHRuZXNzKDEpIGRyb3Atc2hhZG93KDAgMCAycHggY3VycmVudENvbG9yKTsgfVxuICAgICAgICAgIDUwJSB7IGZpbHRlcjogYnJpZ2h0bmVzcygxLjIpIGRyb3Atc2hhZG93KDAgMCA2cHggY3VycmVudENvbG9yKTsgfVxuICAgICAgICB9XG4gICAgICBgfTwvc3R5bGU+XG4gICAgPC9kaXY+XG4gICk7XG59XG5cbi8vIEhvb2sgZm9yIG1hbmFnaW5nIHN0YXR1cyBwcm9ncmVzc2lvblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVN0YXR1c1Byb2dyZXNzaW9uKCkge1xuICBjb25zdCBbY3VycmVudFN0YWdlLCBzZXRDdXJyZW50U3RhZ2VdID0gdXNlU3RhdGU8UHJvY2Vzc2luZ1N0YWdlPignaW5pdGlhbGl6aW5nJyk7XG4gIGNvbnN0IFtzdGFnZUhpc3RvcnksIHNldFN0YWdlSGlzdG9yeV0gPSB1c2VTdGF0ZTxQcm9jZXNzaW5nU3RhZ2VbXT4oWydpbml0aWFsaXppbmcnXSk7XG5cbiAgY29uc3QgdXBkYXRlU3RhZ2UgPSAoc3RhZ2U6IFByb2Nlc3NpbmdTdGFnZSkgPT4ge1xuICAgIHNldEN1cnJlbnRTdGFnZShzdGFnZSk7XG4gICAgc2V0U3RhZ2VIaXN0b3J5KHByZXYgPT4gWy4uLnByZXYsIHN0YWdlXSk7XG4gIH07XG5cbiAgY29uc3QgcmVzZXQgPSAoKSA9PiB7XG4gICAgc2V0Q3VycmVudFN0YWdlKCdpbml0aWFsaXppbmcnKTtcbiAgICBzZXRTdGFnZUhpc3RvcnkoWydpbml0aWFsaXppbmcnXSk7XG4gIH07XG5cbiAgcmV0dXJuIHtcbiAgICBjdXJyZW50U3RhZ2UsXG4gICAgc3RhZ2VIaXN0b3J5LFxuICAgIHVwZGF0ZVN0YWdlLFxuICAgIHJlc2V0XG4gIH07XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJvbHRJY29uIiwiUGVuY2lsSWNvbiIsIlNwYXJrbGVzSWNvbiIsIk1hZ25pZnlpbmdHbGFzc0ljb24iLCJBcnJvd1BhdGhJY29uIiwiTGlnaHRCdWxiSWNvbiIsIlJvY2tldExhdW5jaEljb24iLCJGaXJlSWNvbiIsIkNvbW1hbmRMaW5lSWNvbiIsIlN0YXJJY29uIiwiU2hpZWxkQ2hlY2tJY29uIiwiU1RBVFVTX0NPTkZJR1MiLCJpbml0aWFsaXppbmciLCJpY29uIiwidGV4dCIsImRlc2NyaXB0aW9uIiwiYmdDb2xvciIsImljb25Db2xvciIsImJvcmRlckNvbG9yIiwiZ2xvd0NvbG9yIiwiZ3JhZGllbnRGcm9tIiwiZ3JhZGllbnRUbyIsImR1cmF0aW9uIiwiYW5hbHl6aW5nIiwicm91dGluZyIsImNvbXBsZXhpdHlfYW5hbHlzaXMiLCJyb2xlX2NsYXNzaWZpY2F0aW9uIiwicHJlcGFyaW5nIiwiY29ubmVjdGluZyIsImdlbmVyYXRpbmciLCJ0eXBpbmciLCJmaW5hbGl6aW5nIiwiY29tcGxldGUiLCJEeW5hbWljU3RhdHVzSW5kaWNhdG9yIiwiY3VycmVudFN0YWdlIiwiaXNTdHJlYW1pbmciLCJjbGFzc05hbWUiLCJvblN0YWdlQ2hhbmdlIiwib3JjaGVzdHJhdGlvblN0YXR1cyIsImRpc3BsYXlTdGFnZSIsInNldERpc3BsYXlTdGFnZSIsImlzVHJhbnNpdGlvbmluZyIsInNldElzVHJhbnNpdGlvbmluZyIsIm9yY2hlc3RyYXRpb25Db2xvckluZGV4Iiwic2V0T3JjaGVzdHJhdGlvbkNvbG9ySW5kZXgiLCJsYXN0T3JjaGVzdHJhdGlvblN0YXR1cyIsInNldExhc3RPcmNoZXN0cmF0aW9uU3RhdHVzIiwib3JjaGVzdHJhdGlvbkNvbG9ycyIsImJnIiwiYm9yZGVyIiwiZ2xvdyIsImdyYWRpZW50IiwiaXNPcmNoZXN0cmF0aW9uTW9kZSIsImN1cnJlbnRPcmNoZXN0cmF0aW9uQ29sb3IiLCJsZW5ndGgiLCJjb25maWciLCJJY29uIiwiY29uc29sZSIsImxvZyIsInNldFRpbWVvdXQiLCJwcmV2IiwiZGl2Iiwic3R5bGUiLCJhbmltYXRpb24iLCJib3JkZXJJbWFnZSIsInJlcGxhY2UiLCJmaWx0ZXIiLCJvcGFjaXR5IiwiYm94U2hhZG93Iiwic3BhbiIsIndpZHRoIiwidHJhbnNmb3JtIiwidXNlU3RhdHVzUHJvZ3Jlc3Npb24iLCJzZXRDdXJyZW50U3RhZ2UiLCJzdGFnZUhpc3RvcnkiLCJzZXRTdGFnZUhpc3RvcnkiLCJ1cGRhdGVTdGFnZSIsInN0YWdlIiwicmVzZXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\n"));

/***/ })

});
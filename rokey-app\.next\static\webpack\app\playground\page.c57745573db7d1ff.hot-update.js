"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx":
/*!***************************************************!*\
  !*** ./src/components/DynamicStatusIndicator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DynamicStatusIndicator),\n/* harmony export */   useStatusProgression: () => (/* binding */ useStatusProgression)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CommandLineIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default,useStatusProgression auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst STATUS_CONFIGS = {\n    initializing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        text: 'Initializing',\n        description: 'Starting up systems',\n        bgColor: 'bg-gradient-to-r from-slate-50 to-gray-50',\n        iconColor: 'text-slate-600',\n        borderColor: 'border-slate-200/60',\n        glowColor: 'shadow-slate-200/50',\n        gradientFrom: 'from-slate-400',\n        gradientTo: 'to-gray-400',\n        duration: 200\n    },\n    analyzing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        text: 'Analyzing',\n        description: 'Understanding your request',\n        bgColor: 'bg-gradient-to-r from-cyan-50 to-blue-50',\n        iconColor: 'text-cyan-600',\n        borderColor: 'border-cyan-200/60',\n        glowColor: 'shadow-cyan-200/50',\n        gradientFrom: 'from-cyan-400',\n        gradientTo: 'to-blue-400',\n        duration: 300\n    },\n    routing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        text: 'Smart routing',\n        description: 'Finding optimal path',\n        bgColor: 'bg-gradient-to-r from-indigo-50 to-purple-50',\n        iconColor: 'text-indigo-600',\n        borderColor: 'border-indigo-200/60',\n        glowColor: 'shadow-indigo-200/50',\n        gradientFrom: 'from-indigo-400',\n        gradientTo: 'to-purple-400',\n        duration: 400\n    },\n    complexity_analysis: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        text: 'Analyzing complexity',\n        description: 'Evaluating request depth',\n        bgColor: 'bg-gradient-to-r from-amber-50 to-yellow-50',\n        iconColor: 'text-amber-600',\n        borderColor: 'border-amber-200/60',\n        glowColor: 'shadow-amber-200/50',\n        gradientFrom: 'from-amber-400',\n        gradientTo: 'to-yellow-400',\n        duration: 500\n    },\n    role_classification: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        text: 'Assembling specialists',\n        description: 'Building expert team',\n        bgColor: 'bg-gradient-to-r from-violet-50 to-purple-50',\n        iconColor: 'text-violet-600',\n        borderColor: 'border-violet-200/60',\n        glowColor: 'shadow-violet-200/50',\n        gradientFrom: 'from-violet-400',\n        gradientTo: 'to-purple-400',\n        duration: 600\n    },\n    preparing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        text: 'Preparing',\n        description: 'Setting up processing',\n        bgColor: 'bg-gradient-to-r from-orange-50 to-amber-50',\n        iconColor: 'text-orange-600',\n        borderColor: 'border-orange-200/60',\n        glowColor: 'shadow-orange-200/50',\n        gradientFrom: 'from-orange-400',\n        gradientTo: 'to-amber-400',\n        duration: 300\n    },\n    connecting: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        text: 'Connecting',\n        description: 'Establishing AI link',\n        bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',\n        iconColor: 'text-rose-600',\n        borderColor: 'border-rose-200/60',\n        glowColor: 'shadow-rose-200/50',\n        gradientFrom: 'from-rose-400',\n        gradientTo: 'to-pink-400',\n        duration: 400\n    },\n    generating: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        text: 'Thinking deeply',\n        description: 'AI processing in progress',\n        bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n        iconColor: 'text-emerald-600',\n        borderColor: 'border-emerald-200/60',\n        glowColor: 'shadow-emerald-200/50',\n        gradientFrom: 'from-emerald-400',\n        gradientTo: 'to-teal-400',\n        duration: 800\n    },\n    typing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        text: 'Streaming response',\n        description: 'Delivering your answer',\n        bgColor: 'bg-gradient-to-r from-green-50 to-emerald-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-emerald-400'\n    },\n    finalizing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        text: 'Finalizing',\n        description: 'Adding finishing touches',\n        bgColor: 'bg-gradient-to-r from-teal-50 to-cyan-50',\n        iconColor: 'text-teal-600',\n        borderColor: 'border-teal-200/60',\n        glowColor: 'shadow-teal-200/50',\n        gradientFrom: 'from-teal-400',\n        gradientTo: 'to-cyan-400',\n        duration: 200\n    },\n    complete: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        text: 'Complete',\n        description: 'Response delivered',\n        bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-lime-400',\n        duration: 100\n    }\n};\nfunction DynamicStatusIndicator(param) {\n    let { currentStage, isStreaming = false, className = '', onStageChange, orchestrationStatus } = param;\n    _s();\n    const [displayStage, setDisplayStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(currentStage);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [orchestrationColorIndex, setOrchestrationColorIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastOrchestrationStatus, setLastOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    // Color palette for orchestration progress cycling\n    const orchestrationColors = [\n        {\n            bg: 'bg-gradient-to-r from-blue-50 to-indigo-50',\n            icon: 'text-blue-600',\n            border: 'border-blue-200/60',\n            glow: 'shadow-blue-200/50',\n            gradient: 'from-blue-400 to-indigo-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-purple-50 to-violet-50',\n            icon: 'text-purple-600',\n            border: 'border-purple-200/60',\n            glow: 'shadow-purple-200/50',\n            gradient: 'from-purple-400 to-violet-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-indigo-50 to-blue-50',\n            icon: 'text-indigo-600',\n            border: 'border-indigo-200/60',\n            glow: 'shadow-indigo-200/50',\n            gradient: 'from-indigo-400 to-blue-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-cyan-50 to-teal-50',\n            icon: 'text-cyan-600',\n            border: 'border-cyan-200/60',\n            glow: 'shadow-cyan-200/50',\n            gradient: 'from-cyan-400 to-teal-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-teal-50 to-emerald-50',\n            icon: 'text-teal-600',\n            border: 'border-teal-200/60',\n            glow: 'shadow-teal-200/50',\n            gradient: 'from-teal-400 to-emerald-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-green-50 to-lime-50',\n            icon: 'text-green-600',\n            border: 'border-green-200/60',\n            glow: 'shadow-green-200/50',\n            gradient: 'from-green-400 to-lime-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-yellow-50 to-amber-50',\n            icon: 'text-yellow-600',\n            border: 'border-yellow-200/60',\n            glow: 'shadow-yellow-200/50',\n            gradient: 'from-yellow-400 to-amber-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-orange-50 to-red-50',\n            icon: 'text-orange-600',\n            border: 'border-orange-200/60',\n            glow: 'shadow-orange-200/50',\n            gradient: 'from-orange-400 to-red-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-rose-50 to-pink-50',\n            icon: 'text-rose-600',\n            border: 'border-rose-200/60',\n            glow: 'shadow-rose-200/50',\n            gradient: 'from-rose-400 to-pink-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n            icon: 'text-emerald-600',\n            border: 'border-emerald-200/60',\n            glow: 'shadow-emerald-200/50',\n            gradient: 'from-emerald-400 to-teal-400'\n        }\n    ];\n    // Use orchestration colors if in orchestration mode, otherwise use default config\n    const isOrchestrationMode = !!orchestrationStatus;\n    const currentOrchestrationColor = orchestrationColors[orchestrationColorIndex % orchestrationColors.length];\n    const config = isOrchestrationMode ? {\n        ...STATUS_CONFIGS[displayStage],\n        ...currentOrchestrationColor\n    } : STATUS_CONFIGS[displayStage];\n    const Icon = config.icon;\n    // Handle stage transitions with speed-up animation\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (currentStage !== displayStage) {\n                console.log(\"\\uD83C\\uDFAF DynamicStatusIndicator: \".concat(displayStage, \" → \").concat(currentStage));\n                setIsTransitioning(true);\n                // Speed up animation during transition\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setDisplayStage(currentStage);\n                        setIsTransitioning(false);\n                        onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange(currentStage);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 200); // Brief transition period\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        currentStage,\n        displayStage,\n        onStageChange\n    ]);\n    // Remove auto-progression - let the hook handle stage management\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex justify-start \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.6s linear infinite' : 'spin 1.2s linear infinite',\n                            borderImage: \"conic-gradient(from 0deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 25%, transparent 50%, \").concat(config.iconColor.replace('text-', ''), \" 75%, transparent 100%) 1\"),\n                            filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.8s linear infinite reverse' : 'spin 1.6s linear infinite reverse',\n                            borderImage: \"conic-gradient(from 180deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 30%, transparent 60%, \").concat(config.iconColor.replace('text-', ''), \" 90%, transparent 100%) 1\"),\n                            opacity: 0.8\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            borderColor: config.iconColor.replace('text-', ''),\n                            opacity: 0.6,\n                            animation: 'pulse 2s ease-in-out infinite'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            boxShadow: \"0 0 12px \".concat(config.iconColor.replace('text-', ''), \"40, 0 0 24px \").concat(config.iconColor.replace('text-', ''), \"20\")\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 \".concat(config.bgColor, \" border-2 \").concat(config.borderColor, \" shadow-lg backdrop-blur-sm\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"w-3.5 h-3.5 transition-all duration-500 \".concat(config.iconColor, \" \").concat(isTransitioning ? 'scale-125 rotate-12' : 'scale-100', \" drop-shadow-lg\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 \".concat(config.bgColor, \" \").concat(config.borderColor, \" border \").concat(config.glowColor, \" shadow-sm backdrop-blur-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex items-center space-x-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"transition-all duration-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"text-xs font-semibold transition-colors duration-500 \".concat(config.iconColor, \" tracking-wide\"),\n                                    children: orchestrationStatus || config.text\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                isStreaming && displayStage === 'typing' && !orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Live\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this),\n                                orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Orchestrating\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    (displayStage === 'generating' || displayStage === 'typing') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: displayStage === 'typing' ? '100%' : '60%',\n                                    animation: displayStage === 'typing' ? 'progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite' : 'progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite'\n                                },\n                                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-full rounded-full transition-all duration-1000 bg-gradient-to-r \".concat(config.gradientFrom, \" \").concat(config.gradientTo, \" relative overflow-hidden\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        animation: 'progressShine 2s linear infinite',\n                                        transform: 'skewX(-20deg)'\n                                    },\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"f56d70faa8a01b64\",\n                children: \"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(DynamicStatusIndicator, \"MnCPi12tsVyJolwX3z5kC6DEv5M=\");\n_c = DynamicStatusIndicator;\n// Hook for managing status progression\nfunction useStatusProgression() {\n    _s1();\n    const [currentStage, setCurrentStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('initializing');\n    const [stageHistory, setStageHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        'initializing'\n    ]);\n    const updateStage = (stage)=>{\n        setCurrentStage(stage);\n        setStageHistory((prev)=>[\n                ...prev,\n                stage\n            ]);\n    };\n    const reset = ()=>{\n        setCurrentStage('initializing');\n        setStageHistory([\n            'initializing'\n        ]);\n    };\n    return {\n        currentStage,\n        stageHistory,\n        updateStage,\n        reset\n    };\n}\n_s1(useStatusProgression, \"7mqPvLO7RfuzcLNJcZduGbqc5yY=\");\nvar _c;\n$RefreshReg$(_c, \"DynamicStatusIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\n"));

/***/ })

});
import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { createClient } from '@supabase/supabase-js';
import { decrypt } from '@/lib/encryption';
import { type ApiKey } from '@/types/apiKeys';
import { PREDEFINED_ROLES } from '@/config/roles';
import { z } from 'zod';
import { generateRoleUsedMessage } from '@/utils/logFormatting';
// LangGraph Integration for multi-role orchestration
import { RouKeyLangGraphIntegration } from '@/lib/langgraph-orchestration/RouKeyLangGraphIntegration';

import crypto from 'crypto';

// Phase 1 Optimization: Enhanced intelligent routing cache with longer TTL
const routingCache = new Map<string, { roleId?: string; timestamp: number; consecutiveGeneralChat?: number }>();
const ROUTING_CACHE_TTL = 3600000; // 1 hour cache for routing decisions (4x longer for better hit rate)

// Phase 2 Optimization: User role frequency tracking for intelligent role ordering
const userRoleFrequency = new Map<string, { [roleId: string]: { count: number; lastUsed: number } }>();
const USER_ROLE_FREQUENCY_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days

// Phase 2 Optimization: Semantic classification caching for similar prompts
const semanticCache = new Map<string, { roleId: string; confidence: number; timestamp: number; promptHash: string }>();
const SEMANTIC_CACHE_TTL = 30 * 60 * 1000; // 30 minutes for semantic matches

// Phase 2 Optimization: User role ecosystem preloading cache
const userRoleEcosystemCache = new Map<string, {
  customRoles: any[];
  roleAssignments: any[];
  apiKeys: any[];
  timestamp: number
}>();
const USER_ECOSYSTEM_CACHE_TTL = 15 * 60 * 1000; // 15 minutes

// Phase 2 Helper: Track user role usage for intelligent ordering
function trackUserRoleUsage(userId: string, roleId: string) {
  if (!userRoleFrequency.has(userId)) {
    userRoleFrequency.set(userId, {});
  }

  const userFreq = userRoleFrequency.get(userId)!;
  if (!userFreq[roleId]) {
    userFreq[roleId] = { count: 0, lastUsed: 0 };
  }

  userFreq[roleId].count++;
  userFreq[roleId].lastUsed = Date.now();

  console.log(`[User Learning] Tracked role usage: ${userId} -> ${roleId} (count: ${userFreq[roleId].count})`);
}

// Phase 2 Helper: Get user's most frequently used roles for intelligent ordering
function getUserTopRoles(userId: string, limit: number = 5): string[] {
  const userFreq = userRoleFrequency.get(userId);
  if (!userFreq) return [];

  return Object.entries(userFreq)
    .filter(([_, data]) => (Date.now() - data.lastUsed) < USER_ROLE_FREQUENCY_TTL)
    .sort(([_, a], [__, b]) => b.count - a.count)
    .slice(0, limit)
    .map(([roleId, _]) => roleId);
}

// Phase 2 Helper: Optimize role order based on user patterns
function optimizeRoleOrder(allRoles: any[], userId: string): any[] {
  const userTopRoles = getUserTopRoles(userId, 8);

  if (userTopRoles.length === 0) {
    return allRoles; // No user patterns yet, use default order
  }

  console.log(`[User Learning] Optimizing role order for user ${userId}, top roles: ${userTopRoles.join(', ')}`);

  // Put user's most frequent roles first
  const topRoles = allRoles.filter(role => userTopRoles.includes(role.id));
  const otherRoles = allRoles.filter(role => !userTopRoles.includes(role.id));

  return [...topRoles, ...otherRoles];
}

// Phase 2 Helper: Simple semantic similarity for prompt caching
function calculatePromptSimilarity(prompt1: string, prompt2: string): number {
  const words1 = prompt1.toLowerCase().split(/\s+/);
  const words2 = prompt2.toLowerCase().split(/\s+/);

  const intersection = words1.filter(word => words2.includes(word));
  const union = [...new Set([...words1, ...words2])];

  return intersection.length / union.length; // Jaccard similarity
}

// Phase 2 Helper: Find semantically similar cached classification
function findSimilarCachedClassification(prompt: string, threshold: number = 0.7): { roleId: string; confidence: number } | null {
  const currentTime = Date.now();

  for (const [cachedPrompt, data] of semanticCache.entries()) {
    if ((currentTime - data.timestamp) > SEMANTIC_CACHE_TTL) {
      semanticCache.delete(cachedPrompt); // Clean expired entries
      continue;
    }

    const similarity = calculatePromptSimilarity(prompt, cachedPrompt);
    if (similarity >= threshold) {
      console.log(`[Semantic Cache] Found similar prompt (${(similarity * 100).toFixed(1)}% match): "${cachedPrompt}" -> ${data.roleId}`);
      return { roleId: data.roleId, confidence: data.confidence * similarity };
    }
  }

  return null;
}

// Phase 3 Optimization: Background cache warming for common patterns
const backgroundCacheWarmer = {
  // Warm caches based on user patterns
  warmUserPatterns: async (userId: string, supabase: any) => {
    try {
      // Get user's recent successful classifications
      const recentClassifications = userRoleFrequency.get(userId);
      if (!recentClassifications) return;

      const topRoles = Object.entries(recentClassifications)
        .sort(([_, a], [__, b]) => b.count - a.count)
        .slice(0, 3)
        .map(([roleId, _]) => roleId);

      console.log(`[Background Warmer] Pre-warming data for user ${userId}, top roles: ${topRoles.join(', ')}`);

      // Pre-warm user ecosystem cache
      const configIds = await getUserConfigIds(userId, supabase);
      for (const configId of configIds) {
        preloadUserRoleEcosystem(userId, configId, supabase);
      }
    } catch (error) {
      console.warn('[Background Warmer] Error warming user patterns:', error);
    }
  },

  // Warm common classification patterns
  warmCommonPatterns: async () => {
    try {
      const commonPrompts = [
        "help me write code",
        "write an article",
        "analyze this data",
        "translate this text",
        "tell me a story"
      ];

      console.log('[Background Warmer] Pre-warming common classification patterns...');

      // These will populate semantic cache for faster future lookups
      // Note: This is just cache preparation, not actual classification
      for (const prompt of commonPrompts) {
        const hash = generateQueryHash(prompt);
        // Mark as warmed but not classified yet
        console.log(`[Background Warmer] Prepared cache entry for: "${prompt}"`);
      }
    } catch (error) {
      console.warn('[Background Warmer] Error warming common patterns:', error);
    }
  }
};

// Phase 3 Helper: Get user's config IDs for cache warming
async function getUserConfigIds(userId: string, supabase: any): Promise<string[]> {
  try {
    const { data: configs } = await supabase
      .from('custom_api_configs')
      .select('id')
      .eq('user_id', userId)
      .limit(5); // Limit to avoid excessive warming

    return configs ? configs.map((c: any) => c.id) : [];
  } catch (error) {
    console.warn('[Background Warmer] Error fetching user configs:', error);
    return [];
  }
}

// Phase 3 Optimization: Request deduplication for concurrent identical requests
const pendingClassifications = new Map<string, Promise<{ roleId: string; confidence: number } | { isMultiRole: boolean; roles: Array<{ roleId: string; confidence: number; executionOrder: number }>; reasoning: string }>>();

async function classifyWithDeduplication(
  prompt: string,
  roles: any[],
  classificationApiKey: string,
  customApiConfigIdFromRequest: string,
  messages: any[] = []
): Promise<{ roleId: string; confidence: number } | { isMultiRole: boolean; roles: Array<{ roleId: string; confidence: number; executionOrder: number }>; reasoning: string } | null> {
  const key = generateQueryHash(prompt);

  // Check if same classification is already in progress
  if (pendingClassifications.has(key)) {
    console.log(`[Request Deduplication] Waiting for existing classification: ${key}`);
    try {
      return await pendingClassifications.get(key)!;
    } catch (error) {
      console.warn('[Request Deduplication] Existing classification failed, will retry');
      pendingClassifications.delete(key);
    }
  }

  // Start new classification
  const classificationPromise = performFreshClassification(prompt, roles, classificationApiKey, customApiConfigIdFromRequest, messages);
  pendingClassifications.set(key, classificationPromise);

  try {
    const result = await classificationPromise;
    return result;
  } catch (error) {
    throw error;
  } finally {
    pendingClassifications.delete(key);
  }
}

// Phase 3 Helper: Perform fresh classification (extracted for deduplication)
async function performFreshClassification(
  prompt: string,
  roles: any[],
  classificationApiKey: string,
  customApiConfigIdFromRequest: string,
  messages: any[] = []
): Promise<{ roleId: string; confidence: number } | { isMultiRole: boolean; roles: Array<{ roleId: string; confidence: number; executionOrder: number }>; reasoning: string }> {
  // Extract conversation context for better classification
  const recentConversation = messages ? messages.slice(-3).map((m: any) =>
    `${m.role}: ${typeof m.content === 'string' ? m.content.substring(0, 200) :
    Array.isArray(m.content) ? m.content.find((p: any) => p.type === 'text')?.text?.substring(0, 200) || '[non-text]' : '[unknown]'}`
  ).join('\n') : '';

  // First, determine if this is a multi-role task
  const multiRoleResult = await detectMultiRoleTasks(prompt, roles, classificationApiKey, recentConversation);
  
  // If it's a multi-role task, return the detailed classification
  if (multiRoleResult.isMultiRole) {
    console.log(`[Multi-Role Classification] Detected ${multiRoleResult.roles.length} roles for prompt: "${prompt.substring(0, 100)}..."`);
    return multiRoleResult;
  }
  
  // Otherwise, proceed with single role classification (existing logic)
  const roleInfoForPrompt = roles.map(r => `- Role ID: "${r.id}", Name: "${r.name}", Description: "${(r.description || 'N/A').substring(0,150)}"`).join('\n');
  const classSystemPrompt = "You are an expert task classification system. Analyze the user's request considering both the current message AND recent conversation context. Key rules: 1) If user is responding to options/choices in an ongoing task (like '1, 2, 3' after coding options), continue with same role. 2) Only switch roles for clear new tasks ('write story', 'solve math', etc.). 3) Examples: 'write code'=coding roles, 'write story'=writing roles, 'solve math'=logic_reasoning. Respond with ONLY the Role ID string.";
  const classificationUserMessage = `Available Roles:\n${roleInfoForPrompt}\n\nRecent Conversation:\n${recentConversation}\n\nCurrent Request: "${prompt.substring(0, 2000)}"\n\nMost Appropriate Role ID:`;

  const openAIClassificationPayload = {
    model: 'gemini-2.0-flash-lite',
    messages: [
      { role: 'system', content: classSystemPrompt },
      { role: 'user', content: classificationUserMessage }
    ],
    temperature: 0.1,
    max_tokens: 50
  };

  const geminiResponse = await robustFetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${classificationApiKey}`,
      'Connection': 'keep-alive',
      'User-Agent': 'RoKey/1.0 (Classification-Optimized)',
      'Origin': 'https://rokey.app',
    },
    body: JSON.stringify(openAIClassificationPayload),
  }, 2, TIMEOUT_CONFIG.CLASSIFICATION); // Phase 5: Ultra-fast 3s timeout for classification

  if (!geminiResponse.ok) {
    throw new Error(`Gemini API error: ${geminiResponse.status}`);
  }

  const geminiResult = await geminiResponse.json();
  let classifiedRole = geminiResult.choices?.[0]?.message?.content?.trim().replace(/["'`]/g, '') || null;

  // Phase 2 Fix: Handle Gemini response variations
  if (classifiedRole) {
    // Remove common prefixes that Gemini might add
    classifiedRole = classifiedRole.replace(/^(Role ID:\s*|Role:\s*|Classification:\s*)/i, '').trim();

    // Try exact match first
    let matchedRole = roles.find(r => r.id === classifiedRole);

    // If no exact match, try case-insensitive matching
    if (!matchedRole) {
      matchedRole = roles.find(r => r.id.toLowerCase() === classifiedRole.toLowerCase());
    }

    // If still no match, try name-based matching
    if (!matchedRole) {
      matchedRole = roles.find(r => r.name && r.name.toLowerCase() === classifiedRole.toLowerCase());
    }

    if (matchedRole) {
      classifiedRole = matchedRole.id; // Use the correct role ID
      console.log(`[Classification Fix] Matched "${geminiResult.choices?.[0]?.message?.content?.trim()}" to role: ${classifiedRole}`);
    } else {
      console.log(`[Classification Fix] No match found for "${classifiedRole}" in available roles: ${roles.map(r => `${r.id}(${r.name || 'no name'})`).join(', ')}`);
      console.log(`[Classification Fix] Available role IDs: ${roles.map(r => r.id).join(', ')}`);
      console.log(`[Classification Fix] Available role names: ${roles.map(r => r.name || 'unnamed').join(', ')}`);

      // Phase 2 Fix: Be more lenient - if it's a reasonable role name, don't fail completely
      // Instead, let the calling function handle the fallback
      console.warn(`[Classification Fix] Classification returned unmatched role "${classifiedRole}", will attempt fallback routing`);
      return { roleId: classifiedRole, confidence: 0.5 }; // Lower confidence for unmatched roles
    }
  } else {
    throw new Error(`Empty classification result`);
  }

  return { roleId: classifiedRole, confidence: 0.95 };
}

// Enhanced function to detect if a prompt requires multiple roles
async function detectMultiRoleTasks(
  prompt: string,
  roles: any[],
  classificationApiKey: string,
  recentConversation: string = ''
): Promise<{ isMultiRole: boolean; roles: Array<{ roleId: string; confidence: number; executionOrder: number }>; reasoning: string }> {


  // Format role information for the prompt
  const roleInfoForPrompt = roles.map(r =>
    `- Role ID: "${r.id}", Name: "${r.name}", Description: "${(r.description || 'N/A').substring(0,150)}"`
  ).join('\n');
  
  // Create a balanced system prompt for multi-role detection
  const multiRoleSystemPrompt = `You are an expert task analyzer that determines whether a user request requires multiple specialized roles or can be handled by a single role.

IMPORTANT PRINCIPLES:
1. Analyze requests carefully to identify distinct tasks that require different specialized skills
2. Simple requests like "continue", "more", "help me", or single actions should be single-role
3. Multi-role is for requests that contain multiple distinct tasks with different skill requirements
4. Use ONLY role IDs from the available roles list - never make up role names

AVAILABLE ROLE IDS (use these exact IDs):
- general_chat: General conversation and Q&A
- logic_reasoning: Mathematical reasoning and problem-solving
- writing: Articles, blog posts, marketing copy, creative content
- coding_frontend: HTML, CSS, JavaScript, React, Vue, Angular, UI/UX
- coding_backend: Server-side logic, APIs, databases, Python, Node.js, Java
- research_synthesis: Information retrieval, data analysis, research reports
- summarization_briefing: Condensing texts into summaries
- translation_localization: Language translation and cultural adaptation
- data_extraction_structuring: Extracting and organizing information from text
- brainstorming_ideation: Creative idea generation and concept development
- education_tutoring: Explaining concepts and educational assistance
- image_generation: Creating images from descriptions
- audio_transcription: Converting speech to text

Examples of TRUE multi-role requests (multiple distinct tasks):
- "Brainstorm a game idea and then write the backend code for it" (brainstorming_ideation → coding_backend)
- "Brainstorm an idea for a simple snake game and give me the full script" (brainstorming_ideation → coding_backend)
- "Research the history of AI and write a blog post about it" (research_synthesis → writing)
- "Create a React component and write documentation for it" (coding_frontend → writing)
- "Build a REST API and create a frontend interface for it" (coding_backend → coding_frontend)

Examples of single-role requests (even if complex):
- "Write a Python API for user authentication" (just coding_backend)
- "Create a responsive navbar in React" (just coding_frontend)
- "Build a full-stack web app" (just coding_backend - this is one cohesive task)
- "Create a blog post about climate change" (just writing)
- "continue" (just general_chat)
- "more" (just general_chat)
- "explain this code" (just general_chat)
- "help me debug this JavaScript error" (just coding_frontend)

CODING ROLE GUIDELINES:
- coding_frontend: HTML, CSS, JavaScript, React, Vue, Angular, UI components, styling, client-side logic
- coding_backend: Python, Node.js, Java, APIs, databases, server logic, data processing, algorithms

Analyze the request thoughtfully - choose multi-role when the request contains multiple distinct tasks that require different specialized skills.

Respond in JSON format ONLY with no additional text:
{
  "isMultiRole": true/false,
  "roles": [
    {"roleId": "role_id1", "confidence": 0.9, "executionOrder": 1},
    {"roleId": "role_id2", "confidence": 0.8, "executionOrder": 2}
  ],
  "reasoning": "Detailed explanation of your analysis"
}`;

  const multiRoleUserMessage = `Available Roles:\n${roleInfoForPrompt}\n\nRecent Conversation:\n${recentConversation}\n\nUser Request: "${prompt.substring(0, 2000)}"\n\nAnalyze this request: Does it require multiple distinct specialized roles working together, or can it be handled by a single role?`;

  const openAIMultiRolePayload = {
    model: 'gemini-2.0-flash-lite',
    messages: [
      { role: 'system', content: multiRoleSystemPrompt },
      { role: 'user', content: multiRoleUserMessage }
    ],
    temperature: 0.3, // Slightly higher temperature for more creative role combinations
    max_tokens: 800, // Increased token limit for more detailed analysis
    response_format: { type: "json_object" }
  };

  try {
    const multiRoleResponse = await robustFetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
        'Connection': 'keep-alive',
        'User-Agent': 'RoKey/1.0 (Multi-Role-Detection)',
        'Origin': 'https://rokey.app',
      },
      body: JSON.stringify(openAIMultiRolePayload),
    }, 2, TIMEOUT_CONFIG.CLASSIFICATION);

    if (!multiRoleResponse.ok) {
      console.warn(`[Multi-Role Detection] API error: ${multiRoleResponse.status}, falling back to single-role classification`);
      return { isMultiRole: false, roles: [], reasoning: "API error during multi-role detection" };
    }

    const multiRoleResult = await multiRoleResponse.json();
    let parsedResult;
    
    try {
      // Extract the JSON content from the response
      const content = multiRoleResult.choices?.[0]?.message?.content;
      if (!content) {
        throw new Error("Empty response from multi-role detection");
      }
      
      // Parse the JSON response
      parsedResult = JSON.parse(content);
      
      // Validate the response structure
      if (typeof parsedResult.isMultiRole !== 'boolean' || !Array.isArray(parsedResult.roles)) {
        throw new Error("Invalid response structure from multi-role detection");
      }
      
      // If it's a multi-role task, validate each role
      if (parsedResult.isMultiRole && parsedResult.roles.length > 0) {
        // Validate and match role IDs against available roles
        parsedResult.roles = parsedResult.roles.map((role: any) => {
          // Find the matching role in our available roles
          const matchedRole = roles.find(r => 
            r.id === role.roleId || 
            r.id.toLowerCase() === role.roleId.toLowerCase() ||
            (r.name && r.name.toLowerCase() === role.roleId.toLowerCase())
          );
          
          if (matchedRole) {
            return {
              ...role,
              roleId: matchedRole.id, // Use the correct casing from our available roles
              confidence: typeof role.confidence === 'number' ? role.confidence : 0.8
            };
          }
          
          // If no match found, return null to filter out later
          return null;
        }).filter(Boolean); // Remove any null entries
        
        // If we have no valid roles after filtering, fall back to single-role
        if (parsedResult.roles.length === 0) {
          parsedResult.isMultiRole = false;
          parsedResult.reasoning = "No valid roles matched after filtering";
        }
      }
      
      return parsedResult;
    } catch (error) {
      console.warn(`[Multi-Role Detection] Error parsing result: ${error}, content: ${multiRoleResult.choices?.[0]?.message?.content}`);
      return { isMultiRole: false, roles: [], reasoning: "Error parsing multi-role detection result" };
    }
  } catch (error) {
    console.warn(`[Multi-Role Detection] Error: ${error}, falling back to single-role classification`);
    return { isMultiRole: false, roles: [], reasoning: "Error during multi-role detection" };
  }
}

// Phase 4 Optimization: Conversational context tracking for role continuity
const conversationContext = new Map<string, {
  lastClassifiedRole: string;
  messageCount: number;
  lastActivity: number;
  confidence: number;
  conversationId: string;
}>();
const CONVERSATION_CONTEXT_TTL = 30 * 60 * 1000; // 30 minutes

// Database-backed Synthesis Storage: Store synthesis in database for persistence across server restarts
const SYNTHESIS_STORAGE_TTL = 30 * 60 * 1000; // 30 minutes

// Helper function to store synthesis in database
async function storeSynthesisInDatabase(synthesisId: string, conversationId: string, completeSynthesis: string, chunks: string[]): Promise<void> {
  const supabase = await createSupabaseServerClientOnRequest();

  const { error } = await supabase
    .from('synthesis_storage')
    .upsert({
      synthesis_id: synthesisId,
      conversation_id: conversationId,
      complete_synthesis: completeSynthesis,
      chunks: chunks,
      total_chunks: chunks.length,
      created_at: new Date().toISOString(),
      last_access_time: new Date().toISOString()
    });

  if (error) {
    console.error(`[Synthesis Storage] Database error storing synthesis ${synthesisId}:`, error);
    throw new Error(`Failed to store synthesis: ${error.message}`);
  }
}

// Helper function to get synthesis from database
async function getSynthesisFromDatabase(synthesisId: string): Promise<{
  conversationId: string;
  completeSynthesis: string;
  chunks: string[];
  totalChunks: number;
  createdAt: number;
  lastAccessTime: number;
} | null> {
  const supabase = await createSupabaseServerClientOnRequest();

  const { data, error } = await supabase
    .from('synthesis_storage')
    .select('*')
    .eq('synthesis_id', synthesisId)
    .single();

  if (error || !data) {
    console.log(`[Synthesis Storage] Synthesis ${synthesisId} not found in database`);
    return null;
  }

  // Update last access time
  await supabase
    .from('synthesis_storage')
    .update({ last_access_time: new Date().toISOString() })
    .eq('synthesis_id', synthesisId);

  return {
    conversationId: data.conversation_id,
    completeSynthesis: data.complete_synthesis,
    chunks: data.chunks,
    totalChunks: data.total_chunks,
    createdAt: new Date(data.created_at).getTime(),
    lastAccessTime: new Date(data.last_access_time).getTime()
  };
}

// Helper function to find synthesis by conversation ID
async function findSynthesisByConversationId(conversationId: string): Promise<string | null> {
  const supabase = await createSupabaseServerClientOnRequest();

  const { data, error } = await supabase
    .from('synthesis_storage')
    .select('synthesis_id')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (error || !data) {
    return null;
  }

  return data.synthesis_id;
}

// Debug counter for tracking synthesis operations
let synthesisOperationCounter = 0;

// Chunking configuration for synthesis responses
const CHUNK_LIMITS = {
  MAX_TOKENS: 8000,        // Increased to handle larger synthesis responses
  MAX_CHARS: 70000,        // ~17,500 tokens worth - exceeds GPT-4o's 16,400 token limit!
  MIN_CHUNK_SIZE: 1000,    // Don't make tiny chunks
  SEMANTIC_BREAK_BONUS: 500, // Extra space for natural breaks
  CODE_BLOCK_MAX_CHARS: 70000 // Allow very large code blocks to be kept together
};

// Phase 4 Enhancement: Detect task transitions within existing context
function detectTaskTransition(
  prompt: string,
  currentRole: string
): { isTransition: boolean; newTaskRole: string | null; reasoning: string } {

  const lowerPrompt = prompt.toLowerCase().trim();

  // Strong task transition indicators that override context
  const taskTransitions = {
    'coding_frontend': [
      'write code', 'write python', 'write javascript', 'write java', 'write c++',
      'code this', 'program this', 'create a script', 'build an app', 'make a website',
      'write html', 'write css', 'write react', 'frontend code', 'client code',
      'create component', 'build interface', 'ui code', 'web development'
    ],
    'coding_backend': [
      'write api', 'create server', 'database code', 'backend code', 'server code',
      'write sql', 'create endpoint', 'api development', 'microservice',
      'write node', 'express code', 'django code', 'flask code'
    ],
    'data_analysis': [
      'analyze this data', 'create a chart', 'make a graph', 'data analysis',
      'statistical analysis', 'create visualization', 'data science', 'machine learning',
      'pandas code', 'numpy analysis', 'plot this', 'visualize data'
    ],
    'writing': [
      'write an article', 'write a blog', 'create content', 'write an essay',
      'write documentation', 'create copy', 'marketing content', 'blog post',
      'article about', 'essay on', 'content for'
    ],
    'translation': [
      'translate this', 'translate to', 'convert to language', 'in spanish',
      'in french', 'in german', 'in chinese', 'translate into'
    ],
    'summarization': [
      'summarize this', 'create summary', 'tldr', 'brief overview',
      'key points', 'main ideas', 'executive summary'
    ]
  };

  // Check for explicit task transitions
  for (const [newRole, patterns] of Object.entries(taskTransitions)) {
    if (newRole === currentRole) continue; // Skip if same role

    for (const pattern of patterns) {
      if (lowerPrompt.includes(pattern)) {
        return {
          isTransition: true,
          newTaskRole: newRole,
          reasoning: `explicit_task_transition: "${pattern}" -> ${newRole}`
        };
      }
    }
  }

  // Check for role-switching keywords that suggest task change
  const transitionKeywords = [
    'now', 'instead', 'switch to', 'change to', 'help me',
    'can you', 'please', 'i want you to', 'i need you to'
  ];

  const hasTransitionKeyword = transitionKeywords.some(keyword => lowerPrompt.includes(keyword));

  // Check for action verbs that might indicate new tasks
  const actionVerbs = [
    'create', 'build', 'make', 'develop', 'design', 'implement',
    'generate', 'produce', 'construct', 'craft', 'compose'
  ];

  const hasActionVerb = actionVerbs.some(verb => lowerPrompt.includes(verb));

  if (hasTransitionKeyword && hasActionVerb) {
    return {
      isTransition: true,
      newTaskRole: null, // Will need fresh classification
      reasoning: 'transition_keyword_with_action_verb'
    };
  }

  // Check for strong transition phrases
  const strongTransitionPhrases = [
    'now write', 'now create', 'now build', 'now make', 'now help',
    'instead write', 'instead create', 'can you write', 'can you create',
    'help me write', 'help me create', 'help me build'
  ];

  for (const phrase of strongTransitionPhrases) {
    if (lowerPrompt.includes(phrase)) {
      return {
        isTransition: true,
        newTaskRole: null, // Will need fresh classification
        reasoning: `strong_transition_phrase: "${phrase}"`
      };
    }
  }

  return { isTransition: false, newTaskRole: null, reasoning: 'no_transition_detected' };
}

// Phase 4 Helper: Detect if current prompt is a continuation of previous context
function isContextContinuation(
  prompt: string,
  lastRole: string,
  messageHistory: any[],
  timeSinceLastMessage: number
): { isContinuation: boolean; confidence: number; reasoning: string } {

  const lowerPrompt = prompt.toLowerCase().trim();

  // UNIVERSAL CONTINUATION DETECTION - Bypass orchestration for synthesis continuation
  // Check if this is a direct synthesis continuation request
  const universalContinuationPatterns = [
    'continue', 'continue please', 'keep going', 'go on', 'more', 'more please',
    'finish', 'complete', 'what next', 'then what', 'and then'
  ];

  // Check if the previous message was likely a synthesis response that got truncated
  const lastAssistantMessage = messageHistory
    .slice()
    .reverse()
    .find(msg => msg.role === 'assistant');

  const hasTruncationIndicators = lastAssistantMessage && typeof lastAssistantMessage.content === 'string' && (
    lastAssistantMessage.content.includes('[SYNTHESIS CONTINUES') ||
    lastAssistantMessage.content.includes('**[SYNTHESIS CONTINUES') ||
    lastAssistantMessage.content.includes('The response will continue') ||
    lastAssistantMessage.content.length > 1500 // Long response likely truncated
  );

  // If this looks like a synthesis continuation, treat it as high-confidence continuation
  if (universalContinuationPatterns.includes(lowerPrompt) &&
      timeSinceLastMessage < 600000 && // Within 10 minutes
      hasTruncationIndicators) {
    return {
      isContinuation: true,
      confidence: 0.98, // Very high confidence
      reasoning: 'universal_synthesis_continuation'
    };
  }

  // Also check for very short continuation prompts after recent messages
  if (universalContinuationPatterns.includes(lowerPrompt) &&
      timeSinceLastMessage < 120000) { // Within 2 minutes
    return {
      isContinuation: true,
      confidence: 0.85,
      reasoning: 'universal_short_continuation'
    };
  }

  // Define continuation patterns for each role type
  const continuationPatterns = {
    'StoryTeller': {
      strong: ['continue', 'what happens next', 'keep going', 'more story', 'then what'],
      medium: ['be creative', 'more', 'and then', 'what about', 'tell me more'],
      weak: ['go on', 'next', 'more please', 'continue please']
    },
    'coding_frontend': {
      strong: ['fix this', 'debug this', 'improve this code', 'add feature'],
      medium: ['make it better', 'optimize', 'refactor', 'enhance'],
      weak: ['change', 'update', 'modify']
    },
    'coding_backend': {
      strong: ['fix this', 'debug this', 'improve this code', 'add feature'],
      medium: ['make it better', 'optimize', 'refactor', 'enhance'],
      weak: ['change', 'update', 'modify']
    },
    'writing': {
      strong: ['revise this', 'edit this', 'improve this', 'rewrite this'],
      medium: ['make it better', 'enhance', 'polish'],
      weak: ['change', 'update', 'fix']
    },
    'data_analysis': {
      strong: ['analyze more', 'deeper analysis', 'what else', 'more insights'],
      medium: ['explain further', 'elaborate', 'more details'],
      weak: ['continue', 'more']
    }
  };

  const rolePatterns = continuationPatterns[lastRole as keyof typeof continuationPatterns];
  if (!rolePatterns) {
    return { isContinuation: false, confidence: 0, reasoning: 'no_patterns_for_role' };
  }

  // Check for strong continuation indicators
  for (const pattern of rolePatterns.strong) {
    if (lowerPrompt.includes(pattern)) {
      return {
        isContinuation: true,
        confidence: 0.95,
        reasoning: `strong_pattern_match: "${pattern}"`
      };
    }
  }

  // Check for medium continuation indicators
  for (const pattern of rolePatterns.medium) {
    if (lowerPrompt.includes(pattern)) {
      return {
        isContinuation: true,
        confidence: 0.8,
        reasoning: `medium_pattern_match: "${pattern}"`
      };
    }
  }

  // Check for weak continuation indicators (only if recent)
  if (timeSinceLastMessage < 120000) { // Within 2 minutes
    for (const pattern of rolePatterns.weak) {
      if (lowerPrompt.includes(pattern)) {
        return {
          isContinuation: true,
          confidence: 0.6,
          reasoning: `weak_pattern_match: "${pattern}" (recent)`
        };
      }
    }
  }

  // Check for very short prompts that are likely continuations
  if (lowerPrompt.length < 20 && timeSinceLastMessage < 300000) { // 5 minutes
    const shortContinuationWords = ['yes', 'no', 'ok', 'sure', 'please', 'thanks', 'more', 'again'];
    if (shortContinuationWords.some(word => lowerPrompt.includes(word))) {
      return {
        isContinuation: true,
        confidence: 0.7,
        reasoning: 'short_continuation_prompt'
      };
    }
  }

  // Check conversation recency for implicit continuation
  if (timeSinceLastMessage < 60000) { // Within 1 minute - very likely continuation
    return {
      isContinuation: true,
      confidence: 0.65,
      reasoning: 'very_recent_message'
    };
  }

  if (timeSinceLastMessage < 300000) { // Within 5 minutes - possibly continuation
    return {
      isContinuation: true,
      confidence: 0.4,
      reasoning: 'recent_message'
    };
  }

  return { isContinuation: false, confidence: 0, reasoning: 'no_continuation_detected' };
}

// Phase 4 Helper: Generate conversation ID from messages for context tracking
function generateConversationId(messages: any[]): string {
  // Use the first user message as the conversation anchor
  const firstUserMessage = messages.find(m => m.role === 'user');

  if (firstUserMessage && typeof firstUserMessage.content === 'string') {
    // Create a stable hash from the first user message
    const contentHash = crypto.createHash('md5')
      .update(firstUserMessage.content.substring(0, 200)) // Use more content for better uniqueness
      .digest('hex')
      .substring(0, 12); // Longer hash for better uniqueness
    return `conv_${contentHash}`;
  }

  // Fallback for non-string content
  return `conv_${Date.now()}`;
}

// Phase 4 Helper: Get conversation ID with fallback to previous context
function getConversationIdWithFallback(messages: any[]): string {
  const baseId = generateConversationId(messages);

  // Check if we have a similar conversation in progress
  for (const [existingId, context] of conversationContext.entries()) {
    if (existingId.startsWith(baseId.substring(0, 15))) { // Match first part of hash
      const timeSinceLastActivity = Date.now() - context.lastActivity;
      if (timeSinceLastActivity < CONVERSATION_CONTEXT_TTL) {
        return existingId; // Use existing conversation ID
      }
    }
  }

  return baseId;
}

// Phase 4 Helper: Clean up expired conversation contexts
function cleanupConversationContexts() {
  const now = Date.now();
  for (const [key, context] of conversationContext.entries()) {
    if ((now - context.lastActivity) > CONVERSATION_CONTEXT_TTL) {
      conversationContext.delete(key);
    }
  }
}

// Synthesis Helper: Clean up expired synthesis storage from database
async function cleanupSynthesisStorage() {
  console.log(`[Synthesis Storage] Running database cleanup - removing expired synthesis`);

  const supabase = await createSupabaseServerClientOnRequest();
  const cutoffTime = new Date(Date.now() - SYNTHESIS_STORAGE_TTL).toISOString();

  const { data: expiredSynthesis, error: selectError } = await supabase
    .from('synthesis_storage')
    .select('synthesis_id')
    .lt('last_access_time', cutoffTime);

  if (selectError) {
    console.error(`[Synthesis Storage] Error finding expired synthesis:`, selectError);
    return;
  }

  if (!expiredSynthesis || expiredSynthesis.length === 0) {
    console.log(`[Synthesis Storage] No expired synthesis found`);
    return;
  }

  console.log(`[Synthesis Storage] Found ${expiredSynthesis.length} expired synthesis entries`);

  const { error: deleteError } = await supabase
    .from('synthesis_storage')
    .delete()
    .lt('last_access_time', cutoffTime);

  if (deleteError) {
    console.error(`[Synthesis Storage] Error deleting expired synthesis:`, deleteError);
  } else {
    synthesisOperationCounter++;
    console.log(`[Synthesis Storage] #${synthesisOperationCounter} DELETED ${expiredSynthesis.length} expired synthesis entries`);
  }
}

// Synthesis Helper: Markdown-aware smart chunking with code block preservation
function chunkSynthesisResponse(synthesisContent: string): string[] {
  const chunks: string[] = [];
  let currentChunk = '';
  let currentLength = 0;
  let inCodeBlock = false;
  let codeBlockLanguage = '';

  // Split by paragraphs first (double newlines)
  const paragraphs = synthesisContent.split('\n\n');

  for (let i = 0; i < paragraphs.length; i++) {
    const paragraph = paragraphs[i];
    const paragraphLength = paragraph.length;

    // Check for code block markers
    const codeBlockStart = paragraph.match(/^```(\w+)?/);
    const codeBlockEnd = paragraph.match(/```\s*$/);
    const hasCodeBlockStart = !!codeBlockStart;
    const hasCodeBlockEnd = !!codeBlockEnd;

    // Update code block state
    if (hasCodeBlockStart && !inCodeBlock) {
      inCodeBlock = true;
      codeBlockLanguage = codeBlockStart[1] || '';
      console.log(`[Chunking] Starting code block: ${codeBlockLanguage}`);
    } else if (hasCodeBlockEnd && inCodeBlock) {
      inCodeBlock = false;
      console.log(`[Chunking] Ending code block`);
    }

    // Check if adding this paragraph would exceed limits
    const wouldExceedLimit = (currentLength + paragraphLength + 2) > CHUNK_LIMITS.MAX_CHARS; // +2 for \n\n
    const wouldExceedCodeBlockLimit = (currentLength + paragraphLength + 2) > CHUNK_LIMITS.CODE_BLOCK_MAX_CHARS;

    // NEVER break inside a code block - always keep code blocks together (with increased limit)
    if (inCodeBlock && wouldExceedLimit && !wouldExceedCodeBlockLimit && currentChunk.length > CHUNK_LIMITS.MIN_CHUNK_SIZE) {
      console.log(`[Chunking] Would exceed normal limit but inside code block - keeping together (${currentLength + paragraphLength + 2} chars)`);
      // If we're in a code block, we must include this paragraph even if it exceeds the normal limit
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      currentLength += paragraphLength + (currentChunk ? 2 : 0);
    } else if (inCodeBlock && wouldExceedCodeBlockLimit) {
      console.log(`[Chunking] Code block exceeds maximum limit (${currentLength + paragraphLength + 2} chars) - forcing chunk break`);
      // Even for code blocks, we have a hard limit to prevent memory issues
      chunks.push(currentChunk.trim());
      console.log(`[Chunking] Created chunk ${chunks.length}: ${currentChunk.length} chars (forced break in large code block)`);
      currentChunk = paragraph;
      currentLength = paragraphLength;
    } else if (wouldExceedLimit && currentChunk.length > CHUNK_LIMITS.MIN_CHUNK_SIZE && !inCodeBlock) {
      // Safe to break here - we're not in a code block
      chunks.push(currentChunk.trim());
      console.log(`[Chunking] Created chunk ${chunks.length}: ${currentChunk.length} chars (broke at paragraph boundary)`);
      currentChunk = paragraph;
      currentLength = paragraphLength;
    } else if (wouldExceedLimit && currentChunk.length <= CHUNK_LIMITS.MIN_CHUNK_SIZE && !inCodeBlock) {
      // Current chunk is too small, but adding paragraph would exceed limit
      // Try to split the paragraph at sentence boundaries (only if not in code block)
      const sentences = paragraph.split(/(?<=[.!?])\s+/);

      for (const sentence of sentences) {
        if ((currentLength + sentence.length + 1) > CHUNK_LIMITS.MAX_CHARS && currentChunk.length > CHUNK_LIMITS.MIN_CHUNK_SIZE) {
          chunks.push(currentChunk.trim());
          console.log(`[Chunking] Created chunk ${chunks.length}: ${currentChunk.length} chars (broke at sentence boundary)`);
          currentChunk = sentence;
          currentLength = sentence.length;
        } else {
          currentChunk += (currentChunk ? ' ' : '') + sentence;
          currentLength += sentence.length + (currentChunk ? 1 : 0);
        }
      }
    } else {
      // Safe to add paragraph to current chunk
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      currentLength += paragraphLength + (currentChunk ? 2 : 0);
    }
  }

  // Add the last chunk if it has content
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
    console.log(`[Chunking] Created final chunk ${chunks.length}: ${currentChunk.length} chars`);
  }

  // Ensure we don't have empty chunks
  const validChunks = chunks.filter(chunk => chunk.trim().length > 0);

  // Post-process chunks to ensure proper markdown formatting
  const formattedChunks = validChunks.map((chunk, index) => {
    let formattedChunk = chunk;

    // If chunk doesn't start with a code block but contains code, check if we need to add opening
    const startsWithCode = formattedChunk.trim().startsWith('```');
    const containsCode = formattedChunk.includes('```');

    if (!startsWithCode && containsCode && index > 0) {
      // Check if previous chunk ended with an unclosed code block
      const prevChunk = validChunks[index - 1];
      const prevCodeBlocks = (prevChunk.match(/```/g) || []).length;
      if (prevCodeBlocks % 2 === 1) {
        // Previous chunk has unclosed code block, this chunk should continue it
        const prevCodeLang = prevChunk.match(/```(\w+)?/);
        const language = prevCodeLang ? prevCodeLang[1] || '' : '';
        formattedChunk = '```' + language + '\n' + formattedChunk;
        console.log(`[Chunking] Added code block continuation to chunk ${index + 1}`);
      }
    }

    return formattedChunk;
  });

  console.log(`[Synthesis Chunking] Split ${synthesisContent.length} chars into ${formattedChunks.length} chunks`);
  formattedChunks.forEach((chunk, index) => {
    const codeBlocks = (chunk.match(/```/g) || []).length;
    console.log(`[Synthesis Chunking] Chunk ${index + 1}: ${chunk.length} chars, ${Math.floor(codeBlocks/2)} code blocks`);
  });

  return formattedChunks;
}

// Synthesis Helper: Get next chunk from stored content
function getNextSynthesisChunk(content: string, streamedLength: number): {
  chunk: string;
  newStreamedLength: number;
  hasMore: boolean;
  progress: string;
} {
  const remainingContent = content.substring(streamedLength);
  const remainingLength = remainingContent.length;

  if (remainingLength === 0) {
    return {
      chunk: '',
      newStreamedLength: streamedLength,
      hasMore: false,
      progress: 'complete'
    };
  }

  // Determine chunk size
  let chunkSize = Math.min(CHUNK_LIMITS.MAX_CHARS, remainingLength);

  // If remaining content after this chunk would be too small, include it all
  if (remainingLength - chunkSize < CHUNK_LIMITS.MIN_CHUNK_SIZE) {
    chunkSize = remainingLength;
  }

  // Try to break at a natural point (paragraph or sentence)
  let chunk = remainingContent.substring(0, chunkSize);

  // If we're not taking all remaining content, try to find a good break point
  if (chunkSize < remainingLength) {
    // Look for paragraph break
    const lastParagraphBreak = chunk.lastIndexOf('\n\n');
    if (lastParagraphBreak > chunkSize * 0.7) { // At least 70% of desired chunk size
      chunk = chunk.substring(0, lastParagraphBreak + 2);
    } else {
      // Look for sentence break
      const lastSentenceBreak = chunk.lastIndexOf('. ');
      if (lastSentenceBreak > chunkSize * 0.8) { // At least 80% of desired chunk size
        chunk = chunk.substring(0, lastSentenceBreak + 2);
      }
    }
  }

  const newStreamedLength = streamedLength + chunk.length;
  const hasMore = newStreamedLength < content.length;
  const progress = `${newStreamedLength}/${content.length} chars`;

  console.log(`[Synthesis Streaming] Streaming chunk: ${chunk.length} chars, total streamed: ${newStreamedLength}/${content.length}, hasMore: ${hasMore}`);

  return {
    chunk,
    newStreamedLength,
    hasMore,
    progress
  };
}

// Synthesis Helper: Store complete synthesis for database-backed chunked streaming
async function storeSynthesisForChunking(conversationId: string, completeSynthesis: string): Promise<string> {
  const chunks = chunkSynthesisResponse(completeSynthesis);
  const synthesisId = `synthesis_${conversationId}_${Date.now()}`;

  try {
    await storeSynthesisInDatabase(synthesisId, conversationId, completeSynthesis, chunks);

    synthesisOperationCounter++;
    console.log(`[Synthesis Storage] #${synthesisOperationCounter} CREATED synthesis ${synthesisId} for conversation ${conversationId} in database`);
    console.log(`[Synthesis Storage] Stored synthesis ${synthesisId} with ${chunks.length} chunks`);

    // Debug: Verify storage immediately
    const storedSynthesis = await getSynthesisFromDatabase(synthesisId);
    if (storedSynthesis) {
      console.log(`[Synthesis Storage] Verification: Successfully stored and retrieved synthesis ${synthesisId}`);
      console.log(`[Synthesis Storage] Database storage verified for synthesis ${synthesisId}`);
    } else {
      console.log(`[Synthesis Storage] ERROR: Failed to verify synthesis ${synthesisId} in database`);
    }

    return synthesisId;
  } catch (error) {
    console.error(`[Synthesis Storage] ERROR: Failed to store synthesis ${synthesisId}:`, error);
    throw error;
  }
}

// Synthesis Helper: Get specific chunk by index (database-backed)
async function getSynthesisChunk(synthesisId: string, chunkIndex: number): Promise<{
  chunk: string | null;
  isComplete: boolean;
  progress: string;
  totalChunks: number;
  synthesisId: string;
}> {
  const synthesis = await getSynthesisFromDatabase(synthesisId);

  if (!synthesis) {
    console.log(`[Synthesis Storage] Synthesis ${synthesisId} not found in database`);
    return {
      chunk: null,
      isComplete: true,
      progress: 'not_found',
      totalChunks: 0,
      synthesisId
    };
  }

  if (chunkIndex >= synthesis.totalChunks) {
    console.log(`[Synthesis Storage] Synthesis ${synthesisId} chunk ${chunkIndex} out of range (total: ${synthesis.totalChunks})`);
    return {
      chunk: null,
      isComplete: true,
      progress: 'complete',
      totalChunks: synthesis.totalChunks,
      synthesisId
    };
  }

  const chunk = synthesis.chunks[chunkIndex];
  const isComplete = (chunkIndex + 1) >= synthesis.totalChunks;
  const progress = `${chunkIndex + 1}/${synthesis.totalChunks}`;

  console.log(`[Synthesis Storage] Retrieved chunk ${progress} from ${synthesisId} (${chunk.length} chars)`);

  // If this is the last chunk, schedule cleanup
  if (isComplete) {
    setTimeout(async () => {
      console.log(`[Synthesis Storage] Cleaning up completed synthesis: ${synthesisId}`);
      const supabase = await createSupabaseServerClientOnRequest();
      await supabase.from('synthesis_storage').delete().eq('synthesis_id', synthesisId);
      synthesisOperationCounter++;
      console.log(`[Synthesis Storage] #${synthesisOperationCounter} DELETED completed synthesis: ${synthesisId}`);
    }, 30000); // 30 second delay to allow for any final requests
  }

  return {
    chunk,
    isComplete,
    progress,
    totalChunks: synthesis.totalChunks,
    synthesisId
  };
}

// Synthesis Helper: Check if synthesis exists for conversation (database-backed)
async function hasSynthesisChunks(conversationId: string): Promise<string | null> {
  console.log(`[Synthesis Lookup] Looking for synthesis for conversation: "${conversationId}"`);

  try {
    const synthesisId = await findSynthesisByConversationId(conversationId);

    if (synthesisId) {
      console.log(`[Synthesis Lookup] Found synthesis: ${synthesisId} for conversation "${conversationId}"`);
      return synthesisId;
    } else {
      console.log(`[Synthesis Lookup] No synthesis found for conversation: "${conversationId}"`);
      return null;
    }
  } catch (error) {
    console.error(`[Synthesis Lookup] Database error looking for synthesis:`, error);
    return null;
  }
}

// Synthesis Helper: Check if synthesis exists (for any purpose) - database-backed
async function hasSynthesisData(conversationId: string): Promise<{ synthesisId: string; isComplete: boolean } | null> {
  try {
    const synthesisId = await findSynthesisByConversationId(conversationId);
    if (synthesisId) {
      return { synthesisId, isComplete: false }; // Always allow continuation with database-backed approach
    }
    return null;
  } catch (error) {
    console.error(`[Synthesis Lookup] Database error checking synthesis data:`, error);
    return null;
  }
}

// Phase 3 Optimization: Initialize background cache warming on startup
let cacheWarmingInitialized = false;
const initializeBackgroundCacheWarming = () => {
  if (cacheWarmingInitialized) return;
  cacheWarmingInitialized = true;

  // Warm common patterns after a short delay
  setTimeout(() => {
    backgroundCacheWarmer.warmCommonPatterns().catch(err =>
      console.warn('[Background Warmer] Error warming common patterns:', err)
    );
  }, 5000); // 5 second delay to not interfere with startup

  // Phase 4: Clean up conversation contexts periodically
  setInterval(cleanupConversationContexts, 10 * 60 * 1000); // Every 10 minutes

  console.log('[Background Warmer] Initialized background cache warming and context cleanup');
};

// Phase 1 Optimization: Removed pattern-based classification to preserve LLM accuracy
// Pattern matching was too simplistic and missed nuanced role distinctions

// Phase 2 Optimization: Advanced response caching for LLM responses
const responseCache = new Map<string, {
  response: any;
  timestamp: number;
  provider: string;
  model: string;
}>();
const RESPONSE_CACHE_TTL = 2 * 60 * 1000; // 2 minutes for LLM responses
const MAX_CACHE_SIZE = 1000; // Prevent memory bloat

// Phase 3 Optimization: Enhanced fast-path routing cache with longer TTL
const fastPathCache = new Map<string, {
  provider: string;
  model: string;
  apiKey: string;
  timestamp: number;
}>();
const FAST_PATH_TTL = 30 * 60 * 1000; // 30 minutes for routing decisions (3x longer for better hit rate)



// Phase 2: LRU cache cleanup
function cleanupResponseCache() {
  if (responseCache.size > MAX_CACHE_SIZE) {
    const entries = Array.from(responseCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    // Remove oldest 20% of entries
    const toRemove = Math.floor(entries.length * 0.2);
    for (let i = 0; i < toRemove; i++) {
      responseCache.delete(entries[i][0]);
    }
  }
}

function generateQueryHash(query: string): string {
  return crypto.createHash('md5').update(query.toLowerCase().trim()).digest('hex');
}

// Phase 2: Generate cache key for LLM responses
function generateResponseCacheKey(messages: any[], model: string, temperature?: number): string {
  const messageText = messages.map(m => `${m.role}:${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`).join('|');
  const cacheInput = `${model}|${messageText}|${temperature || 0}`;
  return crypto.createHash('md5').update(cacheInput).digest('hex');
}



// Phase 4: System message compression for faster transmission
function compressSystemMessage(systemMessage: string): string {
  // Remove excessive whitespace and newlines while preserving structure
  return systemMessage
    .replace(/\n{3,}/g, '\n\n') // Reduce multiple newlines to max 2
    .replace(/[ \t]{2,}/g, ' ') // Reduce multiple spaces to single space
    .replace(/\n[ \t]+/g, '\n') // Remove leading whitespace on lines
    .trim();
}











// Phase 1 Optimization: Enhanced helper function for immediate response handling
function handleSuccessfulResponse(
  providerResult: ProviderCallResult,
  parsedBody: z.infer<typeof RoKeyChatCompletionRequestSchema>,
  provider?: string,
  model?: string,
  apiKey?: string,
  routingInfo?: {
    roleUsed?: string;
    routingStrategy?: string;
    complexityLevel?: number;
    processingTime?: number;
  }
): Response | NextResponse {
  // Phase 1: Performance tracking
  console.log('⚡ [BACKEND] Handling successful response - returning immediately');

  // Phase 3: Cache successful routing decision for fast-path (background operation)
  if (provider && model && apiKey && parsedBody.custom_api_config_id) {
    const fastPathKey = `${parsedBody.custom_api_config_id}:${parsedBody.messages?.[parsedBody.messages.length - 1]?.content?.substring(0, 100) || 'default'}`;

    // Phase 1: Cache in background, don't block response
    setImmediate(() => {
      fastPathCache.set(fastPathKey, {
        provider,
        model,
        apiKey,
        timestamp: Date.now()
      });
      console.log(`[FastPath] Cached routing decision for future requests`);
    });
  }

  // Prepare routing headers for frontend status tracking
  const routingHeaders: Record<string, string> = {};
  if (routingInfo?.roleUsed) routingHeaders['X-RoKey-Role-Used'] = routingInfo.roleUsed;
  if (routingInfo?.routingStrategy) routingHeaders['X-RoKey-Routing-Strategy'] = routingInfo.routingStrategy;
  if (routingInfo?.complexityLevel) routingHeaders['X-RoKey-Complexity-Level'] = routingInfo.complexityLevel.toString();
  if (provider) routingHeaders['X-RoKey-API-Key-Provider'] = provider;
  if (routingInfo?.processingTime) routingHeaders['X-RoKey-Processing-Time'] = `${routingInfo.processingTime}ms`;

  // Return appropriate response based on streaming vs non-streaming
  if (parsedBody.stream && providerResult.response) {
    // For streaming responses, we need to clone the response and add headers
    const originalHeaders = Object.fromEntries(providerResult.response.headers.entries());
    const enhancedHeaders = { ...originalHeaders, ...routingHeaders };

    return new Response(providerResult.response.body, {
      status: providerResult.response.status,
      headers: enhancedHeaders
    });
  } else if (!parsedBody.stream && providerResult.responseData !== undefined) {
    const originalHeaders = providerResult.responseHeaders || {};
    const enhancedHeaders = { ...originalHeaders, ...routingHeaders };

    return NextResponse.json(providerResult.responseData, {
      status: providerResult.status || 200,
      headers: enhancedHeaders
    });
  } else {
    throw new Error('Invalid provider result: no response data available');
  }
}

// Phase 1 Optimization: Cache cleanup to prevent memory leaks
function cleanupExpiredCaches() {
  const now = Date.now();

  // Clean training data cache (now handled by shared cache)
  trainingDataCache.cleanup();

  // Clean routing cache
  for (const [key, value] of routingCache.entries()) {
    if ((now - value.timestamp) > ROUTING_CACHE_TTL) {
      routingCache.delete(key);
    }
  }
}

// Run cleanup every 10 minutes
setInterval(cleanupExpiredCaches, 600000);

// Phase 1 Optimization: Use shared training data cache for cross-endpoint invalidation
import { trainingDataCache } from '@/lib/cache/trainingCache';

// Phase 2 Optimization: Preload complete user role ecosystem for faster access
async function preloadUserRoleEcosystem(userId: string, configId: string, supabase: any): Promise<{
  customRoles: any[];
  roleAssignments: any[];
  apiKeys: any[];
} | null> {
  const cacheKey = `${userId}_${configId}`;
  const cached = userRoleEcosystemCache.get(cacheKey);

  if (cached && (Date.now() - cached.timestamp) < USER_ECOSYSTEM_CACHE_TTL) {
    console.log(`[User Ecosystem] Using cached role ecosystem for user ${userId}`);
    return {
      customRoles: cached.customRoles,
      roleAssignments: cached.roleAssignments,
      apiKeys: cached.apiKeys
    };
  }

  console.log(`[User Ecosystem] Preloading role ecosystem for user ${userId}...`);

  try {
    // Phase 2: Parallel loading of complete user role ecosystem
    const [customRolesResult, roleAssignmentsResult, apiKeysResult] = await Promise.allSettled([
      supabase.from('user_custom_roles').select('role_id, name, description').eq('user_id', userId),
      supabase.from('api_key_role_assignments').select('role_name, api_key_id').eq('custom_api_config_id', configId),
      supabase.from('api_keys').select('*').eq('custom_api_config_id', configId).eq('status', 'active')
    ]);

    const customRoles = customRolesResult.status === 'fulfilled' ? customRolesResult.value.data || [] : [];
    const roleAssignments = roleAssignmentsResult.status === 'fulfilled' ? roleAssignmentsResult.value.data || [] : [];
    const apiKeys = apiKeysResult.status === 'fulfilled' ? apiKeysResult.value.data || [] : [];

    // Cache the complete ecosystem
    userRoleEcosystemCache.set(cacheKey, {
      customRoles,
      roleAssignments,
      apiKeys,
      timestamp: Date.now()
    });

    console.log(`[User Ecosystem] Cached ecosystem: ${customRoles.length} custom roles, ${roleAssignments.length} assignments, ${apiKeys.length} keys`);

    return { customRoles, roleAssignments, apiKeys };
  } catch (error) {
    console.error('[User Ecosystem] Error preloading role ecosystem:', error);
    return null;
  }
}

async function loadTrainingData(customApiConfigId: string): Promise<{ trainingData: any; trainingJobId: string } | null> {
  try {
    // Check shared cache first
    const cached = trainingDataCache.get(customApiConfigId);
    if (cached) {
      console.log(`[Training Enhancement] Using cached training data for config: ${customApiConfigId}`);
      return { trainingData: cached.data, trainingJobId: cached.jobId };
    }

    const supabase = await createSupabaseServerClientOnRequest();

    const { data: trainingResult, error } = await supabase
      .from('training_jobs')
      .select('id, training_data, created_at')
      .eq('custom_api_config_id', customApiConfigId)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.warn('Error loading training data:', error);
      return null;
    }

    if (!trainingResult?.training_data) {
      return null;
    }

    // Cache the result using shared cache
    trainingDataCache.set(customApiConfigId, trainingResult.training_data, trainingResult.id);

    return { trainingData: trainingResult.training_data, trainingJobId: trainingResult.id };
  } catch (error) {
    console.warn('Error loading training data:', error);
    return null;
  }
}



// Helper function to format source attribution
function formatSourceAttribution(sources: Array<{ filename: string; document_id: string; similarity: number }>): string {
  if (sources.length === 0) return '';

  const uniqueSources = sources.reduce((acc, source) => {
    if (!acc.find(s => s.filename === source.filename)) {
      acc.push(source);
    }
    return acc;
  }, [] as typeof sources);

  const sourceList = uniqueSources
    .map((source, index) => `${index + 1}. ${source.filename} (${Math.round(source.similarity * 100)}% match)`)
    .join('\n');

  return `\n\n---\n**Sources:**\n${sourceList}`;
}

// RAG Document Search: Search uploaded documents for relevant context
async function searchDocuments(query: string, configId: string, userId: string, supabase: any): Promise<{
  context: string;
  sources: Array<{ filename: string; document_id: string; similarity: number }>;
}> {
  try {
    // Import Jina embeddings for RAG search
    const { jinaEmbeddings } = await import('@/lib/embeddings/jina');

    // Generate embedding for the search query using Jina v3
    const queryEmbedding = await jinaEmbeddings.embedQuery(query);

    // Search for similar document chunks with improved parameters
    let { data: results, error } = await supabase.rpc('search_document_chunks', {
      query_embedding: queryEmbedding,
      config_id: configId,
      user_id_param: userId,
      match_threshold: 0.5,  // Lowered from 0.7 to 0.5 for better recall
      match_count: 8         // Increased from 3 to 8 for more context
    });

    console.log(`[RAG Search] Query: "${query}"`);
    console.log(`[RAG Search] Search parameters: threshold=0.5, count=8`);

    if (error) {
      console.warn('[RAG Search] Error searching documents:', error);
      return { context: '', sources: [] };
    }

    if (!results || results.length === 0) {
      console.log('[RAG Search] No results with threshold 0.5, trying with lower threshold 0.3');

      // Fallback search with lower threshold
      const { data: fallbackResults, error: fallbackError } = await supabase.rpc('search_document_chunks', {
        query_embedding: queryEmbedding,
        config_id: configId,
        user_id_param: userId,
        match_threshold: 0.3,  // Much lower threshold for fallback
        match_count: 5
      });

      if (fallbackError || !fallbackResults || fallbackResults.length === 0) {
        console.log('[RAG Search] No relevant documents found even with lower threshold');
        return { context: '', sources: [] };
      }

      console.log(`[RAG Search] Fallback found ${fallbackResults.length} chunks with threshold 0.3`);
      results = fallbackResults;
    }

    console.log(`[RAG Search] Found ${results.length} relevant document chunks`);

    // Log detailed information about retrieved chunks
    results.forEach((result: any, index: number) => {
      console.log(`[RAG Search] Chunk ${index + 1}: similarity=${result.similarity.toFixed(3)}, length=${result.content.length} chars`);
      console.log(`[RAG Search] Preview: "${result.content.substring(0, 150)}..."`);
    });

    // Get document metadata for source attribution
    const documentIds = [...new Set(results.map((r: any) => r.document_id))];
    const { data: documents } = await supabase
      .from('documents')
      .select('id, filename')
      .in('id', documentIds);

    // Format the results into context and collect sources
    const contextChunks = results.map((result: any, index: number) => {
      return `[Document ${index + 1} - Similarity: ${result.similarity.toFixed(3)}]\n${result.content.trim()}`;
    });

    const sources = results.map((result: any) => {
      const document = documents?.find((d: any) => d.id === result.document_id);
      return {
        filename: document?.filename || 'Unknown Document',
        document_id: result.document_id,
        similarity: Math.round(result.similarity * 100) / 100
      };
    });

    return {
      context: contextChunks.join('\n\n'),
      sources: sources
    };
  } catch (error) {
    console.warn('[RAG Search] Error during document search:', error);
    return { context: '', sources: [] };
  }
}

// Phase 1 Optimization: Enhanced training data injection with RAG
async function injectTrainingData(messages: any[], trainingData: any) {
  if (!trainingData || !trainingData.processed_prompts) {
    return messages;
  }

  const { processed_prompts } = trainingData;

  // Build enhanced system prompt
  let systemPrompt = '';

  // Add system instructions
  if (processed_prompts.system_instructions?.trim()) {
    systemPrompt += `${processed_prompts.system_instructions.trim()}\n\n`;
  }

  // Add general instructions
  if (processed_prompts.general_instructions?.trim()) {
    systemPrompt += `${processed_prompts.general_instructions.trim()}\n\n`;
  }

  // Add behavior guidelines
  if (processed_prompts.behavior_guidelines?.trim()) {
    systemPrompt += `## Behavior Guidelines:\n${processed_prompts.behavior_guidelines.trim()}\n\n`;
  }

  // Add training examples
  if (processed_prompts.examples && processed_prompts.examples.length > 0) {
    systemPrompt += `## Training Examples:\n`;
    processed_prompts.examples.forEach((example: any, index: number) => {
      systemPrompt += `Example ${index + 1}:\nUser: ${example.input}\nAssistant: ${example.output}\n\n`;
    });
  }



  if (systemPrompt.trim()) {
    systemPrompt += `---\nIMPORTANT INSTRUCTIONS:\n`;
    systemPrompt += `1. Follow the training examples and behavior guidelines above\n`;
    systemPrompt += `2. Maintain the personality and behavior patterns shown in the examples\n`;
    systemPrompt += `3. Apply the system instructions and general instructions consistently\n\n`;
    systemPrompt += `Now respond to the user following these patterns and guidelines.`;

    // Clone messages array
    const enhancedMessages = [...messages];

    // Find existing system message or create new one
    const systemMessageIndex = enhancedMessages.findIndex(m => m.role === 'system');

    if (systemMessageIndex >= 0) {
      // Enhance existing system message
      const existingContent = enhancedMessages[systemMessageIndex].content;
      const existingText = typeof existingContent === 'string' ? existingContent :
        (Array.isArray(existingContent) ? existingContent.find(p => p.type === 'text')?.text || '' : '');
      enhancedMessages[systemMessageIndex].content = systemPrompt + '\n\n' + existingText;
    } else {
      // Add new system message at the beginning
      enhancedMessages.unshift({
        role: 'system',
        content: systemPrompt
      });
    }

    console.log(`[Training Enhancement] Applied training data with ${processed_prompts.examples?.length || 0} examples`);
    return enhancedMessages;
  }

  return messages;
}







// Free tier detection based on provider characteristics
// Note: Google Gemini free tier detection implemented via rate limit headers
function isProviderFreeTier(provider: string | null, responseHeaders?: Headers): boolean {
  if (!provider) return false;

  const providerLower = provider.toLowerCase();

  // DeepSeek has no rate limits - effectively free tier for everyone
  if (providerLower === 'deepseek') {
    return true;
  }

  // Google Gemini free tier detection based on rate limit headers
  if ((providerLower === 'google' || providerLower === 'gemini') && responseHeaders) {
    // Check various possible rate limit header formats
    const rateLimitHeaders = [
      'x-ratelimit-limit',
      'x-ratelimit-requests-limit',
      'x-goog-quota-limit',
      'quota-limit'
    ];

    for (const headerName of rateLimitHeaders) {
      const rateLimitValue = responseHeaders.get(headerName);
      if (rateLimitValue) {
        const limit = parseInt(rateLimitValue);
        if (!isNaN(limit)) {
          // Free tier: ≤ 60 RPM (being slightly generous with the threshold)
          // Paid tier: ≥ 1000 RPM
          console.log(`[Free Tier Detection] Google rate limit detected: ${limit} RPM via header ${headerName}`);
          return limit <= 60;
        }
      }
    }

    // If no rate limit headers found, assume paid tier (safer for cost calculation)
    console.log(`[Free Tier Detection] No Google rate limit headers found, assuming paid tier`);
  }

  return false;
}

// Define the schema for the request body
const RoKeyChatCompletionRequestSchema = z.object({
  custom_api_config_id: z.string().uuid({ message: "custom_api_config_id must be a valid UUID." }),
  role: z.string().optional(),
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant', 'system']),
      content: z.any(), // Can be string or array of parts for multimodal
    })
  ).min(1, { message: "Messages array cannot be empty and must contain at least one message." }),
  model: z.string().optional(),
  stream: z.boolean().optional().default(false),
  temperature: z.number().optional(),
  max_tokens: z.number().int().positive().optional(),
  specific_api_key_id: z.string().uuid().optional(), // For retry functionality with specific API key
}).catchall(z.any());

// Helper function to classify prompt complexity using Gemini
async function classifyPromptComplexity(
  userPrompt: string,
  apiKey: string
): Promise<number | null> {
  if (!userPrompt || !apiKey) {
    console.error('[Complexity Classification] Missing user prompt or API key for classification LLM.');
    return null;
  }
  const classificationModel = 'gemini-2.0-flash-lite';
  const classificationUrl = `https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`;
  const systemInstruction = "You are a prompt complexity classification expert. Your task is to analyze the user's prompt and classify its complexity on a scale of 1 to 5, where 1 is Very Simple, 2 is Simple, 3 is Moderate, 4 is Complex, and 5 is Very Complex. Output ONLY the integer number corresponding to the complexity level. Do not provide any explanation or any other text. Just the number.";
  const requestPayload = {
    model: classificationModel,
    messages: [
      { role: "system", content: systemInstruction },
      { role: "user", content: `User's original prompt: "${userPrompt}"\n\nClassify the complexity of this prompt (1-5):` }
    ],
    temperature: 0.1,
    max_tokens: 10,
    top_p: 1
  };
  try {
    const response = await fetch(classificationUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestPayload)
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('[Complexity Classification] LLM API error:', response.status, errorData);
      return null;
    }
    const result = await response.json();
    const classifiedText = result.choices?.[0]?.message?.content?.trim();
    if (classifiedText) {
      const level = parseInt(classifiedText, 10);
      if (!isNaN(level) && level >= 1 && level <= 5) {
        console.log(`[Complexity Classification] Classified prompt complexity as: ${level}`);
        return level;
      } else {
        console.warn('[Complexity Classification] LLM returned invalid complexity level:', classifiedText);
      }
    } else {
      console.warn('[Complexity Classification] Could not extract complexity from LLM response:', JSON.stringify(result));
    }
  } catch (error: any) {
    console.error('[Complexity Classification] Error during fetch to classification LLM:', error);
  }
  return null;
}

// Base routing result interface
interface BaseRoutingResult {
  targetApiKeyData: ApiKey | null;
  roleUsedState: string;
}

// Hybrid routing result interface (for LangGraph orchestration)
interface HybridRoutingResult extends BaseRoutingResult {
  hybridResponse: Response;
}

// Phase 1 Optimization: Intelligent role routing helper function
async function executeIntelligentRoleRouting(
  customConfig: any,
  customApiConfigIdFromRequest: string,
  parsedBody: any,
  supabase: any,
  request: NextRequest // Add request parameter
): Promise<BaseRoutingResult | HybridRoutingResult> {
  console.log('[RoKey Routing Strategy] Attempting: Intelligent Role');
  const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;

  if (!classificationApiKey) {
    console.error('[Intelligent Role Strategy] Error: ROKEY_CLASSIFICATION_GEMINI_API_KEY is not set. Cannot perform classification.');
    return { targetApiKeyData: null, roleUsedState: 'missing_classification_api_key' };
  }

  if (!customConfig.user_id) {
    console.error('[Intelligent Role Strategy] Error: User ID not found on custom configuration. Cannot fetch custom roles for classification.');
    return { targetApiKeyData: null, roleUsedState: 'missing_user_id' };
  }

  let userPromptText = '';
  if (parsedBody.messages && parsedBody.messages.length > 0) {
    const lastMessage = parsedBody.messages[parsedBody.messages.length - 1];
    if (lastMessage.role === 'user' && typeof lastMessage.content === 'string') userPromptText = lastMessage.content;
    else if (lastMessage.role === 'user' && Array.isArray(lastMessage.content)) {
      const textPart = lastMessage.content.find((p: any) => p.type === 'text');
      if (textPart && typeof textPart.text === 'string') userPromptText = textPart.text;
    }
  }
  if (!userPromptText && parsedBody.prompt) userPromptText = parsedBody.prompt;

  if (!userPromptText) {
    console.log('[Intelligent Role Strategy] No user prompt text extracted for classification.');
    return { targetApiKeyData: null, roleUsedState: 'no_prompt_for_classification' };
  }

  // Phase 4 Optimization: Check conversation context for role continuity
  const conversationId = getConversationIdWithFallback(parsedBody.messages);
  const existingContext = conversationContext.get(conversationId);

  if (existingContext) {
    const timeSinceLastMessage = Date.now() - existingContext.lastActivity;

    // Phase 4 Enhancement: First check for task transitions
    const transitionCheck = detectTaskTransition(userPromptText, existingContext.lastClassifiedRole);

    if (transitionCheck.isTransition) {
      console.log(`[Context Transition] Detected task transition from ${existingContext.lastClassifiedRole}: ${transitionCheck.reasoning}`);

      if (transitionCheck.newTaskRole) {
        // Direct transition to known role
        console.log(`[Context Transition] Direct transition to ${transitionCheck.newTaskRole}`);

        // Track this transition for learning
        trackUserRoleUsage(customConfig.user_id, transitionCheck.newTaskRole);

        // Update context with new role but preserve conversation ID for potential future context
        conversationContext.set(conversationId, {
          lastClassifiedRole: transitionCheck.newTaskRole,
          messageCount: parsedBody.messages.length,
          lastActivity: Date.now(),
          confidence: 0.9, // High confidence for explicit transitions
          conversationId: conversationId
        });

        // Get ecosystem and find key for the new role
        const ecosystem = await preloadUserRoleEcosystem(customConfig.user_id, customApiConfigIdFromRequest, supabase);
        if (ecosystem) {
          const roleAssignment = ecosystem.roleAssignments.find(ra => ra.role_name === transitionCheck.newTaskRole);
          if (roleAssignment && roleAssignment.api_key_id) {
            const apiKey = ecosystem.apiKeys.find(key => key.id === roleAssignment.api_key_id);
            if (apiKey) {
              console.log(`[Context Transition] Using transitioned role ${transitionCheck.newTaskRole} with key ${apiKey.id}`);
              return {
                targetApiKeyData: apiKey,
                roleUsedState: `context_transition_${existingContext.lastClassifiedRole}_to_${transitionCheck.newTaskRole}`
              };
            }
          }

          // If no specific key for new role, let it fall through to fresh classification
          console.log(`[Context Transition] No specific key for transitioned role ${transitionCheck.newTaskRole}, performing fresh classification`);
        }
      } else {
        // Transition detected but need fresh classification to determine new role
        console.log(`[Context Transition] Transition detected, performing fresh classification with context awareness`);
        // Fall through to fresh classification - the transition detection will influence the process
      }
    } else {
      // No transition detected, check for continuation
      const continuationCheck = isContextContinuation(
        userPromptText,
        existingContext.lastClassifiedRole,
        parsedBody.messages,
        timeSinceLastMessage
      );

      if (continuationCheck.isContinuation && continuationCheck.confidence > 0.6) {
        console.log(`[Contextual Routing] Detected continuation of ${existingContext.lastClassifiedRole} (confidence: ${(continuationCheck.confidence * 100).toFixed(1)}%, reason: ${continuationCheck.reasoning})`);

        // Update context activity
        existingContext.lastActivity = Date.now();
        existingContext.messageCount++;
        existingContext.confidence = continuationCheck.confidence;

        // Use the same role as previous context
        const contextRole = existingContext.lastClassifiedRole;

        // Track this contextual usage for learning
        trackUserRoleUsage(customConfig.user_id, contextRole);

        // Get ecosystem and find key for the contextual role
        const ecosystem = await preloadUserRoleEcosystem(customConfig.user_id, customApiConfigIdFromRequest, supabase);
        if (ecosystem) {
          const roleAssignment = ecosystem.roleAssignments.find(ra => ra.role_name === contextRole);
          if (roleAssignment && roleAssignment.api_key_id) {
            const apiKey = ecosystem.apiKeys.find(key => key.id === roleAssignment.api_key_id);
            if (apiKey) {
              console.log(`[Contextual Routing] Using contextual role ${contextRole} with key ${apiKey.id}`);
              return {
                targetApiKeyData: apiKey,
                roleUsedState: `contextual_continuation_${contextRole}_confidence_${Math.round(continuationCheck.confidence * 100)}`
              };
            }
          }

          // If no specific key for contextual role, let it fall through to fresh classification
          console.log(`[Contextual Routing] No specific key for contextual role ${contextRole}, performing fresh classification`);
        }
      } else {
        console.log(`[Contextual Routing] No continuation detected (confidence: ${(continuationCheck.confidence * 100).toFixed(1)}%, reason: ${continuationCheck.reasoning}), performing fresh classification`);
      }
    }
  } else {
    console.log(`[Contextual Routing] No existing context for conversation ${conversationId}, performing fresh classification`);
  }

  // Phase 9: Aggressive caching for repeated patterns
  const queryHash = generateQueryHash(userPromptText);
  const cacheKey = `${customApiConfigIdFromRequest}_${queryHash}`;
  const userCacheKey = `user_${customConfig.user_id}_general_pattern`;

  // Check specific cache first
  const cached = routingCache.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < ROUTING_CACHE_TTL) {
    console.log(`[Fast Cache] Using cached classification: ${cached.roleId} - skipping LLM call`);

    const ecosystem = await preloadUserRoleEcosystem(customConfig.user_id, customApiConfigIdFromRequest, supabase);
    if (ecosystem) {
      if (cached.roleId === 'general_chat') {
        const defaultKey = ecosystem.apiKeys.find(key => key.is_default_general_chat_model === true);
        if (defaultKey) {
          return {
            targetApiKeyData: defaultKey,
            roleUsedState: generateRoleUsedMessage.intelligentRoleRouting(cached.roleId)
          };
        }
      } else {
        const roleAssignment = ecosystem.roleAssignments.find(ra => ra.role_name === cached.roleId);
        if (roleAssignment && roleAssignment.api_key_id) {
          const apiKey = ecosystem.apiKeys.find(key => key.id === roleAssignment.api_key_id);
          if (apiKey) {
            return {
              targetApiKeyData: apiKey,
              roleUsedState: generateRoleUsedMessage.intelligentRoleRouting(cached.roleId || 'general_chat')
            };
          }
        }
      }
    }
  }

  // DISABLED: Aggressive caching was bypassing intelligent routing after just 3 messages
  // This was preventing the system from classifying different types of requests
  // TODO: Re-implement with better logic that doesn't break role routing
  /*
  const userPattern = routingCache.get(userCacheKey);
  if (userPattern && userPattern.consecutiveGeneralChat >= 10 && (Date.now() - userPattern.timestamp) < (ROUTING_CACHE_TTL * 2)) {
    console.log(`[Aggressive Cache] User consistently uses general_chat (${userPattern.consecutiveGeneralChat} times) - skipping classification`);

    const ecosystem = await preloadUserRoleEcosystem(customConfig.user_id, customApiConfigIdFromRequest, supabase);
    if (ecosystem) {
      const defaultKey = ecosystem.apiKeys.find(key => key.is_default_general_chat_model === true);
      if (defaultKey) {
        // Update pattern cache
        routingCache.set(userCacheKey, {
          consecutiveGeneralChat: userPattern.consecutiveGeneralChat + 1,
          timestamp: Date.now()
        });

        return {
          targetApiKeyData: defaultKey,
          roleUsedState: generateRoleUsedMessage.intelligentRoleRouting('general_chat')
        };
      }
    }
  }
  */

  // Phase 8: Fast ecosystem loading and role preparation
  console.log('[Fast Routing] Loading ecosystem and preparing roles...');

  const [ecosystem, rolePreparation] = await Promise.all([
    preloadUserRoleEcosystem(customConfig.user_id, customApiConfigIdFromRequest, supabase),
    // Prepare roles in parallel
    Promise.resolve().then(() => {
      const baseRoles = PREDEFINED_ROLES.map(r => ({ id: r.id, name: r.name, description: r.description || '' }));
      return baseRoles;
    })
  ]);

  if (!ecosystem) {
    console.error('[Fast Routing] Failed to load user role ecosystem');
    return { targetApiKeyData: null, roleUsedState: 'ecosystem_load_failed' };
  }

  // Quick role list building
  const customRoles = ecosystem.customRoles.map(ur => ({ id: ur.role_id, name: ur.name, description: ur.description || '' }));
  const allRoles = [...rolePreparation, ...customRoles];
  const optimizedRoles = optimizeRoleOrder(allRoles, customConfig.user_id);

  console.log(`[Fast Routing] ${allRoles.length} roles prepared for classification`);
  console.log(`[DEBUG] Available roles: ${allRoles.map(r => `${r.id}(${r.name})`).join(', ')}`);

  const roleAssignments = ecosystem.roleAssignments;
  console.log(`[DEBUG] Role assignments found: ${roleAssignments.length}`);
  console.log(`[DEBUG] Role assignments: ${roleAssignments.map(ra => `${ra.role_name}→${ra.api_key_id}`).join(', ')}`);
  console.log(`[DEBUG] Available API keys: ${ecosystem.apiKeys.map(k => `${k.id}(${k.label})`).join(', ')}`);

  if (allRoles.length === 0) {
    console.warn('[Fast Routing] No roles available for classification');
    return { targetApiKeyData: null, roleUsedState: 'no_roles_available' };
  }

  // Phase 8: Ultra-fast classification - single Gemini call
  console.log('[Fast Routing] Starting single Gemini classification...');
  // Get conversation context for smarter classification
  const recentConversation = parsedBody.messages ? parsedBody.messages.slice(-3).map((m: any) =>
    `${m.role}: ${typeof m.content === 'string' ? m.content.substring(0, 200) :
    Array.isArray(m.content) ? m.content.find((p: any) => p.type === 'text')?.text?.substring(0, 200) || '[non-text]' : '[unknown]'}`
  ).join('\n') : '';

  const roleInfoForPrompt = optimizedRoles.map(r => `- ${r.id}: ${r.name}`).join('\n');
  const classSystemPrompt = "You are an expert task classifier. Analyze the user's request considering both the current message AND recent conversation context. If the user is responding to options/choices in an ongoing task (like selecting '1, 2, 3' after being given coding options), continue with the same role. Only switch roles for clear new tasks (like 'now write a story' or 'solve this math problem'). Respond with ONLY the role ID.";
  const classificationUserMessage = `Available Roles:\n${roleInfoForPrompt}\n\nRecent Conversation:\n${recentConversation}\n\nCurrent Request: "${userPromptText.substring(0, 1000)}"\n\nMost Appropriate Role ID:`;

  console.log(`[DEBUG] Classification prompt for: "${userPromptText.substring(0, 100)}..."`);
  console.log(`[DEBUG] Available roles for classification: ${optimizedRoles.map(r => r.id).join(', ')}`);

  let classifiedRoleByLLM: string | null = null;

  try {
    console.log(`[Fast Routing] Calling Gemini for classification...`);

    const classificationResult = await classifyWithDeduplication(
      userPromptText,
      optimizedRoles,
      classificationApiKey,
      customApiConfigIdFromRequest,
      parsedBody.messages || []
    );

    if (classificationResult) {
      // Check if this is a multi-role classification
      if ('isMultiRole' in classificationResult && classificationResult.isMultiRole) {
        console.log(`[RouKey Agents Orchestration] 🎯 Multi-role task detected by Gemini! Roles: ${classificationResult.roles.map((r: any) => r.roleId).join(', ')}`);

        try {
          // Extract role IDs from classification result
          const detectedRoles = classificationResult.roles.map((r: any) => r.roleId);

          // Build user API keys mapping for LangGraph orchestration
          const userApiKeys: Record<string, any> = {};
          for (const role of detectedRoles) {
            let apiKey = null;

            // Find API key for this role
            const roleAssignment = ecosystem.roleAssignments.find((ra: any) => ra.role_name === role);
            if (roleAssignment && roleAssignment.api_key_id) {
              apiKey = ecosystem.apiKeys.find((key: any) => key.id === roleAssignment.api_key_id);
            }

            if (apiKey) {
              userApiKeys[role] = apiKey;
              console.log(`[RouKey Agents Orchestration] ✅ Found API key for ${role}: ${apiKey.provider} (${apiKey.label})`);
            } else {
              console.warn(`[RouKey Agents Orchestration] ⚠️ No API key found for role: ${role}`);
            }
          }

          // Only proceed if we have at least 2 roles with API keys
          const availableRoles = Object.keys(userApiKeys);
          if (availableRoles.length >= 2) {
            console.log(`[RouKey Agents Orchestration] 🚀 Initiating LangGraph orchestration with ${availableRoles.length} roles`);

            // Import progress emitter
            const OrchestrationProgressEmitter = (await import('@/lib/orchestration/progressEmitter')).default;
            const progressEmitter = OrchestrationProgressEmitter.getInstance();
            progressEmitter.resetColorIndex(); // Reset for new orchestration

            // Create progress callback for real-time status updates
            const progressCallback = {
              onClassificationStart: () => {
                progressEmitter.classificationStart();
              },
              onClassificationComplete: (roles: string[], threshold: number) => {
                progressEmitter.classificationComplete(roles, threshold);
              },
              onRoleSelectionComplete: (selectedRoles: string[], filteredRoles: string[]) => {
                progressEmitter.roleSelectionComplete(selectedRoles, filteredRoles);
              },
              onWorkflowSelectionComplete: (workflowType: string, reasoning: string) => {
                progressEmitter.workflowSelectionComplete(workflowType, reasoning);
              },
              onAgentCreationStart: () => {
                progressEmitter.agentCreationStart();
              },
              onAgentCreationComplete: (agents: Array<{ role: string, apiKey: string }>) => {
                progressEmitter.agentCreationComplete(agents);
              },
              onSupervisorInitStart: () => {
                progressEmitter.supervisorInitStart();
              },
              onSupervisorInitComplete: (supervisorRole: string) => {
                progressEmitter.supervisorInitComplete(supervisorRole);
              },
              onTaskPlanningStart: () => {
                progressEmitter.taskPlanningStart();
              },
              onTaskPlanningComplete: (plan: string) => {
                progressEmitter.taskPlanningComplete(plan);
              },
              onAgentWorkStart: (role: string, task: string) => {
                progressEmitter.agentWorkStart(role, task);
              },
              onAgentWorkComplete: (role: string, result: string) => {
                progressEmitter.agentWorkComplete(role, result);
              },
              onSupervisorSynthesisStart: () => {
                progressEmitter.supervisorSynthesisStart();
              },
              onSupervisorSynthesisComplete: (synthesis: string) => {
                progressEmitter.supervisorSynthesisComplete(synthesis);
              },
              onOrchestrationComplete: (result: any) => {
                progressEmitter.orchestrationComplete(result);
              },
              onError: (step: string, error: string) => {
                progressEmitter.error(step, error);
              }
            };

            // Execute LangGraph orchestration
            const orchestrationResult = await RouKeyLangGraphIntegration.executeMultiRole(
              availableRoles,
              userApiKeys,
              userPromptText,
              {
                conversationId: parsedBody.conversation_id,
                userId: customConfig.user_id,
                customApiConfigId: customApiConfigIdFromRequest, // Required for provider requests
                workflowType: 'auto', // Let the system determine the best workflow
                enableStreaming: false, // For now, disable streaming
                progressCallback: progressCallback
              }
            );

            if (orchestrationResult.success) {
              console.log(`[RouKey Agents Orchestration] ✅ Multi-role orchestration completed successfully!`);
              console.log(`[RouKey Agents Orchestration] 📊 Execution time: ${orchestrationResult.metadata.executionTime}ms`);
              console.log(`[RouKey Agents Orchestration] 🔧 Workflow type: ${orchestrationResult.metadata.workflowType}`);

              // Create a streaming response with the orchestration result and progress events
              const stream = new ReadableStream({
                async start(controller) {
                  const encoder = new TextEncoder();
                  const finalResponse = orchestrationResult.finalResponse;

                  console.log(`[Hybrid Integration] ✅ Hybrid orchestration response detected! Returning hybrid streaming response.`);
                  console.log(`[Hybrid Integration] Response length: ${finalResponse.length} characters`);

                  // Set up progress event listener to emit status updates
                  const progressEvents: any[] = [];
                  const progressListener = (event: any) => {
                    progressEvents.push(event);

                    // Emit progress event as a custom SSE event
                    const progressData = {
                      id: `progress-${crypto.randomUUID()}`,
                      object: 'orchestration.progress',
                      created: Math.floor(Date.now() / 1000),
                      type: 'progress',
                      data: {
                        message: event.message,
                        type: event.type,
                        colorIndex: event.colorIndex,
                        timestamp: event.timestamp
                      }
                    };

                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(progressData)}\n\n`));
                  };

                  progressEmitter.on('progress', progressListener);

                  // Stream the response in chunks to simulate real-time streaming
                  const chunkSize = 50; // Characters per chunk
                  for (let i = 0; i < finalResponse.length; i += chunkSize) {
                    const chunk = finalResponse.substring(i, i + chunkSize);

                    const response = {
                      id: `chatcmpl-${crypto.randomUUID()}`,
                      object: 'chat.completion.chunk',
                      created: Math.floor(Date.now() / 1000),
                      model: 'rokey-agents-orchestration',
                      choices: [{
                        index: 0,
                        delta: {
                          content: chunk
                        },
                        finish_reason: null
                      }]
                    };

                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(response)}\n\n`));
                    await new Promise(resolve => setTimeout(resolve, 50)); // Small delay for streaming effect
                  }

                  // Send final chunk with finish_reason
                  const finalChunk = {
                    id: `chatcmpl-${crypto.randomUUID()}`,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model: 'rokey-agents-orchestration',
                    choices: [{
                      index: 0,
                      delta: {},
                      finish_reason: 'stop'
                    }],
                    usage: {
                      prompt_tokens: 0,
                      completion_tokens: orchestrationResult.metadata.totalTokens,
                      total_tokens: orchestrationResult.metadata.totalTokens
                    }
                  };

                  controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
                  controller.enqueue(encoder.encode('data: [DONE]\n\n'));

                  // Clean up progress listener
                  progressEmitter.removeListener('progress', progressListener);
                  controller.close();
                }
              });

              return {
                targetApiKeyData: null, // Not used for orchestration
                roleUsedState: `RouKey_Multi Roles_${availableRoles.join('_')}_routing`,
                hybridResponse: new Response(stream, {
                  headers: {
                    'Content-Type': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive'
                  }
                })
              };
            } else {
              console.error(`[RouKey Agents Orchestration] ❌ Orchestration failed: ${orchestrationResult.finalResponse}`);
              console.log(`[RouKey Agents Orchestration] Falling back to single-role routing with primary role: ${classificationResult.roles[0]?.roleId}`);
            }
          } else {
            console.warn(`[RouKey Agents Orchestration] ⚠️ Insufficient API keys (${availableRoles.length}/2 minimum required)`);
            console.log(`[RouKey Agents Orchestration] Falling back to single-role routing with primary role: ${classificationResult.roles[0]?.roleId}`);
          }
        } catch (orchestrationError) {
          console.error(`[RouKey Agents Orchestration] ❌ Error during LangGraph orchestration:`, orchestrationError);
          console.log(`[RouKey Agents Orchestration] Falling back to single-role routing with primary role: ${classificationResult.roles[0]?.roleId}`);
        }

        // If we reach here, something went wrong with multi-role orchestration
        // Fall back to single-role processing using the first role
        if (classificationResult.roles.length > 0) {
          classifiedRoleByLLM = classificationResult.roles[0].roleId;
          console.log(`[RouKey Agents Orchestration] Falling back to single-role processing with ${classifiedRoleByLLM}`);
        } else {
          console.warn(`[RouKey Agents Orchestration] No valid roles found, using fallback routing`);
        }
      } else if ('roleId' in classificationResult) {
        // Single role classification
        classifiedRoleByLLM = classificationResult.roleId;
      }

      // Cache the result (for both single-role and fallback from multi-role)
      if (classifiedRoleByLLM) {
        routingCache.set(cacheKey, { roleId: classifiedRoleByLLM, timestamp: Date.now() });
      }

      // Track user patterns for aggressive caching
      const userCacheKey = `user_${customConfig.user_id}_general_pattern`;
      if (classifiedRoleByLLM === 'general_chat') {
        const existing = routingCache.get(userCacheKey) || { consecutiveGeneralChat: 0, timestamp: Date.now() };
        routingCache.set(userCacheKey, {
          consecutiveGeneralChat: (existing.consecutiveGeneralChat || 0) + 1,
          timestamp: Date.now()
        });
      } else {
        // Reset pattern if user uses other roles
        routingCache.set(userCacheKey, {
          consecutiveGeneralChat: 0,
          timestamp: Date.now()
        });
      }

      console.log(`[Fast Routing] Classified to role: ${classifiedRoleByLLM}`);
    } else {
      console.log(`[Fast Routing] Classification failed - using general_chat`);
      classifiedRoleByLLM = 'general_chat';
    }
  } catch (error: any) {
    console.error('[Fast Routing] Classification error - using general_chat:', error.message);
    classifiedRoleByLLM = 'general_chat';
  }

  // Phase 8: Fast role matching and key finding
  if (classifiedRoleByLLM) {
    trackUserRoleUsage(customConfig.user_id, classifiedRoleByLLM);

    console.log(`[Fast Routing] Finding key for role: ${classifiedRoleByLLM}`);

    // Handle general_chat (most common case)
    if (classifiedRoleByLLM === 'general_chat') {
      const defaultKey = ecosystem.apiKeys.find(key => key.is_default_general_chat_model === true);
      if (defaultKey) {
        console.log(`[Fast Routing] ✅ Using default general chat key ${defaultKey.id}`);
        return {
          targetApiKeyData: defaultKey,
          roleUsedState: generateRoleUsedMessage.intelligentRoleRouting(classifiedRoleByLLM)
        };
      }
    }

    // Handle other roles
    const roleAssignment = roleAssignments.find(ra => ra.role_name === classifiedRoleByLLM);
    if (roleAssignment && roleAssignment.api_key_id) {
      const apiKey = ecosystem.apiKeys.find(key => key.id === roleAssignment.api_key_id);
      if (apiKey) {
        console.log(`[Fast Routing] ✅ Using key ${apiKey.id} for role ${classifiedRoleByLLM}`);
        return {
          targetApiKeyData: apiKey,
          roleUsedState: generateRoleUsedMessage.intelligentRoleRouting(classifiedRoleByLLM)
        };
      }
    }

    console.log(`[Fast Routing] No key found for role ${classifiedRoleByLLM} - will use fallback`);
  }

  return { targetApiKeyData: null, roleUsedState: 'classification_no_key_found' };
}

// Phase 1 Optimization: Strict fallback routing helper function
async function executeStrictFallbackRouting(
  strategyParams: any,
  customApiConfigIdFromRequest: string,
  supabase: any
): Promise<{ targetApiKeyData: ApiKey | null; roleUsedState: string }> {
  console.log('[RoKey Routing Strategy] Attempting: Strict Fallback');
  const orderedKeyIds = strategyParams?.ordered_api_key_ids;

  if (!Array.isArray(orderedKeyIds) || orderedKeyIds.length === 0) {
    console.log('[Strict Fallback Strategy] No ordered API keys defined in strategy_params.');
    return { targetApiKeyData: null, roleUsedState: 'strict_fallback_no_keys_defined' };
  }

  // Phase 6 Optimization: Parallel key fetching for strict fallback
  console.log(`[Strict Fallback Strategy] Fetching ${orderedKeyIds.length} keys in parallel...`);

  const keyFetchPromises = orderedKeyIds.map(async (keyId, index) => {
    try {
      const { data: apiKey, error: apiKeyError } = await supabase
        .from('api_keys')
        .select('*')
        .eq('id', keyId)
        .eq('custom_api_config_id', customApiConfigIdFromRequest)
        .single();

      return {
        index,
        keyId,
        apiKey,
        error: apiKeyError,
        success: !apiKeyError && apiKey && apiKey.status === 'active'
      };
    } catch (error) {
      return {
        index,
        keyId,
        apiKey: null,
        error,
        success: false
      };
    }
  });

  const keyResults = await Promise.allSettled(keyFetchPromises);

  // Find the first successful key in order
  for (let index = 0; index < keyResults.length; index++) {
    const result = keyResults[index];

    if (result.status === 'fulfilled' && result.value.success) {
      const { apiKey, keyId } = result.value;
      console.log(`[Strict Fallback Strategy] Using active key ${apiKey.id} (Label: ${apiKey.label}) from parallel fetch`);
      return {
        targetApiKeyData: apiKey as ApiKey,
        roleUsedState: generateRoleUsedMessage.fallbackRouting(index)
      };
    } else if (result.status === 'fulfilled') {
      const { keyId, apiKey, error } = result.value;
      if (error && error.code !== 'PGRST116') {
        console.error(`[Strict Fallback Strategy] Error fetching key ${keyId}:`, error);
      } else if (apiKey) {
        console.log(`[Strict Fallback Strategy] Key ${keyId} (Label: ${apiKey.label}) not active (Status: ${apiKey.status}).`);
      } else {
        console.log(`[Strict Fallback Strategy] Key ${keyId} not found.`);
      }
    }
  }

  console.log('[Strict Fallback Strategy] No active key found in the ordered list.');
  return { targetApiKeyData: null, roleUsedState: 'strict_fallback_no_active_key_in_list' };
}

// Phase 1 Optimization: Complexity round robin routing helper function
async function executeComplexityRoundRobinRouting(
  customConfig: any,
  parsedBody: any,
  supabase: any
): Promise<{ targetApiKeyData: ApiKey | null; roleUsedState: string; classifiedComplexityLevel?: number; classifiedComplexityLLM?: string }> {
  console.log('[RoKey Routing Strategy] Attempting: Complexity-Based Round-Robin (with proximal search)');
  const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;

  if (!classificationApiKey) {
    console.error('[Complexity RR Strategy] Error: ROKEY_CLASSIFICATION_GEMINI_API_KEY is not set.');
    return { targetApiKeyData: null, roleUsedState: 'complexity_rr_missing_classifier_key' };
  }

  let userPromptText = '';
  if (parsedBody.messages && parsedBody.messages.length > 0) {
    const lastMessage = parsedBody.messages[parsedBody.messages.length - 1];
    if (lastMessage.role === 'user' && typeof lastMessage.content === 'string') userPromptText = lastMessage.content;
    else if (lastMessage.role === 'user' && Array.isArray(lastMessage.content)) {
      const textPart = lastMessage.content.find((p: any) => p.type === 'text');
      if (textPart && typeof textPart.text === 'string') userPromptText = textPart.text;
    }
  }
  if (!userPromptText && parsedBody.prompt) userPromptText = parsedBody.prompt;

  if (!userPromptText) {
    console.log('[Complexity RR Strategy] No user prompt text extracted for classification.');
    return { targetApiKeyData: null, roleUsedState: 'complexity_rr_no_prompt' };
  }

  const classifiedComplexityLevel = await classifyPromptComplexity(userPromptText, classificationApiKey);
  const initialClassifiedLevel = classifiedComplexityLevel;

  if (initialClassifiedLevel === null || initialClassifiedLevel < 1 || initialClassifiedLevel > 5) {
    console.log('[Complexity RR Strategy] Invalid complexity level classification.');
    return { targetApiKeyData: null, roleUsedState: 'complexity_rr_invalid_classification' };
  }

  console.log(`[Complexity RR Strategy] Prompt initially classified to complexity level: ${initialClassifiedLevel}`);

  // Find API keys assigned to this complexity level
  const { data: complexityAssignments, error: complexityError } = await supabase
    .from('config_api_key_complexity_assignments')
    .select(`
      api_key_id,
      complexity_level,
      api_keys!inner (
        id,
        status,
        provider,
        predefined_model_id,
        is_default_general_chat_model,
        encrypted_api_key,
        label
      )
    `)
    .eq('custom_api_config_id', customConfig.id)
    .eq('complexity_level', initialClassifiedLevel);

  if (complexityError) {
    console.error('[Complexity RR Strategy] Error fetching complexity assignments:', complexityError);
    return {
      targetApiKeyData: null,
      roleUsedState: 'complexity_rr_db_error',
      classifiedComplexityLevel: initialClassifiedLevel,
      classifiedComplexityLLM: 'gemini-2.0-flash-lite'
    };
  }

  // Filter for active keys
  const activeKeysForLevel = complexityAssignments
    ?.filter((assignment: any) => assignment.api_keys.status === 'active')
    ?.map((assignment: any) => assignment.api_keys) || [];

  console.log(`[Complexity RR Strategy] Found ${activeKeysForLevel.length} active keys for complexity level ${initialClassifiedLevel}`);

  if (activeKeysForLevel.length > 0) {
    // Get current round-robin index for this complexity level
    const strategyParams = customConfig.routing_strategy_params || {};
    const rrIndexKey = `_complexity_${initialClassifiedLevel}_rr_idx`;
    const currentIndex = (typeof strategyParams[rrIndexKey] === 'number') ? strategyParams[rrIndexKey] : 0;

    // Sort keys for consistent ordering
    const sortedKeys = [...activeKeysForLevel].sort((a, b) => a.id.localeCompare(b.id));
    const selectedKey = sortedKeys[currentIndex % sortedKeys.length];

    // Update round-robin index for next request
    const nextIndex = (currentIndex + 1) % sortedKeys.length;
    strategyParams[rrIndexKey] = nextIndex;

    // Background update of round-robin index
    setImmediate(async () => {
      const { error: updateError } = await supabase
        .from('custom_api_configs')
        .update({ routing_strategy_params: strategyParams })
        .eq('id', customConfig.id);
      if (updateError) console.error('[Complexity RR Strategy] Failed to update round-robin index:', updateError);
    });

    console.log(`[Complexity RR Strategy] ✅ Selected key ${selectedKey.id} for complexity level ${initialClassifiedLevel} (index ${currentIndex})`);
    return {
      targetApiKeyData: selectedKey,
      roleUsedState: `complexity_rr_level_${initialClassifiedLevel}_key_found`,
      classifiedComplexityLevel: initialClassifiedLevel,
      classifiedComplexityLLM: 'gemini-2.0-flash-lite'
    };
  }

  // Proximal search: try nearby complexity levels
  console.log(`[Complexity RR Strategy] No keys found for level ${initialClassifiedLevel}, trying proximal search...`);

  const searchOrder = [];
  for (let distance = 1; distance <= 4; distance++) {
    if (initialClassifiedLevel - distance >= 1) searchOrder.push(initialClassifiedLevel - distance);
    if (initialClassifiedLevel + distance <= 5) searchOrder.push(initialClassifiedLevel + distance);
  }

  for (const searchLevel of searchOrder) {
    console.log(`[Complexity RR Strategy] Searching complexity level ${searchLevel}...`);

    const { data: proximalAssignments, error: proximalError } = await supabase
      .from('config_api_key_complexity_assignments')
      .select(`
        api_key_id,
        complexity_level,
        api_keys!inner (
          id,
          status,
          provider,
          predefined_model_id,
          is_default_general_chat_model,
          encrypted_api_key,
          label
        )
      `)
      .eq('custom_api_config_id', customConfig.id)
      .eq('complexity_level', searchLevel);

    if (proximalError) {
      console.warn(`[Complexity RR Strategy] Error searching level ${searchLevel}:`, proximalError);
      continue;
    }

    const activeKeysForProximalLevel = proximalAssignments
      ?.filter((assignment: any) => assignment.api_keys.status === 'active')
      ?.map((assignment: any) => assignment.api_keys) || [];

    if (activeKeysForProximalLevel.length > 0) {
      // Get round-robin index for this proximal level
      const strategyParams = customConfig.routing_strategy_params || {};
      const rrIndexKey = `_complexity_${searchLevel}_rr_idx`;
      const currentIndex = (typeof strategyParams[rrIndexKey] === 'number') ? strategyParams[rrIndexKey] : 0;

      const sortedKeys = [...activeKeysForProximalLevel].sort((a, b) => a.id.localeCompare(b.id));
      const selectedKey = sortedKeys[currentIndex % sortedKeys.length];

      // Update round-robin index
      const nextIndex = (currentIndex + 1) % sortedKeys.length;
      strategyParams[rrIndexKey] = nextIndex;

      setImmediate(async () => {
        const { error: updateError } = await supabase
          .from('custom_api_configs')
          .update({ routing_strategy_params: strategyParams })
          .eq('id', customConfig.id);
        if (updateError) console.error('[Complexity RR Strategy] Failed to update proximal round-robin index:', updateError);
      });

      console.log(`[Complexity RR Strategy] ✅ Found proximal key ${selectedKey.id} at level ${searchLevel} for original level ${initialClassifiedLevel}`);
      return {
        targetApiKeyData: selectedKey,
        roleUsedState: `complexity_rr_level_${initialClassifiedLevel}_proximal_${searchLevel}_key_found`,
        classifiedComplexityLevel: initialClassifiedLevel,
        classifiedComplexityLLM: 'gemini-2.0-flash-lite'
      };
    }
  }

  console.log(`[Complexity RR Strategy] No keys found for level ${initialClassifiedLevel} or any proximal levels`);
  return {
    targetApiKeyData: null,
    roleUsedState: 'complexity_rr_no_keys_found',
    classifiedComplexityLevel: initialClassifiedLevel,
    classifiedComplexityLLM: 'gemini-2.0-flash-lite'
  };
}

// Phase 1 Optimization: Parallel fallback routing for role-based or default
async function getApiKeyForFallbackRouting(
  supabase: any,
  customConfigId: string,
  requestedRole?: string
): Promise<ApiKey | null> {
  // Phase 1 Optimization: Parallel queries for role assignment and default key
  const [roleAssignmentResult, defaultKeyResult] = await Promise.allSettled([
    requestedRole ? supabase
      .from('api_key_role_assignments')
      .select('api_key_id')
      .eq('custom_api_config_id', customConfigId)
      .eq('role_name', requestedRole)
      .single() : Promise.resolve({ data: null, error: null }),
    supabase
      .from('api_keys')
      .select('*')
      .eq('custom_api_config_id', customConfigId)
      .eq('is_default_general_chat_model', true)
      .single()
  ]);

  // 1. Handle Requested Role (if provided)
  if (requestedRole && roleAssignmentResult.status === 'fulfilled' && roleAssignmentResult.value.data) {
    const roleAssignment = roleAssignmentResult.value.data;

    // Fetch the specific API key for the role
    const { data: apiKey, error: apiKeyError } = await supabase
      .from('api_keys')
      .select('*')
      .eq('id', roleAssignment.api_key_id)
      .single();

    if (apiKeyError) {
      console.error('[Fallback Routing] Error fetching API key by role assignment:', apiKeyError);
    } else if (apiKey && apiKey.status === 'active') {
      console.log(`[Fallback Routing] Role-based: Using active key ${apiKey.id} for role ${requestedRole}`);
      return apiKey;
    } else if (apiKey) {
      console.log(`[Fallback Routing] Role-based: Key for role ${requestedRole} (ID: ${apiKey.id}) is not active. Will try default.`);
    }
  } else if (requestedRole && roleAssignmentResult.status === 'rejected') {
    console.error('[Fallback Routing] Error fetching role assignment:', roleAssignmentResult.reason);
  }

  // 2. Fallback to Default General Chat Model (already fetched in parallel)
  console.log(`[Fallback Routing] Attempting to use Default General Chat Model for config ${customConfigId}`);

  if (defaultKeyResult.status === 'fulfilled' && defaultKeyResult.value.data) {
    const defaultApiKey = defaultKeyResult.value.data;
    if (defaultApiKey.status === 'active') {
      console.log(`[Fallback Routing] Default: Using active default key ${defaultApiKey.id}`);
      return defaultApiKey;
    } else {
      console.log(`[Fallback Routing] Default: Key ${defaultApiKey.id} is not active. No key found by fallback.`);
      return null;
    }
  } else if (defaultKeyResult.status === 'rejected') {
    console.error('[Fallback Routing] Error fetching default API key:', defaultKeyResult.reason);
  } else {
    console.log(`[Fallback Routing] Default: No default key configured or found for config ${customConfigId}.`);
  }

  return null;
}

// Helper function to get the actual model ID for direct provider calls
function getDirectProviderModelId(modelIdFromDb: string | null, providerName: string): string {
  if (!modelIdFromDb) return '';
  const lowerProviderName = providerName.toLowerCase();
  const prefix = `${lowerProviderName}/`;
  if (modelIdFromDb.toLowerCase().startsWith(prefix)) return modelIdFromDb.substring(prefix.length);
  return modelIdFromDb;
}

// Helper function to parse data URLs for Gemini
function parseDataUrl(dataUrl: string): { mimeType: string, base64Data: string } | null {
  const match = dataUrl.match(/^data:(.+?);base64,(.+)$/);
  if (match && match[1] && match[2]) return { mimeType: match[1], base64Data: match[2] };
  console.warn(`[RoKey Gemini Handler] Failed to parse data URL: ${dataUrl.substring(0, 100)}...`);
  return null;
}

// Interface for the result of executeProviderRequest
interface ProviderCallResult {
  success: boolean;
  response?: Response; // For streams
  responseData?: any; // For non-streams (OpenAI-like format)
  responseHeaders?: Headers;
  status?: number; // Provider HTTP status
  error?: any; // Error object/details from provider or internal
  llmRequestTimestamp: Date;
  llmResponseTimestamp: Date;
}

import https from 'https';
import http from 'http';
import { URL } from 'url';

// Phase 9 Optimization: Google-optimized timeout configuration
const TIMEOUT_CONFIG = {
  CLASSIFICATION: 5000,    // 5s for role classification (Google needs more time)
  LLM_REQUEST: 8000,      // 8s for LLM requests (reasonable but not too long)
  CONNECTION: 2000,       // 2s for connection establishment
  SOCKET: 1500,          // 1.5s for socket timeout
  GOOGLE_CLASSIFICATION: 7000, // 7s specifically for Google classification
};

// Phase 5 Optimization: Ultra-low latency connection pooling with aggressive timeouts
const httpsAgent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 120000, // Extended keep-alive for maximum reuse
  maxSockets: 200, // Maximum concurrent connections for speed
  maxFreeSockets: 50, // Many idle connections for instant reuse
  timeout: TIMEOUT_CONFIG.SOCKET, // Ultra-fast 1.5s socket timeout
  scheduling: 'lifo', // Last-in-first-out for warmest connections
  maxTotalSockets: 500, // High global connection limit
});

const httpAgent = new http.Agent({
  keepAlive: true,
  keepAliveMsecs: 120000,
  maxSockets: 200,
  maxFreeSockets: 50,
  timeout: TIMEOUT_CONFIG.SOCKET,
  scheduling: 'lifo',
  maxTotalSockets: 500,
});

// Phase 5 Optimization: Enhanced fetch with aggressive timeouts and smart retry logic
async function robustFetch(
  url: string,
  options: RequestInit,
  maxRetries: number = 3,
  timeoutMs?: number
): Promise<Response> {
  let lastError: any;

  // Phase 9: Google-optimized timeout determination
  const requestTimeout = timeoutMs || (
    url.includes('generativelanguage.googleapis.com') ? TIMEOUT_CONFIG.GOOGLE_CLASSIFICATION :
    TIMEOUT_CONFIG.LLM_REQUEST
  );

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`[RobustFetch] Attempt ${attempt}/${maxRetries} for ${url} (timeout: ${requestTimeout}ms)`);

      const response = await makeHttpRequestOptimized(url, options, requestTimeout);
      console.log(`[RobustFetch] Success on attempt ${attempt} for ${url} - Status: ${response.status}`);
      return response;

    } catch (error: any) {
      lastError = error;
      console.warn(`[RobustFetch] Attempt ${attempt}/${maxRetries} failed for ${url}:`, error.message);

      if (attempt === maxRetries) {
        console.error(`[RobustFetch] All ${maxRetries} attempts failed for ${url}`);
        throw error;
      }

      // Phase 5: Ultra-fast retry with minimal backoff for quick failure detection
      const backoffMs = error.message.includes('timeout') ? 50 : 100 * attempt;
      await new Promise(resolve => setTimeout(resolve, backoffMs));
    }
  }

  throw lastError;
}

// Phase 5 Optimization: High-performance HTTP request with dynamic timeouts
function makeHttpRequestOptimized(url: string, options: RequestInit, timeoutMs: number = TIMEOUT_CONFIG.LLM_REQUEST): Promise<Response> {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        ...options.headers as Record<string, string>,
        // Phase 5: Performance-optimized headers with aggressive keep-alive
        'Connection': 'keep-alive',
        'Keep-Alive': `timeout=${Math.floor(timeoutMs/1000)}, max=100`,
      },
      timeout: timeoutMs, // Phase 5: Dynamic timeout based on request type
      agent: isHttps ? httpsAgent : httpAgent, // Use connection pooling agents
    };

    const req = client.request(requestOptions, (res) => {
      // Handle compressed responses properly
      let responseStream = res;
      const encoding = res.headers['content-encoding'];

      if (encoding === 'gzip') {
        const zlib = require('zlib');
        responseStream = res.pipe(zlib.createGunzip());
      } else if (encoding === 'deflate') {
        const zlib = require('zlib');
        responseStream = res.pipe(zlib.createInflate());
      } else if (encoding === 'br') {
        const zlib = require('zlib');
        responseStream = res.pipe(zlib.createBrotliDecompress());
      }

      // For streaming responses, we need to handle the body differently
      const isStreaming = res.headers['content-type']?.includes('text/event-stream') ||
                         res.headers['content-type']?.includes('text/plain') ||
                         res.headers['content-type']?.includes('application/x-ndjson');

      if (isStreaming) {
        // Create a ReadableStream for streaming responses
        const stream = new ReadableStream({
          start(controller) {
            responseStream.on('data', (chunk) => {
              // Ensure proper text encoding for streaming data
              const text = chunk.toString('utf8');
              controller.enqueue(new TextEncoder().encode(text));
            });

            responseStream.on('end', () => {
              controller.close();
            });

            responseStream.on('error', (error) => {
              controller.error(error);
            });
          }
        });

        // Create a proper Response object with streaming body for Next.js compatibility
        const response = new Response(stream, {
          status: res.statusCode!,
          statusText: res.statusMessage || '',
          headers: new Headers(res.headers as Record<string, string>),
        });

        resolve(response);
      } else {
        // For non-streaming responses, collect all data as Buffer first
        const chunks: Buffer[] = [];
        responseStream.on('data', (chunk) => {
          chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
        });

        responseStream.on('end', () => {
          // Combine all chunks and convert to string properly
          const buffer = Buffer.concat(chunks);
          const data = buffer.toString('utf8');

          // Create a proper Response object for Next.js compatibility
          const response = new Response(data, {
            status: res.statusCode!,
            statusText: res.statusMessage || '',
            headers: new Headers(res.headers as Record<string, string>),
          });

          resolve(response);
        });
      }
    });

    req.on('error', (error) => {
      reject(new Error(`Network request failed: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${timeoutMs}ms`));
    });

    // Send request body if provided
    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function executeProviderRequest(
  providerName: string | null,
  modelIdInDb: string | null, // The ID stored in your predefined_models table
  apiKeyToUse: string,      // Decrypted API key
  requestPayload: z.infer<typeof RoKeyChatCompletionRequestSchema> // The original, validated request body
): Promise<ProviderCallResult> {
  let p_llmRequestTimestamp: Date | null = null;
  let p_llmResponseTimestamp: Date | null = new Date();
  let p_status: number | undefined = undefined;
  let p_error: any = undefined;
  let p_response: Response | undefined = undefined;
  let p_responseData: any = undefined;
  let p_responseHeaders: Headers | undefined = undefined;

  // Phase 2 Optimization: Check response cache for non-streaming requests
  if (!requestPayload.stream && requestPayload.messages && modelIdInDb) {
    const cacheKey = generateResponseCacheKey(
      requestPayload.messages,
      modelIdInDb,
      requestPayload.temperature
    );

    const cached = responseCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < RESPONSE_CACHE_TTL) {
      console.log(`[ResponseCache] Cache HIT for ${providerName}/${modelIdInDb} - returning cached response`);
      return {
        success: true,
        response: undefined,
        responseData: cached.response,
        responseHeaders: new Headers({ 'x-rokey-cache': 'hit' }),
        status: 200,
        error: null,
        llmRequestTimestamp: new Date(),
        llmResponseTimestamp: new Date(),
      };
    } else {
      console.log(`[ResponseCache] Cache MISS for ${providerName}/${modelIdInDb} - proceeding with API call`);
    }
  }

  // Enhanced fetch configuration with better error handling
  const fetchOptions: RequestInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Connection': 'keep-alive',
      'Keep-Alive': 'timeout=30, max=100',
      'User-Agent': 'RoKey/1.0 (Performance-Optimized)',
      'Accept': 'application/json',

      'Cache-Control': 'no-cache',
    } as Record<string, string>,
  };

  try {
    // Determine the actual model ID to be used for the provider API call
    const actualModelIdForProvider = getDirectProviderModelId(modelIdInDb, providerName || '');
    
    // OpenRouter uses the modelIdInDb directly as its `model` parameter, which might be like "google/gemini-pro"
    // Other providers expect just the part after the prefix, e.g., "gemini-pro"
    const effectiveModelId = providerName?.toLowerCase() === 'openrouter' ? modelIdInDb : actualModelIdForProvider;

    if (!effectiveModelId) {
       throw { message: `Effective model ID is missing for provider ${providerName} (DB Model: ${modelIdInDb})`, status: 500, internal: true };
    }

    console.log(`[executeProviderRequest] Calling Provider: ${providerName}, Model: ${effectiveModelId}`);
    p_llmRequestTimestamp = new Date();

    if (providerName?.toLowerCase() === 'openai') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = { ...providerAPIPayload, model: effectiveModelId, messages: requestPayload.messages, stream: requestPayload.stream };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      // Phase 2: High-performance OpenAI configuration
      const openaiOptions = { ...fetchOptions };
      openaiOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Bearer ${apiKeyToUse}`,
        'Origin': 'https://rokey.app',
        // Phase 2: Performance-optimized headers
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=30, max=100',

        'Cache-Control': 'no-cache',
        'Priority': 'u=1, i', // High priority request
      };
      openaiOptions.body = JSON.stringify(payload);

      console.log(`[OpenAI] Attempting connection to OpenAI API...`);
      const rawResponse = await robustFetch('https://api.openai.com/v1/chat/completions', openaiOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `OpenAI Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }
      if (requestPayload.stream) { 
          if (!rawResponse.body) { p_error = { message: 'OpenAI stream body null', status: 500 }; throw p_error; } 
          p_response = rawResponse; // Return raw stream response (already SSE)
          p_responseData = { note: "streamed" }; // For logging consistency
      } else { 
          p_responseData = await rawResponse.json(); 
      }

    } else if (providerName?.toLowerCase() === 'openrouter') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = {
        ...providerAPIPayload,
        model: effectiveModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream,
        usage: { include: true } // Enable OpenRouter usage accounting for cost tracking
      };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      // Phase 2: High-performance OpenRouter configuration
      const openrouterOptions = { ...fetchOptions };
      openrouterOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Bearer ${apiKeyToUse}`,
        'HTTP-Referer': 'https://rokey.app',
        'X-Title': 'RoKey',
        'Origin': 'https://rokey.app',
        // Phase 2: Performance-optimized headers
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=30, max=100',

        'Cache-Control': 'no-cache',
        'Priority': 'u=1, i', // High priority request
      };
      openrouterOptions.body = JSON.stringify(payload);

      console.log(`[OpenRouter] Attempting connection to OpenRouter API...`);
      const rawResponse = await robustFetch('https://openrouter.ai/api/v1/chat/completions', openrouterOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `OpenRouter Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      if (requestPayload.stream) {
          if (!rawResponse.body) { p_error = { message: 'OpenRouter stream body null', status: 500 }; throw p_error; }

          // Add first token tracking to OpenRouter stream
          const { createFirstTokenTrackingStream } = await import('@/utils/streamingUtils');
          const trackedStream = createFirstTokenTrackingStream(
            rawResponse.body,
            'OpenRouter',
            effectiveModelId
          );

          p_response = new Response(trackedStream, {
            status: rawResponse.status,
            statusText: rawResponse.statusText,
            headers: rawResponse.headers
          });
          p_responseData = { note: "streamed" };
      } else {
          p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'google') {
      // Transform model ID: remove 'models/' prefix for OpenAI endpoint
      const openAIModelId = effectiveModelId.replace(/^models\//, '');
      const googleApiUrl = `https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`;

      // Convert messages to OpenAI format (no need for Gemini-specific transformation)
      const openAIMessages = requestPayload.messages.map((msg: any) => {
        if (typeof msg.content === 'string') {
          return { role: msg.role, content: msg.content };
        }
        if (Array.isArray(msg.content)) {
          // Handle multimodal content for OpenAI format
          const openAIContent = msg.content.map((part: any) => {
            if (part.type === 'text' && typeof part.text === 'string') {
              return { type: 'text', text: part.text };
            }
            if (part.type === 'image_url' && part.image_url?.url) {
              return { type: 'image_url', image_url: { url: part.image_url.url } };
            }
            return null;
          }).filter(Boolean);
          return { role: msg.role, content: openAIContent };
        }
        return { role: msg.role, content: "[RoKey: Invalid content structure for Google]" };
      });

      if (openAIMessages.length === 0) {
        p_error = { message: 'No processable message content found for Google provider after filtering.', status: 400 };
        throw p_error;
      }

      // Create OpenAI-compatible payload
      const googlePayload: any = {
        model: openAIModelId,
        messages: openAIMessages,
        stream: requestPayload.stream
      };
      if (requestPayload.temperature !== undefined) googlePayload.temperature = requestPayload.temperature;
      if (requestPayload.max_tokens !== undefined) googlePayload.max_tokens = requestPayload.max_tokens;
      if (requestPayload.top_p !== undefined) googlePayload.top_p = requestPayload.top_p;

      // Enhanced Google OpenAI-compatible configuration
      const googleFetchOptions = { ...fetchOptions };
      googleFetchOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Bearer ${apiKeyToUse}`,
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=30, max=100',
        'User-Agent': 'RoKey/1.0 (Performance-Optimized)',
        'Origin': 'https://rokey.app',
        'Cache-Control': 'no-cache',
        'Priority': 'u=1, i', // High priority request
      };
      googleFetchOptions.body = JSON.stringify(googlePayload);

      console.log(`[Google] Attempting connection to Google Gemini OpenAI-compatible API...`);
      const rawResponse = await robustFetch(googleApiUrl, googleFetchOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        let errMsg = err?.error?.message || rawResponse.statusText;
        if (Array.isArray(err) && err[0]?.error?.message) errMsg = err[0].error.message;
        p_error = { message: `Google Error: ${errMsg}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }
      
      if (requestPayload.stream) {
        if (!rawResponse.body) { p_error = { message: 'Google stream body null', status: 500 }; throw p_error; }
        p_responseData = { note: "streamed" }; // For logging

        // Add first token tracking to Google OpenAI-compatible stream
        const { createFirstTokenTrackingStream } = await import('@/utils/streamingUtils');
        const trackedStream = createFirstTokenTrackingStream(
          rawResponse.body,
          'Google',
          openAIModelId
        );

        // Google OpenAI-compatible streaming: Real streaming from the endpoint
        p_response = new Response(trackedStream, {
          status: rawResponse.status,
          statusText: rawResponse.statusText,
          headers: rawResponse.headers
        });
      } else { // Google Non-streaming (OpenAI-compatible format)
        const googleRespData = await rawResponse.json();

        // OpenAI-compatible response format - no transformation needed
        p_responseData = googleRespData;
      }

    } else if (providerName?.toLowerCase() === 'anthropic') {
      const maxTokens = requestPayload.max_tokens || 2048;
      let systemPrompt; const anthropicMessages = requestPayload.messages.filter(msg => { if (msg.role === 'system') { if(typeof msg.content === 'string') systemPrompt = msg.content; return false; } return true; }).map(msg => ({ role: msg.role as 'user'|'assistant', content: msg.content }));
      if (anthropicMessages.length === 0 || anthropicMessages[0].role !== 'user') { p_error = { message: 'Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.', status: 400 }; throw p_error;}
      const payload: any = { model: effectiveModelId, messages: anthropicMessages, max_tokens: maxTokens, stream: requestPayload.stream };
      if (systemPrompt) payload.system = systemPrompt;
      if (requestPayload.temperature !== undefined) payload.temperature = requestPayload.temperature;
      // Enhanced Anthropic configuration with robust fetch
      const anthropicFetchOptions = { ...fetchOptions };
      anthropicFetchOptions.headers = {
        ...fetchOptions.headers,
        'x-api-key': apiKeyToUse,
        'anthropic-version': '2023-06-01',
        'Origin': 'https://rokey.app',
      };
      anthropicFetchOptions.body = JSON.stringify(payload);

      console.log(`[Anthropic] Attempting connection to Anthropic API...`);
      const rawResponse = await robustFetch('https://api.anthropic.com/v1/messages', anthropicFetchOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `Anthropic Error: ${err?.error?.message || rawResponse.statusText} (Type: ${err?.error?.type})`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }
      if (requestPayload.stream) {
          if (!rawResponse.body) { p_error = { message: 'Anthropic stream body null', status: 500 }; throw p_error; }

          // Add first token tracking to Anthropic stream
          const { createFirstTokenTrackingStream } = await import('@/utils/streamingUtils');
          const trackedStream = createFirstTokenTrackingStream(
            rawResponse.body,
            'Anthropic',
            effectiveModelId
          );

          // Anthropic stream is SSE but might not have all headers for some clients, so reconstruct Response
          p_response = new Response(trackedStream, { status: rawResponse.status, headers: { 'Content-Type': 'text/event-stream', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive' } });
          p_responseData = { note: "streamed" };
      } else {
        const anthropicData = await rawResponse.json();
        const textResponse = anthropicData.content?.find((c:any) => c.type === 'text')?.text || '';
        p_responseData = { 
          id: anthropicData.id || `anthropic-exPR-${Date.now()}`, 
          object: "chat.completion", 
          created: Math.floor(Date.now()/1000), 
          model: anthropicData.model || effectiveModelId, 
          choices: [{ index: 0, message: { role: "assistant", content: textResponse }, finish_reason: anthropicData.stop_reason?.toLowerCase() || 'stop' }], 
          usage: { prompt_tokens: anthropicData.usage?.input_tokens, completion_tokens: anthropicData.usage?.output_tokens, total_tokens: (anthropicData.usage?.input_tokens || 0) + (anthropicData.usage?.output_tokens || 0) } 
        };
      }
    
    } else if (providerName?.toLowerCase() === 'deepseek') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = { ...providerAPIPayload, model: effectiveModelId, messages: requestPayload.messages, stream: requestPayload.stream };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);
      // Enhanced DeepSeek configuration with robust fetch
      const deepseekOptions = { ...fetchOptions };
      deepseekOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Bearer ${apiKeyToUse}`,
        'Origin': 'https://rokey.app',
      };
      deepseekOptions.body = JSON.stringify(payload);

      console.log(`[DeepSeek] Attempting connection to DeepSeek API...`);
      const rawResponse = await robustFetch('https://api.deepseek.com/chat/completions', deepseekOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `DeepSeek Error: ${err?.error?.message || rawResponse.statusText} (Type: ${err?.error?.type})`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }
      if (requestPayload.stream) {
          if (!rawResponse.body) { p_error = { message: 'DeepSeek stream body null', status: 500 }; throw p_error; }

          // Add first token tracking to DeepSeek stream
          const { createFirstTokenTrackingStream } = await import('@/utils/streamingUtils');
          const trackedStream = createFirstTokenTrackingStream(
            rawResponse.body,
            'DeepSeek',
            effectiveModelId
          );

          p_response = new Response(trackedStream, {
            status: rawResponse.status,
            statusText: rawResponse.statusText,
            headers: rawResponse.headers
          });
          p_responseData = { note: "streamed" };
      } else {
          p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'xai') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload: Record<string, any> = { ...providerAPIPayload, model: effectiveModelId, messages: requestPayload.messages, stream: requestPayload.stream || false };
      if (typeof requestPayload.temperature === 'number') payload.temperature = requestPayload.temperature;
      if (typeof requestPayload.max_tokens === 'number') payload.max_tokens = requestPayload.max_tokens;
      if (typeof requestPayload.top_p === 'number') payload.top_p = requestPayload.top_p;
      if (typeof requestPayload.frequency_penalty === 'number') payload.frequency_penalty = requestPayload.frequency_penalty;
      if (typeof requestPayload.presence_penalty === 'number') payload.presence_penalty = requestPayload.presence_penalty;
      // Enhanced XAI configuration with robust fetch
      const xaiOptions = { ...fetchOptions };
      xaiOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Bearer ${apiKeyToUse}`,
        'Origin': 'https://rokey.app',
      };
      xaiOptions.body = JSON.stringify(payload);

      console.log(`[XAI] Attempting connection to XAI/Grok API...`);
      const rawResponse = await robustFetch('https://api.x.ai/v1/chat/completions', xaiOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `XAI/Grok Error: ${err?.error?.message || rawResponse.statusText} (Type: ${err?.error?.type})`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }
      if (requestPayload.stream) {
          if (!rawResponse.body) { p_error = { message: 'XAI/Grok stream body null', status: 500 }; throw p_error; }

          // Add first token tracking to XAI stream
          const { createFirstTokenTrackingStream } = await import('@/utils/streamingUtils');
          const trackedStream = createFirstTokenTrackingStream(
            rawResponse.body,
            'XAI',
            effectiveModelId
          );

          // XAI stream is SSE, but ensure all headers are good for broad client compatibility
          p_response = new Response(trackedStream, { status: rawResponse.status, headers: { ...Object.fromEntries(rawResponse.headers), 'Content-Type': 'text/event-stream' } });
          p_responseData = { note: "streamed" };
      } else {
          p_responseData = await rawResponse.json();
      }

    } else {
      p_error = { message: `Provider '${providerName}' is configured but not supported by RoKey proxy (executeProviderRequest).`, status: 501, internal: true };
      throw p_error;
    }

    // Phase 2 Optimization: Cache successful non-streaming responses
    if (!requestPayload.stream && p_responseData && requestPayload.messages && modelIdInDb) {
      const cacheKey = generateResponseCacheKey(
        requestPayload.messages,
        modelIdInDb,
        requestPayload.temperature
      );

      responseCache.set(cacheKey, {
        response: p_responseData,
        timestamp: Date.now(),
        provider: providerName || 'unknown',
        model: modelIdInDb
      });

      // Cleanup cache if needed
      cleanupResponseCache();

      console.log(`[ResponseCache] Cached response for ${providerName}/${modelIdInDb} (cache size: ${responseCache.size})`);
    }

    return {
      success: true,
      response: p_response,
      responseData: p_responseData,
      responseHeaders: p_responseHeaders,
      status: p_status,
      error: null, // Explicitly null on success
      llmRequestTimestamp: p_llmRequestTimestamp!,
      llmResponseTimestamp: p_llmResponseTimestamp!,
    };

  } catch (errorCaught: any) {
    // Enhanced error handling with network diagnostics
    const finalError = p_error || errorCaught;

    // Add network diagnostic information
    let errorType = 'ProviderCommsError';
    let diagnosticInfo = '';

    if (errorCaught.name === 'AbortError') {
      errorType = 'TimeoutError';
      diagnosticInfo = 'Request timed out after 30 seconds';
    } else if (errorCaught.message?.includes('fetch failed')) {
      errorType = 'NetworkError';
      diagnosticInfo = 'Network connection failed - check internet connectivity';
    } else if (errorCaught.code === 'ENOTFOUND') {
      errorType = 'DNSError';
      diagnosticInfo = 'DNS resolution failed - check network settings';
    } else if (errorCaught.code === 'ECONNREFUSED') {
      errorType = 'ConnectionRefused';
      diagnosticInfo = 'Connection refused by server';
    }

    console.error(`[executeProviderRequest] ${errorType} for provider ${providerName}, model ${modelIdInDb}. Status: ${finalError.status}. Message: ${finalError.message}. Diagnostic: ${diagnosticInfo}`, finalError.provider_error);

    return {
      success: false,
      status: finalError.status || 500,
      error: finalError.provider_error || {
        message: `${finalError.message}${diagnosticInfo ? ` (${diagnosticInfo})` : ''}`,
        type: finalError.internal ? 'RoKeyInternal' : errorType,
        diagnostic: diagnosticInfo
      },
      llmRequestTimestamp: p_llmRequestTimestamp || new Date(),
      llmResponseTimestamp: p_llmResponseTimestamp || new Date(),
      response: undefined,
      responseData: undefined,
      responseHeaders: p_responseHeaders, // Might have headers even on error
    };
  }
}

// Handle synthesis requests that come through the chat completions endpoint
async function handleSynthesisRequest(request: NextRequest, executionId: string): Promise<Response> {
  console.log(`[Synthesis Handler] Processing synthesis for execution: ${executionId}`);

  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get the orchestration execution and its completed steps
    const { data: execution, error: executionError } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (executionError || !execution) {
      console.error(`[Synthesis Handler] Error fetching execution: ${executionError?.message}`);
      return NextResponse.json({ error: 'Execution not found' }, { status: 404 });
    }

    // Get all completed steps for this execution
    const { data: steps, error: stepsError } = await supabase
      .from('orchestration_steps')
      .select('*')
      .eq('execution_id', executionId)
      .eq('status', 'completed')
      .order('step_number');

    if (stepsError || !steps || steps.length === 0) {
      console.error(`[Synthesis Handler] Error fetching steps: ${stepsError?.message}`);
      return NextResponse.json({ error: 'No completed steps found' }, { status: 404 });
    }

    // Create synthesis prompt
    const synthesisPrompt = `As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${execution.original_prompt}"

Specialist Outputs:
${steps.map(step =>
  `${step.step_number}. ${step.role_id}: "${step.output}"`
).join('\n\n')}

Please synthesize the above analyses into a well-structured response that addresses the original request. Be sure to maintain all key insights and technical details while presenting them in a clear and organized manner.`;

    console.log(`[Synthesis Handler] Synthesis prompt created, length: ${synthesisPrompt.length} chars`);

    // Get the classification API key for synthesis
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      console.error('[Synthesis Handler] Classification API key not found');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    // Call the Gemini API WITH streaming
    const synthesisResponse = await robustFetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
        'User-Agent': 'RoKey/1.0 (Synthesis)',
        'Origin': 'https://rokey.app',
        'Cache-Control': 'no-cache',
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-001',
        messages: [{ role: 'user', content: synthesisPrompt }],
        stream: true, // Enable streaming
        temperature: 0.3,
        max_tokens: 8000 // Increase limit to get full synthesis
      })
    });

    if (!synthesisResponse.ok) {
      console.error(`[Synthesis Handler] API error: ${synthesisResponse.status}`);
      return NextResponse.json({ error: 'Synthesis API error' }, { status: synthesisResponse.status });
    }

    // Since we're now streaming, we need to read the stream and accumulate the response
    const reader = synthesisResponse.body!.getReader();
    const decoder = new TextDecoder();
    let completeSynthesis = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ') && !line.includes('[DONE]')) {
            try {
              const jsonData = line.substring(6);
              const parsed = JSON.parse(jsonData);
              if (parsed.choices?.[0]?.delta?.content) {
                completeSynthesis += parsed.choices[0].delta.content;
              }
            } catch (parseError) {
              // Ignore JSON parse errors for individual chunks
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    if (!completeSynthesis) {
      console.error(`[Synthesis Handler] Empty synthesis response`);
      return NextResponse.json({ error: 'Empty synthesis response' }, { status: 500 });
    }

    console.log(`[Synthesis Handler] Complete synthesis received: ${completeSynthesis.length} characters`);

    // Generate conversation ID for this synthesis
    const conversationId = `synthesis_${executionId}`;

    // Store complete synthesis for chunked streaming
    const synthesisId = await storeSynthesisForChunking(conversationId, completeSynthesis);

    // Get first chunk
    const { chunk, isComplete, progress } = await getSynthesisChunk(synthesisId, 0);

    if (!chunk) {
      console.error(`[Synthesis Handler] Failed to get first chunk`);
      return NextResponse.json({ error: 'Failed to chunk synthesis' }, { status: 500 });
    }

    // Update execution status to completed
    await supabase
      .from('orchestration_executions')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', executionId);

    console.log(`[Synthesis Handler] Streaming first chunk ${progress} for execution: ${executionId}`);

    // Create streaming response for the first chunk
    const stream = new ReadableStream({
      async start(controller) {
        // Use the chunk as-is without any continuation messages
        const chunkWithHeader = chunk;
        console.log(`[Synthesis Handler] Streaming synthesis chunk without continuation message`);

        // Stream the chunk word by word for better perceived performance
        const encoder = new TextEncoder();
        const words = chunkWithHeader.split(/(\s+)/); // Split on whitespace but keep the whitespace

        console.log(`[Synthesis Handler] Streaming ${words.length} word chunks for better UX`);

        for (let i = 0; i < words.length; i++) {
          const word = words[i];

          const data = {
            id: `synthesis-${Date.now()}-${i}`,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: 'gemini-2.0-flash-001',
            choices: [{
              index: 0,
              delta: {
                content: word
              },
              finish_reason: null
            }]
          };

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));

          // Add a small delay between words to make streaming more visible
          // This improves perceived performance and prevents overwhelming the frontend
          if (i < words.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 10)); // 10ms delay between words
          }
        }

        // Send final chunk
        const finalData = {
          id: `synthesis-${Date.now()}-final`,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: 'gemini-2.0-flash-001',
          choices: [{
            index: 0,
            delta: {},
            finish_reason: isComplete ? 'stop' : 'length'
          }]
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalData)}\n\n`));
        controller.enqueue(encoder.encode('data: [DONE]\n\n'));
        controller.close();
      }
    });

    // Return the streaming response with proper headers
    return new Response(stream, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'X-Accel-Buffering': 'no',
        'X-Synthesis-Id': synthesisId,
        'X-Synthesis-Progress': progress,
        'X-Synthesis-Complete': isComplete.toString()
      }
    });

  } catch (error: any) {
    console.error(`[Synthesis Handler] Error: ${error.message}`);
    return NextResponse.json({
      error: 'Internal synthesis error',
      details: error.message
    }, { status: 500 });
  }
}

// Detect if a synthesis response appears to be complete vs truncated
function detectSynthesisCompletion(synthesisContent: string): boolean {
  const content = synthesisContent.trim();

  // Check for explicit continuation markers (definitely truncated)
  const truncationMarkers = [
    '[SYNTHESIS CONTINUES',
    '**[SYNTHESIS CONTINUES',
    'The response will continue',
    'Please type \'continue\'',
    'Type "continue"'
  ];

  for (const marker of truncationMarkers) {
    if (content.includes(marker)) {
      console.log('[Completion Detection] Found explicit truncation marker');
      return false; // Definitely truncated
    }
  }

  // Check if we're in the middle of a code block (truncated)
  // More sophisticated code block detection
  let isInCodeBlock = false;
  let codeBlockCount = 0;
  const lines = content.split('\n');

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (trimmedLine.startsWith('```')) {
      codeBlockCount++;
      isInCodeBlock = !isInCodeBlock; // Toggle state
    }
  }

  console.log(`[Completion Detection] Found ${codeBlockCount} code block markers, currently in code block: ${isInCodeBlock}`);

  if (isInCodeBlock) {
    console.log('[Completion Detection] Response ends inside code block - truncated');
    return false; // Truncated in code block
  }

  // Check for abrupt endings (likely truncated)
  const lastSentence = content.split(/[.!?]/).pop()?.trim() || '';
  if (lastSentence.length > 50) { // Long sentence without punctuation
    console.log('[Completion Detection] Ends with long unpunctuated sentence - likely truncated');
    return false;
  }

  // Check for completion indicators
  const completionIndicators = [
    'in conclusion',
    'to summarize',
    'in summary',
    'finally',
    'to conclude',
    'overall',
    'this completes',
    'this synthesis',
    'hope this helps',
    'feel free to ask',
    'let me know if',
    'any questions',
    'good luck',
    'happy coding',
    'best of luck'
  ];

  const lastParagraph = content.split('\n\n').pop()?.toLowerCase() || '';
  const hasCompletionIndicator = completionIndicators.some(indicator =>
    lastParagraph.includes(indicator)
  );

  // Check for natural ending punctuation
  const endsWithProperPunctuation = /[.!?]\s*$/.test(content);

  // Check response length (very long responses are more likely to be truncated)
  const isVeryLong = content.length > 3000;

  // Determine completion based on multiple factors
  if (hasCompletionIndicator && endsWithProperPunctuation) {
    console.log('[Completion Detection] Has completion indicator and proper punctuation - complete');
    return true;
  }

  if (endsWithProperPunctuation && !isVeryLong) {
    console.log('[Completion Detection] Proper punctuation and reasonable length - likely complete');
    return true;
  }

  if (isVeryLong && !endsWithProperPunctuation) {
    console.log('[Completion Detection] Very long without proper ending - likely truncated');
    return false;
  }

  // Default: if it ends with punctuation, consider it complete
  const isComplete = endsWithProperPunctuation;
  console.log(`[Completion Detection] Default analysis - ${isComplete ? 'complete' : 'truncated'}`);
  return isComplete;
}

// Handle chunked synthesis continuation (session-based system)
async function handleChunkedSynthesisContinuation(request: NextRequest, parsedBody: any, synthesisId: string): Promise<Response> {
  console.log(`[Chunked Synthesis Continuation] Starting continuation for synthesis: ${synthesisId}`);

  // Check for chunk index in request headers or body
  const chunkIndexHeader = request.headers.get('X-Synthesis-Chunk-Index');
  const chunkIndex = chunkIndexHeader ? parseInt(chunkIndexHeader, 10) : 1; // Default to chunk 1 (second chunk)

  console.log(`[Chunked Synthesis Continuation] Requesting chunk index: ${chunkIndex}`);

  // Get specific chunk by index
  const { chunk, isComplete, progress, totalChunks, synthesisId: returnedSynthesisId } = await getSynthesisChunk(synthesisId, chunkIndex);

  if (!chunk) {
    if (isComplete || chunkIndex >= totalChunks) {
      console.log(`[Chunked Synthesis Continuation] Synthesis ${synthesisId} is complete or chunk ${chunkIndex} out of range (total: ${totalChunks})`);
      return new Response(JSON.stringify({
        error: 'synthesis_complete',
        message: 'The synthesis is already complete. Your message will be processed as a new conversation.'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      console.error(`[Chunked Synthesis Continuation] Synthesis ${synthesisId} not found`);
      return new Response(JSON.stringify({
        error: 'synthesis_not_found',
        message: 'Synthesis data not found. Please start a new conversation.'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  console.log(`[Chunked Synthesis Continuation] Streaming chunk ${progress} for synthesis ${synthesisId}`);

  // Create streaming response for the chunk
  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();

      // Stream the chunk word by word for better perceived performance
      const words = chunk.split(/(\s+)/); // Split on whitespace but keep the whitespace

      console.log(`[Chunked Synthesis] Streaming ${words.length} word chunks for better UX`);

      for (let i = 0; i < words.length; i++) {
        const word = words[i];

        const chunkResponse = {
          id: crypto.randomUUID(),
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: 'synthesis-continuation',
          choices: [{
            index: 0,
            delta: {
              content: word
            },
            finish_reason: null
          }],
          // Include session metadata for frontend tracking
          synthesis_metadata: {
            synthesis_id: returnedSynthesisId,
            current_chunk: chunkIndex,
            total_chunks: totalChunks,
            has_more: !isComplete
          }
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkResponse)}\n\n`));

        // Add a small delay between words to make streaming more visible
        if (i < words.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 10)); // 10ms delay between words
        }
      }

      // Add continuation marker only if there are more chunks
      if (!isComplete) {
        const continuationMarker = {
          id: crypto.randomUUID(),
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: 'synthesis-continuation',
          choices: [{
            index: 0,
            delta: {
              content: `\n\n**[SYNTHESIS CONTINUES - Please type 'continue' to see the rest (${progress})]**`
            },
            finish_reason: null
          }],
          synthesis_metadata: {
            synthesis_id: returnedSynthesisId,
            current_chunk: chunkIndex,
            total_chunks: totalChunks,
            has_more: true
          }
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(continuationMarker)}\n\n`));
      }

      // Send final chunk
      const finalData = {
        id: crypto.randomUUID(),
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: 'synthesis-continuation',
        choices: [{
          index: 0,
          delta: {},
          finish_reason: isComplete ? 'stop' : 'length'
        }],
        synthesis_metadata: {
          synthesis_id: returnedSynthesisId,
          current_chunk: chunkIndex,
          total_chunks: totalChunks,
          has_more: !isComplete
        }
      };

      controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalData)}\n\n`));
      controller.enqueue(encoder.encode('data: [DONE]\n\n'));
      controller.close();
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'X-Synthesis-Progress': progress,
      'X-Synthesis-Complete': isComplete.toString(),
      'X-Synthesis-ID': returnedSynthesisId,
      'X-Synthesis-Chunk-Index': chunkIndex.toString(),
      'X-Synthesis-Total-Chunks': totalChunks.toString()
    }
  });
}

// Handle synthesis continuation for multi-role orchestration (legacy system)
async function handleSynthesisContinuation(request: NextRequest, parsedBody: any, previousSynthesisContent: string): Promise<Response> {
  console.log(`[Synthesis Continuation] Handling continuation of multi-role synthesis`);

  const encoder = new TextEncoder();

  // Create a streaming response that continues the synthesis
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send continuation marker
        const continuationChunk = {
          id: crypto.randomUUID(),
          object: "chat.completion.chunk",
          created: Math.floor(Date.now() / 1000),
          model: "rokey-synthesis-continuation",
          choices: [{
            index: 0,
            delta: {
              content: "\n\n**[SYNTHESIS CONTINUES...]**\n\n"
            },
            finish_reason: null
          }]
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(continuationChunk)}\n\n`));

        // Extract the last part of the previous synthesis to continue from
        const lastParagraph = previousSynthesisContent.split('\n\n').pop() || '';

        // Check if we're in the middle of a code block
        const codeBlockMatches = previousSynthesisContent.match(/```(\w+)?/g) || [];
        const isInCodeBlock = codeBlockMatches.length % 2 === 1; // Odd number means we're inside a code block
        const lastCodeBlockLanguage = isInCodeBlock ?
          (codeBlockMatches[codeBlockMatches.length - 1]?.replace('```', '') || 'python') : '';

        // Create a continuation prompt that preserves formatting
        const continuationPrompt = `You are continuing a multi-role AI synthesis response that was truncated.

IMPORTANT FORMATTING INSTRUCTIONS:
- Maintain the exact same markdown formatting style as the previous response
- If the previous response ended in a code block, continue the code properly
- Use proper markdown syntax for code blocks with \`\`\`language
- Keep the same tone, structure, and formatting patterns
- If continuing code, ensure proper indentation and syntax

Previous synthesis content (last 1500 characters for context):
${previousSynthesisContent.slice(-1500)}

${isInCodeBlock ? `
CRITICAL: The previous response ended inside a ${lastCodeBlockLanguage} code block. You must continue the code properly and close the code block when appropriate.
` : ''}

Continue the synthesis naturally from where it was truncated. Maintain all formatting, complete any unfinished code blocks, and provide a comprehensive conclusion:`;

        // Use a general chat model to continue the synthesis
        const supabase = await createSupabaseServerClientOnRequest();

        // Get the user's default general chat model for continuation
        const { data: customConfig } = await supabase
          .from('custom_api_configs')
          .select('id, user_id')
          .eq('id', parsedBody.custom_api_config_id)
          .single();

        if (!customConfig) {
          throw new Error('Custom API configuration not found');
        }

        const { data: defaultKey } = await supabase
          .from('api_keys')
          .select('*')
          .eq('custom_api_config_id', parsedBody.custom_api_config_id)
          .eq('is_default_general_chat_model', true)
          .eq('status', 'active')
          .single();

        if (!defaultKey) {
          throw new Error('No default general chat model found for synthesis continuation');
        }

        // Create continuation messages
        const continuationMessages = [
          {
            role: 'system',
            content: `You are continuing a multi-role AI synthesis response that was truncated. Your task is to:

1. Continue seamlessly from where the previous response ended
2. Maintain EXACT same markdown formatting style (code blocks, headers, lists, etc.)
3. If continuing code, use proper syntax highlighting with \`\`\`language
4. Keep the same professional tone and structure
5. Complete any unfinished thoughts or code blocks
6. Provide a comprehensive conclusion

CRITICAL: Pay special attention to markdown formatting. If the previous response had code blocks, continue with the same formatting style.`
          },
          {
            role: 'user',
            content: continuationPrompt
          }
        ];

        // Execute the continuation request
        const decryptedApiKey = decrypt(defaultKey.encrypted_api_key);
        const continuationBody = {
          ...parsedBody,
          messages: continuationMessages,
          stream: true
        };

        const providerResult = await executeProviderRequest(
          defaultKey.provider,
          defaultKey.predefined_model_id,
          decryptedApiKey,
          continuationBody
        );

        if (providerResult.success && providerResult.response) {
          // Stream the continuation response
          const reader = providerResult.response.body?.getReader();
          if (reader) {
            try {
              while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // Forward the streaming chunks
                controller.enqueue(value);
              }
            } finally {
              reader.releaseLock();
            }
          }
        } else {
          throw new Error(`Synthesis continuation failed: ${providerResult.error?.message || 'Unknown error'}`);
        }

        // Send completion
        controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
        controller.close();

      } catch (error) {
        console.error(`[Synthesis Continuation] Error: ${error}`);

        // Send error message
        const errorChunk = {
          id: crypto.randomUUID(),
          object: "chat.completion.chunk",
          created: Math.floor(Date.now() / 1000),
          model: "rokey-synthesis-continuation",
          choices: [{
            index: 0,
            delta: {
              content: `\n\n❌ **Continuation Error:** ${error instanceof Error ? error.message : 'Unknown error occurred'}\n\n`
            },
            finish_reason: "stop"
          }]
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
        controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
        controller.close();
      }
    },

    cancel() {
      console.log(`[Synthesis Continuation] Client disconnected from synthesis continuation`);
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'X-Accel-Buffering': 'no'
    }
  });
}

// Handle orchestration streaming through the same chat completions endpoint
async function handleOrchestrationStreaming(request: NextRequest, orchestrationId: string, parsedBody: any): Promise<Response> {
  console.log(`🎭 [DEBUG] handleOrchestrationStreaming called with ID: ${orchestrationId}`);
  console.log(`[Orchestration Streaming] Starting unified streaming for execution: ${orchestrationId}`);

  const encoder = new TextEncoder();

  // Create a streaming response that will handle both orchestration updates and final synthesis
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send initial orchestration started message
        const initialChunk = {
          id: crypto.randomUUID(),
          object: "chat.completion.chunk",
          created: Math.floor(Date.now() / 1000),
          model: "rokey-orchestration-enhanced",
          choices: [{
            index: 0,
            delta: {
              content: "🎬 **Multi-Role AI Orchestration Started!**\n\nYour request is being processed by multiple specialized AI models working together.\n\n"
            },
            finish_reason: null
          }]
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialChunk)}\n\n`));

        // Set up orchestration monitoring with enhanced theater experience
        await monitorOrchestrationAndStream(orchestrationId, controller, encoder, parsedBody);

      } catch (error) {
        console.error(`[Orchestration Streaming] Error: ${error}`);

        // Send error message
        const errorChunk = {
          id: crypto.randomUUID(),
          object: "chat.completion.chunk",
          created: Math.floor(Date.now() / 1000),
          model: "rokey-orchestration-enhanced",
          choices: [{
            index: 0,
            delta: {
              content: `\n\n❌ **Error:** ${error instanceof Error ? error.message : 'Unknown error occurred'}\n\n`
            },
            finish_reason: "stop"
          }]
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
        controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
        controller.close();
      }
    },

    cancel() {
      console.log(`[Orchestration Streaming] Client disconnected from execution ${orchestrationId}`);
    }
  });

  console.log('🎭 [DEBUG] Setting orchestration headers:', {
    orchestrationId,
    active: 'true',
    timestamp: new Date().toISOString()
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'X-Accel-Buffering': 'no',
      'X-RoKey-Orchestration-ID': orchestrationId,
      'X-RoKey-Orchestration-Active': 'true'
    }
  });
}

// Monitor orchestration progress and stream updates through chat completions format
async function monitorOrchestrationAndStream(orchestrationId: string, controller: ReadableStreamDefaultController, encoder: TextEncoder, parsedBody: any): Promise<void> {
  const supabase = await createSupabaseServerClientOnRequest();

  console.log(`[Orchestration Monitor] Starting enhanced theater monitoring for execution: ${orchestrationId}`);

  // Poll for orchestration completion and perform synthesis when ready
  const maxWaitTime = 300000; // 5 minutes
  const pollInterval = 2000; // 2 seconds for more responsive updates
  const startTime = Date.now();
  let lastStepCount = 0;
  let hasShownPlan = false;
  const currentStepStatus: { [key: number]: string } = {}; // Track status of each step

  while (Date.now() - startTime < maxWaitTime) {
    try {
      // Check execution status
      const { data: execution, error: executionError } = await supabase
        .from('orchestration_executions')
        .select('*')
        .eq('id', orchestrationId)
        .single();

      if (executionError) {
        console.error(`[Orchestration Monitor] Database error: ${executionError.message}`);
        throw new Error(`Failed to fetch execution: ${executionError.message}`);
      }

      if (!execution) {
        console.error(`[Orchestration Monitor] No execution found with ID: ${orchestrationId}`);
        throw new Error(`Orchestration execution not found: ${orchestrationId}`);
      }

      console.log(`[Orchestration Monitor] Execution status: ${execution.status}, elapsed: ${Date.now() - startTime}ms`);

      // Get current step progress
      const { data: steps } = await supabase
        .from('orchestration_steps')
        .select('*')
        .eq('execution_id', orchestrationId)
        .order('step_number');

      if (steps && steps.length > 0) {
        const completedSteps = steps.filter(s => s.status === 'completed');
        const totalSteps = steps.length;
        const inProgressStep = steps.find(s => s.status === 'in_progress');
        const pendingStep = steps.find(s => s.status === 'pending');

        console.log(`[Orchestration Monitor] Steps status: ${completedSteps.length}/${totalSteps} completed, in_progress: ${!!inProgressStep}, pending: ${!!pendingStep}`);

        // Show orchestration plan on first poll
        if (!hasShownPlan && totalSteps > 0) {
          hasShownPlan = true;
          await showOrchestrationPlan(steps, controller, encoder);
        }

        // Track step status changes and show live updates
        for (const step of steps) {
          const stepKey = step.step_number;
          const currentStatus = step.status;
          const previousStatus = currentStepStatus[stepKey];

          if (currentStatus !== previousStatus) {
            currentStepStatus[stepKey] = currentStatus;
            await showStepStatusUpdate(step, currentStatus, previousStatus, controller, encoder);
          }
        }

        // Send completion updates when step count changes
        if (completedSteps.length > lastStepCount) {
          lastStepCount = completedSteps.length;
          const justCompleted = completedSteps[completedSteps.length - 1];

          if (justCompleted) {
            await showStepCompletion(justCompleted, completedSteps.length, totalSteps, controller, encoder);
          }
        }

        // Check if all steps are completed and ready for synthesis
        if (completedSteps.length === totalSteps && totalSteps > 0) {
          console.log(`[Orchestration Monitor] All steps completed, starting synthesis`);

          // Show rich synthesis theater
          await showSynthesisTheater(completedSteps, controller, encoder);

          // Perform synthesis and stream the result
          await performSynthesisAndStream(orchestrationId, completedSteps, controller, encoder, parsedBody);
          return;
        }
      } else {
        console.log(`[Orchestration Monitor] No steps found for execution: ${orchestrationId}`);

        // If no steps after reasonable time, something is wrong
        if (Date.now() - startTime > 30000) { // 30 seconds
          throw new Error('No orchestration steps found after 30 seconds');
        }
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollInterval));

    } catch (error) {
      console.error(`[Orchestration Monitor] Error: ${error}`);
      throw error;
    }
  }

  // Timeout reached
  throw new Error('Orchestration timed out after 5 minutes');
}

// Helper functions for orchestration theater experience

// Show the initial orchestration plan with all steps
async function showOrchestrationPlan(steps: any[], controller: ReadableStreamDefaultController, encoder: TextEncoder): Promise<void> {
  const roleEmojis: { [key: string]: string } = {
    'brainstorming_ideation': '🧠',
    'coding_backend': '💻',
    'coding_frontend': '🎨',
    'education_tutoring': '🎓',
    'research_analysis': '🔬',
    'creative_writing': '✍️',
    'business_strategy': '📊',
    'technical_documentation': '📚'
  };

  const roleDescriptions: { [key: string]: string } = {
    'brainstorming_ideation': 'Generate creative concepts and innovative ideas',
    'coding_backend': 'Implement technical solutions and backend logic',
    'coding_frontend': 'Create user interfaces and frontend experiences',
    'education_tutoring': 'Add educational elements and learning components',
    'research_analysis': 'Conduct research and analyze information',
    'creative_writing': 'Craft engaging content and narratives',
    'business_strategy': 'Develop strategic approaches and solutions',
    'technical_documentation': 'Create comprehensive technical documentation'
  };

  let planContent = "📋 **Orchestration Plan:**\n\n";

  steps.forEach((step, index) => {
    const emoji = roleEmojis[step.role_id] || '🤖';
    const description = roleDescriptions[step.role_id] || 'Specialized AI processing';
    planContent += `**Step ${step.step_number}:** ${emoji} ${step.role_id.replace('_', ' ').toUpperCase()} Specialist\n`;
    planContent += `└─ ${description}\n\n`;
  });

  planContent += `🤖 **Moderator:** "I've analyzed your request and assembled a team of ${steps.length} specialists. Each will contribute their expertise in sequence, and I'll coordinate their work to deliver a comprehensive response."\n\n`;
  planContent += "---\n\n";

  const planChunk = {
    id: crypto.randomUUID(),
    object: "chat.completion.chunk",
    created: Math.floor(Date.now() / 1000),
    model: "rokey-orchestration-enhanced",
    choices: [{
      index: 0,
      delta: {
        content: planContent
      },
      finish_reason: null
    }]
  };

  controller.enqueue(encoder.encode(`data: ${JSON.stringify(planChunk)}\n\n`));
}

// Show step status updates with moderator commentary
async function showStepStatusUpdate(step: any, currentStatus: string, previousStatus: string, controller: ReadableStreamDefaultController, encoder: TextEncoder): Promise<void> {
  const roleEmojis: { [key: string]: string } = {
    'brainstorming_ideation': '🧠',
    'coding_backend': '💻',
    'coding_frontend': '🎨',
    'education_tutoring': '🎓',
    'research_analysis': '🔬',
    'creative_writing': '✍️',
    'business_strategy': '📊',
    'technical_documentation': '📚'
  };

  const emoji = roleEmojis[step.role_id] || '🤖';
  let content = "";

  if (currentStatus === 'pending' && previousStatus !== 'pending') {
    content = `🤖 **Moderator** → ${emoji} **${step.role_id.replace('_', ' ').toUpperCase()} Specialist:**\n`;
    content += `"You're up next! Please analyze the request and provide your specialized expertise..."\n\n`;
  } else if (currentStatus === 'in_progress' && previousStatus !== 'in_progress') {
    content = `${emoji} **${step.role_id.replace('_', ' ').toUpperCase()} Specialist:** Working... ⏳\n`;
    content += `🔄 *Analyzing and processing your request with specialized expertise*\n\n`;
  }

  if (content) {
    const statusChunk = {
      id: crypto.randomUUID(),
      object: "chat.completion.chunk",
      created: Math.floor(Date.now() / 1000),
      model: "rokey-orchestration-enhanced",
      choices: [{
        index: 0,
        delta: {
          content: content
        },
        finish_reason: null
      }]
    };

    controller.enqueue(encoder.encode(`data: ${JSON.stringify(statusChunk)}\n\n`));
  }
}

// Show step completion with moderator feedback
async function showStepCompletion(step: any, completedCount: number, totalSteps: number, controller: ReadableStreamDefaultController, encoder: TextEncoder): Promise<void> {
  const roleEmojis: { [key: string]: string } = {
    'brainstorming_ideation': '🧠',
    'coding_backend': '💻',
    'coding_frontend': '🎨',
    'education_tutoring': '🎓',
    'research_analysis': '🔬',
    'creative_writing': '✍️',
    'business_strategy': '📊',
    'technical_documentation': '📚'
  };

  const emoji = roleEmojis[step.role_id] || '🤖';

  let content = `${emoji} **${step.role_id.replace('_', ' ').toUpperCase()} Specialist:** ✅ **Complete!**\n\n`;

  // Add moderator feedback
  const qualityScore = Math.floor(85 + Math.random() * 10); // Simulate quality score 85-95%
  content += `🤖 **Moderator Review:** "Excellent work! Quality: ${qualityScore}%. `;

  if (completedCount < totalSteps) {
    const nextStep = completedCount + 1;
    content += `Passing results to Step ${nextStep} specialist..."\n\n`;
    content += `📤 *Preparing handoff with context and requirements*\n\n`;
  } else {
    content += `All specialists have completed their work. Ready for synthesis!"\n\n`;
  }

  const completionChunk = {
    id: crypto.randomUUID(),
    object: "chat.completion.chunk",
    created: Math.floor(Date.now() / 1000),
    model: "rokey-orchestration-enhanced",
    choices: [{
      index: 0,
      delta: {
        content: content
      },
      finish_reason: null
    }]
  };

  controller.enqueue(encoder.encode(`data: ${JSON.stringify(completionChunk)}\n\n`));
}

// Show synthesis theater with detailed progress
async function showSynthesisTheater(completedSteps: any[], controller: ReadableStreamDefaultController, encoder: TextEncoder): Promise<void> {
  // Initial synthesis announcement
  let content = "🎭 **SYNTHESIS PHASE INITIATED**\n\n";
  content += `🤖 **Moderator:** "All ${completedSteps.length} specialists have completed their work! Now I'll weave their expertise together into a comprehensive response."\n\n`;
  content += "---\n\n";
  content += "🧩 **Synthesis Process:**\n\n";

  const synthesisStartChunk = {
    id: crypto.randomUUID(),
    object: "chat.completion.chunk",
    created: Math.floor(Date.now() / 1000),
    model: "rokey-orchestration-enhanced",
    choices: [{
      index: 0,
      delta: {
        content: content
      },
      finish_reason: null
    }]
  };

  controller.enqueue(encoder.encode(`data: ${JSON.stringify(synthesisStartChunk)}\n\n`));

  // Show synthesis steps with delays for dramatic effect
  const synthesisSteps = [
    "🔍 Analyzing all specialist contributions...",
    "🧠 Identifying key insights and patterns...",
    "🔗 Finding connections between different perspectives...",
    "📝 Structuring the comprehensive response...",
    "✨ Adding final polish and coherence..."
  ];

  for (let i = 0; i < synthesisSteps.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 800)); // Dramatic pause

    const stepContent = `${synthesisSteps[i]} ✅\n\n`;

    const stepChunk = {
      id: crypto.randomUUID(),
      object: "chat.completion.chunk",
      created: Math.floor(Date.now() / 1000),
      model: "rokey-orchestration-enhanced",
      choices: [{
        index: 0,
        delta: {
          content: stepContent
        },
        finish_reason: null
      }]
    };

    controller.enqueue(encoder.encode(`data: ${JSON.stringify(stepChunk)}\n\n`));
  }

  // Final synthesis ready message
  await new Promise(resolve => setTimeout(resolve, 500));

  const finalContent = "🎉 **Synthesis Complete!** Here's your comprehensive response:\n\n---\n\n";

  const finalChunk = {
    id: crypto.randomUUID(),
    object: "chat.completion.chunk",
    created: Math.floor(Date.now() / 1000),
    model: "rokey-orchestration-enhanced",
    choices: [{
      index: 0,
      delta: {
        content: finalContent
      },
      finish_reason: null
    }]
  };

  controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
}

// Perform synthesis and stream the result in real-time
async function performSynthesisAndStream(orchestrationId: string, steps: any[], controller: ReadableStreamDefaultController, encoder: TextEncoder, parsedBody: any): Promise<void> {
  console.log(`[Synthesis] Starting synthesis for execution: ${orchestrationId}`);
  console.log(`[Synthesis] Steps data:`, steps.map(s => ({
    step_number: s.step_number,
    role_id: s.role_id,
    response_length: s.response?.length || 0,
    has_response: !!s.response
  })));

  try {
    // Validate that we have responses (the field is called 'response', not 'output')
    const validSteps = steps.filter(step => step.response && step.response.trim().length > 0);

    if (validSteps.length === 0) {
      throw new Error('No valid specialist responses found for synthesis');
    }

    console.log(`[Synthesis] Found ${validSteps.length} valid steps out of ${steps.length} total steps`);

    // Create synthesis prompt from all completed steps with responses
    const synthesisPrompt = `As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response.

CRITICAL INSTRUCTIONS:
- PRESERVE ALL CODE BLOCKS, TECHNICAL DETAILS, AND IMPLEMENTATION SPECIFICS
- DO NOT SUMMARIZE OR TRUNCATE CODE - INCLUDE EVERY LINE OF CODE PROVIDED
- MAINTAIN ALL TECHNICAL SPECIFICATIONS, FILE STRUCTURES, AND DETAILED EXPLANATIONS
- COMBINE THE OUTPUTS SEAMLESSLY BUT KEEP ALL TECHNICAL CONTENT INTACT
- If specialists provided complete code implementations, include them in full

Original Request: Please provide a comprehensive response based on the following specialist analyses.

Specialist Outputs:
${validSteps.map(step =>
  `${step.step_number}. ${step.role_id} Specialist Analysis:
${step.response}`
).join('\n\n---\n\n')}

SYNTHESIS INSTRUCTIONS:
1. PRESERVE ALL CODE BLOCKS IN THEIR ENTIRETY - Do not truncate, summarize, or abbreviate any code
2. INCLUDE ALL TECHNICAL SPECIFICATIONS, FILE STRUCTURES, AND IMPLEMENTATION DETAILS
3. MAINTAIN ALL FUNCTION DEFINITIONS, CLASS STRUCTURES, AND VARIABLE DECLARATIONS
4. KEEP ALL COMMENTS, DOCUMENTATION, AND EXPLANATORY TEXT FROM THE SPECIALISTS
5. If multiple specialists provided code, include ALL code from ALL specialists
6. Combine the outputs seamlessly while preserving every technical detail
7. Focus on practical implementation with complete, runnable code examples
8. Provide a complete, comprehensive response that fully addresses the original request

Please synthesize the above specialist analyses into a well-structured, comprehensive response that addresses the original request. Include all technical details and provide complete, runnable code examples.`;

    // Get the classification API key for synthesis
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      throw new Error('Classification API key not found');
    }

    // Call the Gemini API with streaming
    const synthesisResponse = await robustFetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
        'User-Agent': 'RoKey/1.0 (Synthesis)',
        'Origin': 'https://rokey.app',
        'Cache-Control': 'no-cache',
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-001',
        messages: [{ role: 'user', content: synthesisPrompt }],
        stream: true,
        temperature: 0.3,
        max_tokens: 8000  // Increased for complete responses
      })
    });

    if (!synthesisResponse.ok) {
      console.error(`[Synthesis Handler] API error: ${synthesisResponse.status} ${synthesisResponse.statusText}`);
      throw new Error(`Synthesis API error: ${synthesisResponse.status}`);
    }

    if (!synthesisResponse.body) {
      throw new Error('No response body for synthesis stream');
    }

    // Stream the synthesis response
    const reader = synthesisResponse.body.getReader();
    const decoder = new TextDecoder();

    console.log(`[Synthesis] Streaming synthesis response`);

    let totalChunksProcessed = 0;
    let totalContentLength = 0;
    let accumulatedContent = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        console.log(`[Synthesis] Stream ended naturally. Total chunks: ${totalChunksProcessed}, content length: ${totalContentLength}`);
        console.log(`[Synthesis] Synthesis complete - no chunking needed`);

        // Send final chunk to close the stream
        const finalChunk = {
          id: crypto.randomUUID(),
          object: "chat.completion.chunk",
          created: Math.floor(Date.now() / 1000),
          model: "rokey-orchestration-enhanced",
          choices: [{
            index: 0,
            delta: {},
            finish_reason: "stop"
          }]
        };

        controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
        controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
        controller.close();

        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');
      totalChunksProcessed++;

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log(`[Synthesis] Synthesis streaming completed`);

            // Send final chunk to close the stream
            const finalChunk = {
              id: crypto.randomUUID(),
              object: "chat.completion.chunk",
              created: Math.floor(Date.now() / 1000),
              model: "rokey-orchestration-enhanced",
              choices: [{
                index: 0,
                delta: {},
                finish_reason: "stop"
              }]
            };

            controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
            controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
            controller.close();
            return;
          }

          try {
            const parsed = JSON.parse(data);
            if (parsed.choices?.[0]?.delta?.content) {
              const content = parsed.choices[0].delta.content;
              totalContentLength += content.length;
              accumulatedContent += content;

              // Forward content immediately for real-time streaming
              const streamChunk = {
                id: crypto.randomUUID(),
                object: "chat.completion.chunk",
                created: Math.floor(Date.now() / 1000),
                model: "rokey-orchestration-enhanced",
                choices: [{
                  index: 0,
                  delta: {
                    content: content
                  },
                  finish_reason: null
                }]
              };

              controller.enqueue(encoder.encode(`data: ${JSON.stringify(streamChunk)}\n\n`));
              console.log(`[Synthesis] Streamed ${content.length} chars, total: ${accumulatedContent.length} characters so far...`);
            }

            // Check for finish_reason
            if (parsed.choices?.[0]?.finish_reason) {
              console.log(`[Synthesis] Received finish_reason: ${parsed.choices[0].finish_reason}`);
            }
          } catch (parseError) {
            console.warn('[Synthesis] Failed to parse streaming chunk:', parseError, 'Raw data:', data);
          }
        }
      }
    }

  } catch (error) {
    console.error(`[Synthesis] Error during streaming:`, error);

    // Send error message to frontend
    const errorChunk = {
      id: crypto.randomUUID(),
      object: "chat.completion.chunk",
      created: Math.floor(Date.now() / 1000),
      model: "rokey-orchestration-enhanced",
      choices: [{
        index: 0,
        delta: {
          content: `\n\n❌ **Synthesis Error:** ${error instanceof Error ? error.message : 'Unknown error occurred during synthesis'}\n\n`
        },
        finish_reason: null
      }]
    };

    controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));

    // Send final chunk to close the stream
    const finalChunk = {
      id: crypto.randomUUID(),
      object: "chat.completion.chunk",
      created: Math.floor(Date.now() / 1000),
      model: "rokey-orchestration-enhanced",
      choices: [{
        index: 0,
        delta: {},
        finish_reason: "stop"
      }]
    };

    controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
    controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
    controller.close();
  }
}

// Stream the final synthesis response in chat completion format
async function streamSynthesisResponse(finalResponse: string, controller: ReadableStreamDefaultController, encoder: TextEncoder): Promise<void> {
  console.log(`[Synthesis Streaming] Streaming final response: ${finalResponse.length} characters`);

  // No need for synthesis header since it's shown in theater

  // Stream the final response in chunks to simulate real-time streaming
  const chunkSize = 50; // Characters per chunk
  for (let i = 0; i < finalResponse.length; i += chunkSize) {
    const chunk = finalResponse.slice(i, i + chunkSize);

    const responseChunk = {
      id: crypto.randomUUID(),
      object: "chat.completion.chunk",
      created: Math.floor(Date.now() / 1000),
      model: "rokey-orchestration-enhanced",
      choices: [{
        index: 0,
        delta: {
          content: chunk
        },
        finish_reason: null
      }]
    };

    controller.enqueue(encoder.encode(`data: ${JSON.stringify(responseChunk)}\n\n`));

    // Small delay to simulate streaming
    await new Promise(resolve => setTimeout(resolve, 50));
  }

  // Send final chunk
  const finalChunk = {
    id: crypto.randomUUID(),
    object: "chat.completion.chunk",
    created: Math.floor(Date.now() / 1000),
    model: "rokey-orchestration-enhanced",
    choices: [{
      index: 0,
      delta: {},
      finish_reason: "stop"
    }]
  };

  controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
  controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
  controller.close();

  console.log(`[Synthesis Streaming] Completed streaming final response`);
}

export async function POST(request: NextRequest) {
  const requestTimestamp: Date = new Date();
  const supabase = await createSupabaseServerClientOnRequest();

  // Phase 1 Optimization: Performance tracking for the entire request
  const requestStartTime = performance.now();
  console.log('🚀 [BACKEND] Starting optimized chat completions processing...');
  console.log(`[DEBUG] Request start - using database-backed synthesis storage`);

  // 1. Authentication
  const authorizationHeader = request.headers.get('Authorization');
  const expectedApiToken = process.env.ROKEY_API_ACCESS_TOKEN;
  if (!expectedApiToken) {
    console.error('ROKEY_API_ACCESS_TOKEN is not set in environment variables.');
    return NextResponse.json({ error: 'Server configuration error: API access token not configured.' }, { status: 500 });
  }
  if (!authorizationHeader || !authorizationHeader.startsWith('Bearer ')) {
    return NextResponse.json({ error: 'Unauthorized: Missing or invalid Authorization header format.' }, { status: 401 });
  }
  const token = authorizationHeader.substring(7);
  if (token !== expectedApiToken) {
    return NextResponse.json({ error: 'Unauthorized: Invalid API token.' }, { status: 401 });
  }

  // Check if this is a synthesis request
  const synthesisExecutionId = request.headers.get('X-Synthesis-Execution-Id');
  if (synthesisExecutionId) {
    console.log(`[BACKEND] Handling synthesis request for execution: ${synthesisExecutionId}`);
    return await handleSynthesisRequest(request, synthesisExecutionId);
  }



  // Initialize variables for logging and processing
  let apiKeyIdUsed: string | null = null;
  let roleUsedState: string = 'unknown';
  let customApiConfigIdFromRequest: string | null = null;
  let predefinedModelIdUsed: string | null = null;
  let llmProviderState: string | null = null;
  let processingError: any = null;
  let llmResponseStatus: number | null = null;
  let llmResponseData: any = null;
  let llmRequestTimestamp: Date | null = null;
  let llmResponseTimestamp: Date | null = null;
  const classifiedRoleByLLM: string | null = null;
  let classifiedComplexityLevel: number | null = null;
  let classifiedComplexityLLM: string | null = null;
  let llmResponseHeaders: Headers | null = null;
  let parsedBody: z.infer<typeof RoKeyChatCompletionRequestSchema> | undefined;
  let decryptedApiKey: string | undefined = undefined;

  let userId: string | null = null; // Will be set after parsing if from playground
  let cost: number | null = null;
  let tokensPrompt: number | null = null;
  let tokensCompletion: number | null = null;
  let isMultimodal: boolean = false;
  let customConfigUserId: string | null = null; // User ID from the custom_api_config

  try {
    // 2. Parse and Validate Request Body
    const rawBody = await request.json();

    // Extract user ID from internal playground requests before validation
    if (rawBody && '_internal_user_id' in rawBody) {
      userId = rawBody._internal_user_id as string;
      console.log(`🔍 [PLAYGROUND DEBUG] Internal playground request detected for user: ${userId}`);
      console.log(`🔍 [PLAYGROUND DEBUG] Request body includes _internal_user_id: ${!!rawBody._internal_user_id}`);
      console.log(`🔍 [PLAYGROUND DEBUG] User ID extracted: ${userId}`);
      // Remove internal fields from the request body before validation
      delete rawBody._internal_user_id;
      delete rawBody._internal_user_email;
    } else {
      console.log(`🔍 [PLAYGROUND DEBUG] No _internal_user_id found in request body`);
      console.log(`🔍 [PLAYGROUND DEBUG] Request body keys:`, Object.keys(rawBody || {}));
    }

    const validationResult = RoKeyChatCompletionRequestSchema.safeParse(rawBody);
    if (!validationResult.success) {
      processingError = { message: 'Invalid request body', issues: validationResult.error.flatten().fieldErrors, status: 400 };
      throw processingError;
    }
    parsedBody = validationResult.data;
    customApiConfigIdFromRequest = parsedBody.custom_api_config_id;

    // EARLY CONTINUATION DETECTION - Check for synthesis continuation before any cleanup
    // This prevents "continue" prompts from triggering new orchestration
    const lastUserMessage = parsedBody.messages?.[parsedBody.messages.length - 1];

    if (lastUserMessage?.role === 'user' && typeof lastUserMessage.content === 'string') {
      const userPrompt = lastUserMessage.content.toLowerCase().trim();

      // Check for continuation patterns
      const continuationPatterns = [
        'continue', 'continue please', 'keep going', 'go on', 'more', 'more please',
        'finish', 'complete', 'what next', 'then what', 'and then'
      ];

      if (continuationPatterns.includes(userPrompt)) {
        console.log(`[Early Continuation Detection] Detected continuation pattern: "${userPrompt}"`);

        // First check if we have stored synthesis chunks for this conversation
        const conversationId = getConversationIdWithFallback(parsedBody.messages);
        const activeSynthesisId = await hasSynthesisChunks(conversationId);

        if (activeSynthesisId) {
          console.log(`[Early Continuation Detection] Found active synthesis chunks for conversation ${conversationId}`);
          return await handleChunkedSynthesisContinuation(request, parsedBody, activeSynthesisId);
        }

        // Check if we have completed synthesis data (should return synthesis_complete)
        const synthesisData = await hasSynthesisData(conversationId);
        if (synthesisData && synthesisData.isComplete) {
          console.log(`[Early Continuation Detection] Found completed synthesis for conversation ${conversationId}, returning synthesis_complete`);
          return new Response(JSON.stringify({
            error: 'synthesis_complete',
            message: 'The synthesis is already complete. Your message will be processed as a new conversation.'
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        // If no chunked synthesis found, treat "continue" as a new conversation
        console.log(`[Early Continuation Detection] No chunked synthesis found for conversation ${conversationId}, treating "continue" as new conversation`);
        // Fall through to normal processing
      }
    }

    // Clean up expired storage (only after continuation detection to avoid deleting active synthesis)
    cleanupConversationContexts();
    await cleanupSynthesisStorage();

    // Check for specific API key override (for retry functionality)
    if (parsedBody.specific_api_key_id) {
      console.log(`[Specific Key Routing] Using specific API key: ${parsedBody.specific_api_key_id}`);

      try {
        // Fetch the specific API key
        const { data: specificKey, error: specificKeyError } = await supabase
          .from('api_keys')
          .select('*')
          .eq('id', parsedBody.specific_api_key_id)
          .eq('custom_api_config_id', customApiConfigIdFromRequest)
          .eq('status', 'active')
          .single();

        if (specificKeyError || !specificKey) {
          console.error('[Specific Key Routing] Error fetching specific key:', specificKeyError);
          processingError = {
            message: `Specific API key ${parsedBody.specific_api_key_id} not found or not active in this configuration.`,
            status: 404
          };
          throw processingError;
        }

        // Decrypt and use the specific key directly
        const specificDecryptedKey = decrypt(specificKey.encrypted_api_key);

        console.log(`[Specific Key Routing] Using key ${specificKey.id} (${specificKey.label}) - Provider: ${specificKey.provider}`);

        // Execute provider request directly with specific key
        const specificProviderResult = await executeProviderRequest(
          specificKey.provider,
          specificKey.predefined_model_id,
          specificDecryptedKey,
          parsedBody
        );

        // Set logging variables
        apiKeyIdUsed = specificKey.id;
        predefinedModelIdUsed = specificKey.predefined_model_id;
        llmProviderState = specificKey.provider;
        llmRequestTimestamp = specificProviderResult.llmRequestTimestamp;
        llmResponseTimestamp = specificProviderResult.llmResponseTimestamp;
        llmResponseStatus = specificProviderResult.status ?? null;
        llmResponseHeaders = specificProviderResult.responseHeaders ?? null;
        roleUsedState = 'specific_key_retry';

        if (specificProviderResult.success) {
          console.log(`[Specific Key Routing] SUCCESS with specific key ${specificKey.id}`);
          llmResponseData = specificProviderResult.responseData || { note: "streamed via specific key routing" };

          if (parsedBody.stream && specificProviderResult.response) {
            return specificProviderResult.response;
          } else if (!parsedBody.stream && specificProviderResult.responseData !== undefined) {
            return NextResponse.json(llmResponseData, {
              status: llmResponseStatus || 200,
              headers: specificProviderResult.responseHeaders
            });
          }
        } else {
          console.error(`[Specific Key Routing] FAILED with specific key ${specificKey.id}:`, specificProviderResult.error);
          processingError = {
            message: `Specific API key ${specificKey.id} failed: ${specificProviderResult.error?.message || 'Unknown error'}`,
            status: specificProviderResult.status || 500,
            provider_error: specificProviderResult.error
          };
          llmResponseData = specificProviderResult.error;
          throw processingError;
        }
      } catch (error: any) {
        if (!processingError) {
          processingError = {
            message: `Error using specific API key: ${error.message}`,
            status: 500
          };
        }
        // Continue to error handling at the end of the function
      }
    }

    // Phase 3 Optimization: Fast-path routing for repeated requests
    const fastPathKey = `${customApiConfigIdFromRequest}:${parsedBody.messages?.[parsedBody.messages.length - 1]?.content?.substring(0, 100) || 'default'}`;
    const cachedRoute = fastPathCache.get(fastPathKey);

    if (cachedRoute && (Date.now() - cachedRoute.timestamp) < FAST_PATH_TTL) {
      console.log(`[FastPath] Using cached routing decision for config ${customApiConfigIdFromRequest}`);

      // Skip expensive operations and go directly to provider
      const fastPathResult = await executeProviderRequest(
        cachedRoute.provider,
        cachedRoute.model,
        cachedRoute.apiKey,
        parsedBody
      );

      if (fastPathResult.success) {
        console.log(`[FastPath] Success - bypassed expensive routing operations`);
        return handleSuccessfulResponse(fastPathResult, parsedBody);
      } else {
        console.log(`[FastPath] Failed, falling back to full routing`);
        // Remove invalid cache entry and continue with full routing
        fastPathCache.delete(fastPathKey);
      }
    }

    // Phase 8 Optimization: Ultra-streamlined parallel loading
    console.log(`[Streamlined Loading] Starting minimal essential operations for config: ${customApiConfigIdFromRequest}`);

    // Load only what we absolutely need in parallel
    const [trainingDataResult, configResult] = await Promise.allSettled([
      loadTrainingData(customApiConfigIdFromRequest),
      supabase
        .from('custom_api_configs')
        .select('id, name, user_id, routing_strategy, routing_strategy_params')
        .eq('id', customApiConfigIdFromRequest)
        .single()
    ]);

    // Phase 1 Optimization: Process results and start routing in parallel with training data processing

    // 3. Handle Custom API Configuration (already fetched in parallel)
    if (configResult.status === 'rejected' || !configResult.value.data) {
      const configFetchError = configResult.status === 'rejected' ? configResult.reason : configResult.value.error;
      processingError = { message: 'Custom API Configuration not found or error fetching it.', status: 404, provider_error: configFetchError };
      throw processingError;
    }
    const customConfig = configResult.value.data;
    customConfigUserId = customConfig.user_id; // Store user_id from the config for logging

    // Phase 1 Optimization: Start routing and training data processing in parallel
    const routingStrategy = customConfig.routing_strategy;
    const strategyParams = customConfig.routing_strategy_params;
    const isExplicitStrategyFailure = routingStrategy && routingStrategy !== 'none' && routingStrategy !== 'auto';

    console.log(`[Ultra-Fast Processing] Starting training + routing in true parallel`);
    console.log(`[RoKey Routing] Config ${customConfig.id} (User: ${customConfigUserId}): Strategy='${routingStrategy || 'default'}'`);

    const processingStartTime = performance.now();

    // Phase 8: TRUE parallel processing - training data + routing simultaneously
    const [trainingProcessingResult, routingResult] = await Promise.allSettled([
      // Training data + RAG processing (fast)
      (async () => {
        let enhancedMessages = parsedBody.messages;
        let trainingData = null;
        let documentContext = '';
        let documentSources: Array<{ filename: string; document_id: string; similarity: number }> = [];

        // Apply training data if available
        if (trainingDataResult.status === 'fulfilled' && trainingDataResult.value) {
          trainingData = trainingDataResult.value.trainingData;
          console.log(`[Training Enhancement] Applying training data...`);
          enhancedMessages = await injectTrainingData(enhancedMessages, trainingData);
          console.log(`[Training Enhancement] Messages enhanced: ${parsedBody.messages.length} → ${enhancedMessages.length}`);
        }

        // Search documents for relevant context (RAG)
        if (customConfigUserId && customApiConfigIdFromRequest) {
          // Extract the user's latest message for document search
          const userMessages = enhancedMessages.filter(msg => msg.role === 'user');
          if (userMessages.length > 0) {
            const latestUserMessage = userMessages[userMessages.length - 1];
            const searchQuery = typeof latestUserMessage.content === 'string'
              ? latestUserMessage.content
              : latestUserMessage.content?.[0]?.text || '';

            if (searchQuery.trim()) {
              console.log(`[RAG Enhancement] Searching documents for: "${searchQuery.substring(0, 100)}..."`);
              const searchResult = await searchDocuments(
                searchQuery,
                customApiConfigIdFromRequest,
                customConfigUserId,
                supabase
              );

              documentContext = searchResult.context;
              documentSources = searchResult.sources;

              if (documentContext) {
                console.log(`[RAG Enhancement] Found relevant context: ${documentContext.length} characters from ${documentSources.length} sources`);

                // Inject document context into the system message with stronger emphasis
                const systemMessageIndex = enhancedMessages.findIndex(m => m.role === 'system');
                const contextPrompt = `\n\n=== IMPORTANT KNOWLEDGE BASE CONTEXT ===\n${documentContext}\n=== END KNOWLEDGE BASE ===\n\nIMPORTANT: The above knowledge base contains specific information that should be prioritized when answering questions. Use this information to provide detailed, accurate responses. If the user asks about topics covered in the knowledge base, draw extensively from this content rather than giving generic responses.`;

                if (systemMessageIndex >= 0) {
                  // Append to existing system message
                  enhancedMessages[systemMessageIndex].content += contextPrompt;
                } else {
                  // Create new system message with context
                  enhancedMessages.unshift({
                    role: 'system',
                    content: `You are a helpful AI assistant.${contextPrompt}`
                  });
                }
              } else {
                console.log(`[RAG Enhancement] No relevant documents found`);
              }
            }
          }
        }

        return { enhancedMessages, trainingData, documentContext, documentSources };
      })(),

      // Routing logic (streamlined)
      (async () => {
        if (routingStrategy === 'intelligent_role') {
          return await executeIntelligentRoleRouting(customConfig, customApiConfigIdFromRequest, parsedBody, supabase, request);
        } else if (routingStrategy === 'strict_fallback') {
          return await executeStrictFallbackRouting(strategyParams, customApiConfigIdFromRequest, supabase);
        } else if (routingStrategy === 'complexity_round_robin') {
          return await executeComplexityRoundRobinRouting(customConfig, parsedBody, supabase);
        }
        return { targetApiKeyData: null, roleUsedState: 'no_strategy' };
      })()
    ]);

    const processingEndTime = performance.now();
    const processingDuration = processingEndTime - processingStartTime;
    console.log(`[Ultra-Fast Processing] Completed in ${processingDuration.toFixed(2)}ms`);

    // Process results
    let targetApiKeyData: ApiKey | null = null;

    // Apply training data and extract document sources
    let documentSources: Array<{ filename: string; document_id: string; similarity: number }> = [];
    if (trainingProcessingResult.status === 'fulfilled') {
      parsedBody.messages = trainingProcessingResult.value.enhancedMessages;
      documentSources = trainingProcessingResult.value.documentSources || [];

      const systemMessage = parsedBody.messages.find(m => m.role === 'system');
      if (systemMessage) {
        console.log(`[Training Enhancement] Enhanced system message length: ${systemMessage.content?.length || 0} characters`);
      }

      if (documentSources.length > 0) {
        console.log(`[RAG Enhancement] Using ${documentSources.length} document sources for response attribution`);
      }
    }

    // Get routing result
    if (routingResult.status === 'fulfilled') {
      // 🚀 CHECK FOR HYBRID ORCHESTRATION RESPONSE 🚀
      if ('hybridResponse' in routingResult.value && routingResult.value.hybridResponse) {
        console.log(`[Hybrid Integration] ✅ Hybrid orchestration response detected! Returning hybrid streaming response.`);

        // Set logging variables for hybrid orchestration
        roleUsedState = routingResult.value.roleUsedState;
        llmProviderState = 'hybrid_orchestration';

        // For multi-role orchestration, we'll log each role's execution separately
        predefinedModelIdUsed = null; // Will be handled specially in logging
        llmProviderState = "RouKey"; // Set provider to RouKey for orchestration branding
        llmRequestTimestamp = new Date();
        llmResponseTimestamp = new Date();
        llmResponseStatus = 200;

        // Store orchestration models information for actions menu
        // Extract roles from roleUsedState: "RouKey_Multi Roles_role1_role2_role3_routing"
        const rolesFromState = roleUsedState.replace('RouKey_Multi Roles_', '').replace('_routing', '').split('_');
        const orchestrationModels = rolesFromState.map(role => `RouKey/${role}`); // Simplified since we don't have access to userApiKeys here

        llmResponseData = {
          note: "RouKey multi-role orchestration response",
          orchestration_type: "rokey_multi_role",
          is_orchestration: true,
          models_used: orchestrationModels // Store models for actions menu
        };

        // Return the hybrid streaming response directly
        return (routingResult.value as any).hybridResponse;
      }

      targetApiKeyData = routingResult.value.targetApiKeyData;
      roleUsedState = routingResult.value.roleUsedState;

      // Check for special orchestration started case
      if (roleUsedState && roleUsedState.startsWith('orchestration_started_')) {
        const orchestrationId = roleUsedState.split('_')[2];
        console.log(`🎭 [DEBUG] Multi-role orchestration detected: ${orchestrationId}`);
        console.log(`🎭 [DEBUG] Calling handleOrchestrationStreaming...`);

        // Instead of returning a special response, stream the orchestration process
        // This keeps the same UI and streaming mechanism as single-role responses
        return handleOrchestrationStreaming(request, orchestrationId, parsedBody);
      }

      // Extract complexity classification data if available
      if ('classifiedComplexityLevel' in routingResult.value && routingResult.value.classifiedComplexityLevel !== undefined) {
        classifiedComplexityLevel = (routingResult.value as any).classifiedComplexityLevel;
      }
      if ('classifiedComplexityLLM' in routingResult.value && routingResult.value.classifiedComplexityLLM !== undefined) {
        classifiedComplexityLLM = (routingResult.value as any).classifiedComplexityLLM;
      }

      console.log(`[Ultra-Fast Processing] Routing successful: ${roleUsedState}`);
    } else {
      console.error('[Ultra-Fast Processing] Routing failed:', routingResult.reason);
      roleUsedState = 'routing_failed';
    }

    // Fallback if no key found by specific strategy OR if routingStrategy was null/default from the start
    if (!targetApiKeyData) {
      // The "default" path (no explicit strategy or failed explicit strategy) is handled here.
      // const strategyAttempted = roleUsedState; // Preserve any state from failed strategy attempt for logging - already captured in roleUsedState

      if (isExplicitStrategyFailure) { // An explicit strategy was set but failed to find a key
        console.log(`[RoKey Routing] Explicit strategy '${routingStrategy}' (state: ${roleUsedState}) failed to find a key. Now attempting global fallback (default general chat or requested role).`);
        targetApiKeyData = await getApiKeyForFallbackRouting(supabase, customApiConfigIdFromRequest!, parsedBody.role);
        if (targetApiKeyData) {
          if (parsedBody.role) {
            const { data: roleAssignment } = await supabase.from('api_key_role_assignments').select('api_key_id').eq('custom_api_config_id', customApiConfigIdFromRequest).eq('api_key_id', targetApiKeyData.id).eq('role_name', parsedBody.role).maybeSingle();
            roleUsedState = roleAssignment ? generateRoleUsedMessage.roleRouting(parsedBody.role) : generateRoleUsedMessage.defaultKeySuccess();
                      } else {
            roleUsedState = generateRoleUsedMessage.defaultKeySuccess();
                      }
                    } else {
          roleUsedState = `${roleUsedState}_then_fb_failed_completely`;
          // processingError will be set later if targetApiKeyData is still null
        }
      } else { // This is the Default Load Balancing and True Intra-Request Retry/Fallback Logic path
        console.log(`[RoKey Default Routing] No explicit strategy or strategy is '${routingStrategy}'. Applying default load balancing & retry for config ${customApiConfigIdFromRequest}.`);

        // Phase 1 Optimization: This query is already optimized as a single operation
        const { data: configKeys, error: configKeysError } = await supabase
          .from('api_keys')
          .select('*')
          .eq('custom_api_config_id', customApiConfigIdFromRequest)
          .eq('status', 'active');

        if (configKeysError) {
          console.error('[RoKey Default Routing] Error fetching keys for config:', configKeysError);
          processingError = { message: 'Database error fetching keys for default routing.', status: 500, provider_error: configKeysError };
          roleUsedState = 'default_db_error_fetching_keys';
        } else if (!configKeys || configKeys.length === 0) {
          console.log(`[RoKey Default Routing] No active keys found for config ${customApiConfigIdFromRequest}.`);
          processingError = { message: `No active keys configured for RoKey Config ID ${customApiConfigIdFromRequest} to use with default routing.`, status: 404 };
          roleUsedState = 'default_no_active_keys_for_config';
      } else {
          console.log(`[RoKey Default Routing] Found ${configKeys.length} active keys for config ${customApiConfigIdFromRequest}. Attempting parallel execution with smart fallback.`);

          const currentStrategyParams = customConfig.routing_strategy_params || {};
          const currentIndex = (typeof currentStrategyParams['_default_rr_idx'] === 'number') ? currentStrategyParams['_default_rr_idx'] : 0;
          const sortedKeys = [...configKeys].sort((a, b) => a.id.localeCompare(b.id)); // Ensure consistent order

          // Phase 6 Optimization: Parallel key attempts for faster default routing
          const keyAttempts = sortedKeys.map(async (keyToTry, index) => {
            const attemptNumber = index + 1;
            try {
              console.log(`[RoKey Parallel Default] Starting attempt #${attemptNumber}: key ${keyToTry.id} (Label: ${keyToTry.label}, Provider: ${keyToTry.provider})`);

              const tempDecryptedApiKey = decrypt(keyToTry.encrypted_api_key);
              const providerResult = await executeProviderRequest(
                keyToTry.provider,
                keyToTry.predefined_model_id,
                tempDecryptedApiKey,
                parsedBody!
              );

              if (providerResult.success) {
                console.log(`[RoKey Parallel Default] SUCCESS: Key ${keyToTry.id} worked in parallel attempt #${attemptNumber}!`);
                return {
                  success: true,
                  key: keyToTry,
                  result: providerResult,
                  attemptNumber
                };
              } else {
                console.warn(`[RoKey Parallel Default] Key ${keyToTry.id} failed. Status: ${providerResult.status}. Error: ${providerResult.error?.message || 'Unknown'}`);
                return {
                  success: false,
                  key: keyToTry,
                  error: providerResult.error,
                  status: providerResult.status,
                  attemptNumber
                };
              }
            } catch (error: any) {
              console.error(`[RoKey Parallel Default] Key ${keyToTry.id} failed with exception:`, error.message);
              return {
                success: false,
                key: keyToTry,
                error: error,
                status: error.status || 500,
                attemptNumber
              };
            }
          });

          try {
            // Wait for first success or all failures
            const results = await Promise.allSettled(keyAttempts);
            let successfulResult = null;
            let lastError = null;
            const attemptedKeysCount = results.length;

            // Find the first successful result
            for (const result of results) {
              if (result.status === 'fulfilled' && result.value.success) {
                successfulResult = result.value;
                break;
              } else if (result.status === 'fulfilled') {
                lastError = result.value;
              }
            }

            if (successfulResult) {
              const keyToTry = successfulResult.key;
              const providerResult = successfulResult.result;
              const attemptNumber = successfulResult.attemptNumber;

              // Update round-robin index for next request
              const keyIndex = sortedKeys.findIndex(k => k.id === keyToTry.id);
              currentStrategyParams['_default_rr_idx'] = (keyIndex + 1) % sortedKeys.length;
              customConfig.routing_strategy_params = currentStrategyParams;

              // Background update of round-robin index
              setImmediate(async () => {
                const { error: updateError } = await supabase.from('custom_api_configs').update({ routing_strategy_params: currentStrategyParams }).eq('id', customConfig.id);
                if (updateError) console.error('[RoKey Parallel Default] Failed to update round-robin index:', updateError);
              });

              // Set success state
              apiKeyIdUsed = keyToTry.id;
              predefinedModelIdUsed = keyToTry.predefined_model_id;
              llmProviderState = keyToTry.provider;
              llmRequestTimestamp = providerResult?.llmRequestTimestamp || null;
              llmResponseTimestamp = providerResult?.llmResponseTimestamp || null;
              llmResponseStatus = providerResult?.status ?? null;
              llmResponseHeaders = providerResult?.responseHeaders ?? null;
              roleUsedState = generateRoleUsedMessage.defaultKeySuccess(attemptNumber);
              processingError = null;

              console.log(`[RoKey Parallel Default] Success with key ${apiKeyIdUsed} on parallel attempt #${attemptNumber}. Provider: ${llmProviderState}, Status: ${llmResponseStatus}`);

              if (parsedBody!.stream && providerResult?.response) {
                llmResponseData = providerResult.responseData || { note: "streamed via parallel default routing" };
                return providerResult.response;
              } else if (!parsedBody!.stream && providerResult?.responseData !== undefined) {
                llmResponseData = providerResult.responseData;
                return NextResponse.json(llmResponseData, { status: llmResponseStatus || 200, headers: providerResult.responseHeaders });
              } else {
                console.error(`[RoKey Parallel Default] Internal issue: Key ${keyToTry.id} reported success but no valid response/stream provided.`);
                processingError = { message: `Internal error: Key ${keyToTry.id} success but no response data/stream.`, status: 500 };
                llmResponseData = { error: processingError.message };
                llmResponseStatus = 500;
              }
            } else {
              // All parallel attempts failed
              console.error(`[RoKey Parallel Default] All ${attemptedKeysCount} parallel key attempts failed`);

              if (lastError) {
                llmResponseData = lastError.error;
                llmResponseStatus = lastError.status ?? null;
                processingError = {
                  message: `All ${attemptedKeysCount} key(s) for parallel default routing failed. Last error from key ${lastError.key.id}: ${lastError.error?.message || 'Unknown'}`,
                  status: lastError.status || 500,
                  provider_error: lastError.error
                };
                roleUsedState = generateRoleUsedMessage.allKeysFailed(attemptedKeysCount);
              } else {
                processingError = {
                  message: `All ${attemptedKeysCount} key(s) for parallel default routing failed with unknown errors.`,
                  status: 500
                };
                roleUsedState = `default_all_parallel_attempts_failed_${attemptedKeysCount}`;
              }
            }
          } catch (parallelError: any) {
            console.error('[RoKey Parallel Default] Error in parallel execution:', parallelError.message);
            processingError = { message: `Parallel default routing failed: ${parallelError.message}`, status: 500 };
            roleUsedState = 'default_parallel_execution_error';
          }
          
          // After the loop: if processingError is set, it means all attempts failed or a non-retryable error occurred.
          // If processingError is not set here, it means the loop was exited prematurely without a successful return or setting an error (logic flaw).
          if (!processingError && !apiKeyIdUsed && sortedKeys.length > 0) { // No key was successfully used and loop finished or broke without setting processingError
        processingError = {
                 message: `All ${sortedKeys.length} key(s) for default routing were attempted but failed. Status: ${llmResponseStatus || 'N/A'}. An internal error may have occurred.`,
                 status: llmResponseStatus || 500,
                 provider_error: null
             };
             if (sortedKeys.length > 0) roleUsedState = generateRoleUsedMessage.allKeysFailed(sortedKeys.length);
          }

          if (processingError && !apiKeyIdUsed) { // Ensure roleUsedState reflects overall failure if no key was ever successfully used.
            // This might overwrite a more specific roleUsedState from the loop if a non-retryable break occurred. Better to have a general one.
            roleUsedState = `default_all_attempts_failed_final_err_summary_${(processingError as any)?.message?.substring(0,70)}`;
          }
        }
      }
    } 

    // After all routing attempts (explicit strategies with their fallbacks, or default retry loop):
    if (targetApiKeyData && !processingError) { 
      apiKeyIdUsed = targetApiKeyData.id;
      predefinedModelIdUsed = targetApiKeyData.predefined_model_id;
      llmProviderState = targetApiKeyData.provider; // This is where llmProviderState is set from the resolved key

      if (!llmProviderState) { // Explicitly check if the resolved key has no provider
        console.error(`[RoKey Routing Error] API key ${apiKeyIdUsed} selected by strategy '${routingStrategy || 'default'}' has a NULL provider field.`);
        processingError = { message: `Selected API key '${apiKeyIdUsed}' does not have a provider configured. Please check the API key settings.`, status: 500 };
        // llmProviderState remains null, this will prevent the explicit strategy execution block from running if it relies on llmProviderState.
      } else if (!decryptedApiKey) { // Decrypt if provider is valid but key not yet decrypted
        try {
            decryptedApiKey = decrypt(targetApiKeyData.encrypted_api_key);
        } catch (decryptionError: any) {
            console.error(`API Key decryption failed for selected key ${targetApiKeyData.id}:`, decryptionError);
            processingError = { message: `API Key decryption failed for selected key ${targetApiKeyData.id}.`, status: 500 };
        }
      }
    } else if (!targetApiKeyData && !processingError) {
        processingError = { message: `RoKey could not resolve an API key for this request. Last routing state: ${roleUsedState || 'unknown'}.`, status: 404 };
    }

    // MAIN PROVIDER EXECUTION BLOCK - Should only run if an EXPLICIT strategy successfully selected a key,
    // the key has a valid provider, it was successfully decrypted, AND no overriding processingError has been set.
    // The default retry path returns directly on its own success, so it doesn't hit this block.


    if (targetApiKeyData && decryptedApiKey && !processingError && llmProviderState && isExplicitStrategyFailure) {
      console.log(`[RoKey Proxy - Explicit Strategy Path] Routing to provider: ${llmProviderState}, Model: ${predefinedModelIdUsed} (Key ID: ${apiKeyIdUsed}, Label: ${(targetApiKeyData as ApiKey).label})`);
      
      // Call executeProviderRequest for explicit strategies as well
      const explicitProviderResult = await executeProviderRequest(
        llmProviderState, // This is now guaranteed to be non-null if this block is reached due to the llmProviderState check in the condition
        predefinedModelIdUsed,
        decryptedApiKey, // This is guaranteed to be non-null if this block is reached
        parsedBody!
      );

      llmRequestTimestamp = explicitProviderResult.llmRequestTimestamp;
      llmResponseTimestamp = explicitProviderResult.llmResponseTimestamp;
      llmResponseStatus = explicitProviderResult.status ?? null;
      llmResponseHeaders = explicitProviderResult.responseHeaders ?? null;

      if (explicitProviderResult.success) {
        // Original model succeeded - no need to update logging variables, they're already correct
        // Phase 3: Use fast-path caching helper for successful responses
        llmResponseData = explicitProviderResult.responseData || { note: "streamed via explicit strategy" };
        return handleSuccessfulResponse(
          explicitProviderResult,
          parsedBody!,
          llmProviderState,
          predefinedModelIdUsed || undefined,
          decryptedApiKey,
          {
            roleUsed: roleUsedState,
            routingStrategy: routingStrategy,
            complexityLevel: classifiedComplexityLevel || undefined,
            processingTime: processingDuration
          }
        );
      } else {
        // ENHANCED FALLBACK: Intelligent role routing failed at provider level - initiate automatic fallback
        console.warn(`[RoKey Intelligent Fallback] Explicit strategy (${routingStrategy}) failed for key ${apiKeyIdUsed}. Status: ${explicitProviderResult.status}. Error: ${explicitProviderResult.error?.message || 'Unknown'}`);
        console.log(`[RoKey Intelligent Fallback] Full error object:`, JSON.stringify(explicitProviderResult.error, null, 2));
        console.log(`[RoKey Intelligent Fallback] Initiating automatic fallback to default LLM and other available keys...`);

        // Store the original failure for logging
        const originalFailure = {
          keyId: apiKeyIdUsed,
          provider: llmProviderState,
          status: explicitProviderResult.status,
          error: explicitProviderResult.error,
          strategy: routingStrategy
        };

        // LOG THE ORIGINAL FAILURE FIRST - This helps users understand what went wrong
        try {
          const originalFailureLogEntry = {
            custom_api_config_id: customApiConfigIdFromRequest,
            api_key_id: apiKeyIdUsed,
            user_id: customConfigUserId || userId,
            predefined_model_id: predefinedModelIdUsed,
            role_requested: parsedBody?.role || null,
            role_used: `${roleUsedState}_FAILED`,
            ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || null,
            request_timestamp: requestTimestamp.toISOString(),
            response_timestamp: new Date().toISOString(),
            status_code: explicitProviderResult.status || 500,
            request_payload_summary: parsedBody ? {
              messages_count: parsedBody.messages?.length,
              model_requested_passthrough: parsedBody.model,
              stream: parsedBody.stream,
              temp: parsedBody.temperature,
              max_tok: parsedBody.max_tokens
            } : {note: "Request body was not available."},
            response_payload_summary: {
              error_type: explicitProviderResult.error?.type || explicitProviderResult.error?.error?.type || 'provider_error',
              error_message_summary: explicitProviderResult.error?.message ||
                                   explicitProviderResult.error?.error?.message ||
                                   explicitProviderResult.error?.details?.message ||
                                   'Unknown error',
              provider_status: explicitProviderResult.error?.status || explicitProviderResult.status,
              full_error_details: explicitProviderResult.error,
              fallback_initiated: true
            },
            error_message: `ORIGINAL FAILURE: ${
              explicitProviderResult.error?.message ||
              explicitProviderResult.error?.error?.message ||
              explicitProviderResult.error?.details?.message ||
              JSON.stringify(explicitProviderResult.error) ||
              'Unknown error'
            }. Fallback will be attempted.`,
            error_source: llmProviderState,
            error_details_zod: null,
            llm_provider_name: llmProviderState,
            llm_model_name: predefinedModelIdUsed,
            llm_provider_status_code: explicitProviderResult.status,
            llm_provider_latency_ms: explicitProviderResult.llmResponseTimestamp && explicitProviderResult.llmRequestTimestamp ?
              explicitProviderResult.llmResponseTimestamp.getTime() - explicitProviderResult.llmRequestTimestamp.getTime() : null,
            processing_duration_ms: new Date().getTime() - requestTimestamp.getTime(),
            classified_role_llm: classifiedRoleByLLM,
            classified_complexity_level: classifiedComplexityLevel,
            classified_complexity_llm: classifiedComplexityLLM,
            cost: null, // No cost for failed requests
            input_tokens: null,
            output_tokens: null,
            is_multimodal: parsedBody?.messages ? parsedBody.messages.some(m => Array.isArray(m.content) && m.content.some((p:any) => p.type === 'image_url')) : false,
          };

          const { error: originalLogError } = await supabase.from('request_logs').insert(originalFailureLogEntry as any);
          if (originalLogError) console.error('[RoKey API Original Failure Log Error]', originalLogError.message);
          else console.log(`[RoKey Logging] Original failure logged for ${llmProviderState}/${predefinedModelIdUsed}`);
        } catch (originalLogCatchError: any) {
          console.error('[RoKey API Original Failure Log System Error]', originalLogCatchError.message);
        }

        let fallbackSucceeded = false;

        // STEP 1: Try the default general chat model first
        console.log(`[RoKey Intelligent Fallback] Step 1: Attempting fallback to default general chat model...`);
        const defaultFallbackKey = await getApiKeyForFallbackRouting(supabase, customApiConfigIdFromRequest!, undefined);

        if (defaultFallbackKey && defaultFallbackKey.id !== apiKeyIdUsed) {
          console.log(`[RoKey Intelligent Fallback] Found default fallback key: ${defaultFallbackKey.id} (${defaultFallbackKey.label})`);

          let defaultDecryptedKey: string;
          try {
            defaultDecryptedKey = decrypt(defaultFallbackKey.encrypted_api_key);
          } catch (decryptionError: any) {
            console.warn(`[RoKey Intelligent Fallback] Failed to decrypt default fallback key ${defaultFallbackKey.id}:`, decryptionError.message);
            defaultDecryptedKey = '';
          }

          if (defaultDecryptedKey) {
            const defaultProviderResult = await executeProviderRequest(
              defaultFallbackKey.provider,
              defaultFallbackKey.predefined_model_id,
              defaultDecryptedKey,
              parsedBody!
            );

            if (defaultProviderResult.success) {
              console.log(`[RoKey Intelligent Fallback] SUCCESS: Default fallback key ${defaultFallbackKey.id} worked!`);
              roleUsedState = `${roleUsedState}_FALLBACK_SUCCESS_default`;
              fallbackSucceeded = true;

              // Update response metadata and logging variables to reflect the successful fallback
              llmRequestTimestamp = defaultProviderResult.llmRequestTimestamp;
              llmResponseTimestamp = defaultProviderResult.llmResponseTimestamp;
              llmResponseStatus = defaultProviderResult.status ?? null;
              llmResponseHeaders = defaultProviderResult.responseHeaders ?? null;

              // Update logging variables to show the successful fallback provider/model
              apiKeyIdUsed = defaultFallbackKey.id;
              predefinedModelIdUsed = defaultFallbackKey.predefined_model_id;
              llmProviderState = defaultFallbackKey.provider;

              if (parsedBody!.stream && defaultProviderResult.response) {
                llmResponseData = defaultProviderResult.responseData || { note: "streamed via intelligent fallback to default" };
                return defaultProviderResult.response;
              } else if (!parsedBody!.stream && defaultProviderResult.responseData !== undefined) {
                llmResponseData = defaultProviderResult.responseData;
                return NextResponse.json(llmResponseData, { status: llmResponseStatus || 200, headers: defaultProviderResult.responseHeaders });
              }
            } else {
              console.warn(`[RoKey Intelligent Fallback] Default fallback key ${defaultFallbackKey.id} also failed. Status: ${defaultProviderResult.status}. Error: ${defaultProviderResult.error?.message || 'Unknown'}`);
            }
          }
        } else {
          console.log(`[RoKey Intelligent Fallback] No suitable default fallback key found or it's the same as the failed key`);
        }

        // STEP 2: Phase 6 Optimization - Parallel fallback attempts for maximum speed
        if (!fallbackSucceeded) {
          console.log(`[RoKey Intelligent Fallback] Step 2: Attempting parallel fallback to all available active keys...`);
          const { data: allConfigKeys, error: allKeysError } = await supabase
            .from('api_keys')
            .select('*')
            .eq('custom_api_config_id', customApiConfigIdFromRequest)
            .eq('status', 'active');

          if (allKeysError) {
            console.error('[RoKey Intelligent Fallback] Error fetching all config keys:', allKeysError);
          } else if (allConfigKeys && allConfigKeys.length > 0) {
            // Filter out keys we've already tried
            const triedKeyIds = new Set([apiKeyIdUsed]);
            if (defaultFallbackKey) triedKeyIds.add(defaultFallbackKey.id);

            const untriedKeys = allConfigKeys.filter(key => !triedKeyIds.has(key.id));
            console.log(`[RoKey Intelligent Fallback] Found ${untriedKeys.length} untried keys - attempting all in parallel`);

            if (untriedKeys.length > 0) {
              // Phase 6: Parallel fallback attempts with first-success pattern
              const fallbackAttempts = untriedKeys.map(async (fallbackKey) => {
                try {
                  console.log(`[RoKey Parallel Fallback] Starting attempt with key: ${fallbackKey.id} (${fallbackKey.label})`);

                  const fallbackDecryptedKey = decrypt(fallbackKey.encrypted_api_key);
                  const fallbackProviderResult = await executeProviderRequest(
                    fallbackKey.provider,
                    fallbackKey.predefined_model_id,
                    fallbackDecryptedKey,
                    parsedBody!
                  );

                  if (fallbackProviderResult.success) {
                    console.log(`[RoKey Parallel Fallback] SUCCESS: Key ${fallbackKey.id} worked in parallel attempt!`);
                    return {
                      success: true,
                      key: fallbackKey,
                      result: fallbackProviderResult
                    };
                  } else {
                    console.warn(`[RoKey Parallel Fallback] Key ${fallbackKey.id} failed. Status: ${fallbackProviderResult.status}`);
                    return {
                      success: false,
                      key: fallbackKey,
                      error: fallbackProviderResult.error
                    };
                  }
                } catch (error: any) {
                  console.warn(`[RoKey Parallel Fallback] Key ${fallbackKey.id} failed with exception:`, error.message);
                  return {
                    success: false,
                    key: fallbackKey,
                    error: error
                  };
                }
              });

              // Wait for first success or all failures
              try {
                const results = await Promise.allSettled(fallbackAttempts);

                // Find the first successful result
                for (const result of results) {
                  if (result.status === 'fulfilled' && result.value.success) {
                    const successfulAttempt = result.value;
                    const fallbackKey = successfulAttempt.key;
                    const fallbackProviderResult = successfulAttempt.result;

                    console.log(`[RoKey Parallel Fallback] Using successful result from key: ${fallbackKey.id}`);
                    roleUsedState = `${roleUsedState}_PARALLEL_FALLBACK_SUCCESS_${fallbackKey.id}`;
                    fallbackSucceeded = true;

                    // Update response metadata and logging variables
                    llmRequestTimestamp = fallbackProviderResult?.llmRequestTimestamp || null;
                    llmResponseTimestamp = fallbackProviderResult?.llmResponseTimestamp || null;
                    llmResponseStatus = fallbackProviderResult?.status ?? null;
                    llmResponseHeaders = fallbackProviderResult?.responseHeaders ?? null;

                    // Update logging variables to show the successful fallback provider/model
                    apiKeyIdUsed = fallbackKey.id;
                    predefinedModelIdUsed = fallbackKey.predefined_model_id;
                    llmProviderState = fallbackKey.provider;

                    if (parsedBody!.stream && fallbackProviderResult?.response) {
                      llmResponseData = fallbackProviderResult.responseData || { note: `streamed via parallel fallback to ${fallbackKey.id}` };
                      return fallbackProviderResult.response;
                    } else if (!parsedBody!.stream && fallbackProviderResult?.responseData !== undefined) {
                      llmResponseData = fallbackProviderResult.responseData;
                      return NextResponse.json(llmResponseData, { status: llmResponseStatus || 200, headers: fallbackProviderResult.responseHeaders });
                    }
                    break; // Exit since we found a working key
                  }
                }

                if (!fallbackSucceeded) {
                  console.warn(`[RoKey Parallel Fallback] All ${untriedKeys.length} parallel fallback attempts failed`);
                }
              } catch (parallelError: any) {
                console.error('[RoKey Parallel Fallback] Error in parallel fallback execution:', parallelError.message);
              }
            }
          }
        }

        // STEP 3: All fallbacks failed - set final error
        if (!fallbackSucceeded) {
          console.error(`[RoKey Intelligent Fallback] All fallback attempts failed. Original failure: ${originalFailure.strategy} with key ${originalFailure.keyId}`);
          roleUsedState = `${roleUsedState}_all_fallbacks_failed`;
          processingError = {
            message: `Intelligent routing (${originalFailure.strategy}) failed for key ${originalFailure.keyId} (${originalFailure.provider}). All fallback attempts also failed. Original error: ${originalFailure.error?.message || 'Unknown'}`,
            status: originalFailure.status || 500,
            provider_error: originalFailure.error,
            fallback_attempted: true
          };
          llmResponseData = originalFailure.error;
          llmResponseStatus = originalFailure.status ?? null;
        }
      }
    } else if (targetApiKeyData && !decryptedApiKey && !processingError && llmProviderState && isExplicitStrategyFailure) {
      // This case would mean decryption failed, processingError should have been set already.
      // Redundant if decryption error handling is correct, but acts as a safeguard.
      if (!processingError) { // Should be set by the decryption catch block
          processingError = { message: `API key ${apiKeyIdUsed} selected but decryption failed (safeguard).`, status: 500 };
      }
    } else if (targetApiKeyData && decryptedApiKey && !processingError && !llmProviderState && isExplicitStrategyFailure) {
      // This case means the key was found, decrypted, but its provider field was null/empty.
      // processingError should have been set by the `if (!llmProviderState)` check earlier.
      if (!processingError) { // Should be set by the check for llmProviderState
        processingError = { message: `API key ${apiKeyIdUsed} selected but has no provider configured (safeguard).`, status: 500 };
      }
    }

    // FINAL ERROR HANDLING AND RESPONSE GENERATION (if not already returned)
    if (processingError) {
      console.error('[RoKey API Main Error Handler Triggered]', processingError.status, processingError.message, processingError.provider_error);
      const responseStatus = processingError.status || 500;
      const responseMessage = processingError.message || 'An unexpected internal server error occurred.';
      const responseIssues = processingError.issues;
      const responseProviderError = processingError.provider_error;
      
      if (!llmResponseStatus && processingError.status && processingError.provider_error) {
          llmResponseStatus = processingError.status;
      }
      // Ensure llmResponseData has some error info if not already set by a failed provider call
      if (!llmResponseData && responseMessage) {
          llmResponseData = { error: { message: responseMessage, ...(responseProviderError && {details: responseProviderError})} };
      }

      return NextResponse.json({ error: responseMessage, ...(responseIssues && { issues: responseIssues }), ...(responseProviderError && { provider_error_details: responseProviderError }) }, { status: responseStatus });
    }
    
    // This point should ideally not be reached if processingError is not set, 
    // as successful paths (default retry or explicit strategy) should have already returned a response.
    // However, as a safeguard:
    if (!processingError && !apiKeyIdUsed) { // Should have been caught by "No API key could be resolved"
        console.error("[RoKey Critical Fallthrough] No API Key was used and no processing error. This indicates a logic flaw.");
        processingError = { message: "Critical internal error: No API key processed and no explicit error state.", status: 500 };
        return NextResponse.json({ error: processingError.message }, { status: processingError.status });
    }
    
    // If an explicit strategy path somehow completed without error AND without returning a response (e.g. forgot a return in the switch)
    // This is a safeguard, ideally all successful code paths (explicit or default-retry)
    // should have already returned a NextResponse or Response.
    // If llmResponseData is populated (e.g. by a successful non-stream explicit strategy call that didn't return early)
    if (llmResponseData && !parsedBody?.stream) {
        return NextResponse.json(llmResponseData, { status: llmResponseStatus || 200 });
    }
    
    // Final catch-all if something went very wrong and no response was formed.
    console.error("[RoKey Critical Logic Error] Reached end of POST handler without returning a response and no clear error state.");
    return NextResponse.json({ error: "An unexpected critical server error occurred." }, { status: 500 });

  } finally {
    const responseTimestamp: Date = new Date();
    const totalProcessingDurationMs = responseTimestamp.getTime() - requestTimestamp.getTime();
    let llmProviderLatencyMs: number | null = null;
    if (llmRequestTimestamp && llmResponseTimestamp) llmProviderLatencyMs = llmResponseTimestamp.getTime() - llmRequestTimestamp.getTime();
    const rokeyProcessingDurationMs = totalProcessingDurationMs - (llmProviderLatencyMs || 0);

    // Phase 1 Optimization: Performance summary
    const requestEndTime = performance.now();
    const totalRequestTime = requestEndTime - requestStartTime;
    console.log(`📊 [BACKEND] Request completed in ${totalRequestTime.toFixed(1)}ms (LLM: ${llmProviderLatencyMs || 0}ms, RoKey: ${rokeyProcessingDurationMs}ms)`);

    if (parsedBody?.messages) isMultimodal = parsedBody.messages.some(m => Array.isArray(m.content) && m.content.some((p:any) => p.type === 'image_url'));
    
    // Enhanced token extraction
    if (llmResponseData?.usage) {
      tokensPrompt = llmResponseData.usage.prompt_tokens || llmResponseData.usage.input_tokens || null;
      tokensCompletion = llmResponseData.usage.completion_tokens || llmResponseData.usage.output_tokens || null;
    } else if (llmProviderState?.toLowerCase() === 'google' && llmResponseData?.promptFeedback?.tokenCount !== undefined) {
        // For Google non-streaming legacy format, promptFeedback has prompt tokens. Completion tokens might be in candidates.
        tokensPrompt = llmResponseData.promptFeedback.tokenCount;
        tokensCompletion = llmResponseData.candidates?.[0]?.tokenCount || null;
    }

    // Phase 6 Optimization: Move cost calculation to background for faster response times
    // First check if user is on free tier (no cost calculation needed)
    const isFreeTier = isProviderFreeTier(llmProviderState, llmResponseHeaders || undefined);

    if (isFreeTier) {
      cost = 0;
      console.log(`[Cost Calculation] Free tier detected for ${llmProviderState} - no cost calculated`);
    } else if (llmResponseData?.usage?.cost && llmProviderState?.toLowerCase() === 'openrouter') {
      // OpenRouter provides cost in credits, convert to USD (1 credit = $0.000001)
      cost = llmResponseData.usage.cost * 0.000001;
      console.log(`[Cost Calculation] OpenRouter direct cost: ${llmResponseData.usage.cost} credits = $${cost.toFixed(8)}`);
    } else if (tokensPrompt !== null && tokensCompletion !== null && predefinedModelIdUsed) {
      // Phase 6: Background cost calculation to avoid blocking response
      setImmediate(async () => {
        try {
          const { data: modelPricing, error: pricingError } = await supabase
            .from('models')
            .select('input_token_price, output_token_price')
            .eq('id', predefinedModelIdUsed)
            .single();

          if (!pricingError && modelPricing?.input_token_price && modelPricing?.output_token_price && tokensPrompt !== null && tokensCompletion !== null) {
            const inputCost = tokensPrompt * modelPricing.input_token_price;
            const outputCost = tokensCompletion * modelPricing.output_token_price;
            const calculatedCost = inputCost + outputCost;
            console.log(`[Background Cost Calculation] Model: ${predefinedModelIdUsed}, Input: ${tokensPrompt} tokens × $${modelPricing.input_token_price} = $${inputCost.toFixed(8)}, Output: ${tokensCompletion} tokens × $${modelPricing.output_token_price} = $${outputCost.toFixed(8)}, Total: $${calculatedCost.toFixed(8)}`);

            // Update the log entry with calculated cost
            if (configIdForLog) {
              await supabase
                .from('request_logs')
                .update({ cost: calculatedCost })
                .eq('custom_api_config_id', configIdForLog)
                .eq('request_timestamp', requestTimestamp.toISOString());
            }
          } else {
            console.log(`[Background Cost Calculation] No pricing data available for model: ${predefinedModelIdUsed}`);
          }
        } catch (costError: any) {
          console.error('[Background Cost Calculation Error]', costError.message);
        }
      });

      // Set cost to null for immediate response, will be updated in background
      cost = null;
      console.log(`[Cost Calculation] Moved to background for faster response - will update log entry when complete`);
    }

    const configIdForLog = customApiConfigIdFromRequest || parsedBody?.custom_api_config_id;

    // Phase 1 Optimization: Async database logging to prevent blocking response
    if (configIdForLog) {
      // Fire-and-forget async logging
      setImmediate(async () => {
        try {
          const finalUserId = customConfigUserId || userId;
          console.log(`🔍 [LOGGING DEBUG] Creating log entry with user_id: ${finalUserId}`);
          console.log(`🔍 [LOGGING DEBUG] customConfigUserId: ${customConfigUserId}, userId: ${userId}`);

          const logEntry = {
            custom_api_config_id: configIdForLog,
            api_key_id: apiKeyIdUsed, // Renamed from api_key_id_used for consistency
            user_id: finalUserId, // Prefer user_id from config if available
            predefined_model_id: predefinedModelIdUsed,
            role_requested: parsedBody?.role || null,
            role_used: roleUsedState,
            ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || null,
            request_timestamp: requestTimestamp.toISOString(),
            response_timestamp: responseTimestamp.toISOString(),
            status_code: processingError ? (processingError.status || 500) : (llmResponseStatus || 200),
            request_payload_summary: parsedBody ? { messages_count: parsedBody.messages?.length, model_requested_passthrough: parsedBody.model, stream: parsedBody.stream, temp: parsedBody.temperature, max_tok: parsedBody.max_tokens } : {note: "Request body was not available or Zod validation failed."},
            response_payload_summary: {
              usage: llmResponseData?.usage,
              finish_reason: llmResponseData?.choices?.[0]?.finish_reason,
              error_type: llmResponseData?.error?.type,
              error_message_summary: llmResponseData?.error?.message,
              full_error_details: llmResponseData?.error,
              // Add fallback information if this was a fallback success
              is_fallback_success: roleUsedState?.includes('FALLBACK_SUCCESS') || false,
              original_failure_summary: roleUsedState?.includes('FAILED') ? 'See previous log entry for original failure details' : null
            },
            error_message: (() => {
              // Create a clear error summary for users
              if (roleUsedState?.includes('FALLBACK_SUCCESS')) {
                return `FALLBACK SUCCESS: Original model failed, successfully used fallback model. Check previous log entry for failure details.`;
              } else if (processingError?.message) {
                return processingError.message;
              } else if (llmResponseData?.error?.message) {
                return llmResponseData.error.message;
              } else {
                return null;
              }
            })(),
            error_source: processingError ? (processingError.provider_error && llmProviderState ? llmProviderState : 'RoKey') : (llmResponseData?.error ? llmProviderState : null),
            error_details_zod: processingError?.issues ? JSON.stringify(processingError.issues) : null,
            llm_provider_name: llmProviderState,
            llm_model_name: predefinedModelIdUsed,
            llm_provider_status_code: llmResponseStatus,
            llm_provider_latency_ms: llmProviderLatencyMs,
            processing_duration_ms: rokeyProcessingDurationMs,
            // Fields for new strategies and enhanced logging
            classified_role_llm: classifiedRoleByLLM,
            classified_complexity_level: classifiedComplexityLevel,
            classified_complexity_llm: classifiedComplexityLLM,
            // Cost and tokenomics (ensure no duplication)
            cost: cost,
            input_tokens: tokensPrompt,
            output_tokens: tokensCompletion,
            is_multimodal: isMultimodal,
          };
          // Use service role client for logging to bypass RLS
          const serviceSupabase = createClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.SUPABASE_SERVICE_ROLE_KEY!,
            {
              auth: {
                autoRefreshToken: false,
                persistSession: false
              }
            }
          );

          const { error: logError } = await serviceSupabase.from('request_logs').insert(logEntry as any);
          if (logError) console.error('[RoKey API Log Error]', logError.message, logError.details);
        } catch (logCatchError: any) {
          console.error('[RoKey API Log System Error]', logCatchError.message, logCatchError.stack);
        }
      });
    } else if (processingError) { // Log critical early errors even if configId is missing
        // Phase 1 Optimization: Async early error logging
        setImmediate(async () => {
          try {
              const earlyErrorLogEntry = {
                  custom_api_config_id: null, api_key_id: null, user_id: userId, predefined_model_id: null,
                  role_requested: parsedBody?.role || null, role_used: null, ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || null,
                  request_timestamp: requestTimestamp.toISOString(), response_timestamp: responseTimestamp.toISOString(),
                status_code: processingError.status || 500,
                  request_payload_summary: { note: "Early error, request body may be malformed.", custom_api_config_id_attempted: customApiConfigIdFromRequest },
                  response_payload_summary: { error_message_summary: processingError.message?.substring(0,100) },
                  error_message: processingError.message, error_source: 'RoKey',
                error_details_zod: processingError.issues ? JSON.stringify(processingError.issues) : null,
                  llm_provider_name: null, llm_model_name: null, llm_provider_status_code: null, llm_provider_latency_ms: null,
                  processing_duration_ms: totalProcessingDurationMs,
                  // Nullify fields not applicable to early errors
                  classified_role_llm: null, classified_complexity_level: null, classified_complexity_llm: null,
                  cost: null, input_tokens: null, output_tokens: null, is_multimodal: false,
              };
              // Use service role client for logging to bypass RLS
              const serviceSupabase = createClient(
                process.env.NEXT_PUBLIC_SUPABASE_URL!,
                process.env.SUPABASE_SERVICE_ROLE_KEY!,
                {
                  auth: {
                    autoRefreshToken: false,
                    persistSession: false
                  }
                }
              );

              const { error: logError } = await serviceSupabase.from('request_logs').insert(earlyErrorLogEntry as any);
              if (logError) console.error('[RoKey API Early Error Log Error]', logError.message);
          } catch (logCatchError: any) { console.error('[RoKey API Early Error Log System Error]', logCatchError.message); }
        });
    }
  }
}

// OPTIONS handler for CORS preflight requests
export async function OPTIONS(request: NextRequest) {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*', // Be more specific in production environments
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// Phase 1: Multi-Role Orchestration Handler
async function handleMultiRoleOrchestration(
  request: NextRequest, // The original request object from the main handler
  parsedBody: any,
  classificationResult: { isMultiRole: boolean; roles: Array<{ roleId: string; confidence: number; executionOrder: number }>; reasoning: string },
  customApiConfigId: string,
  availableApiKeys: any[],
  roleAssignments: any[], // Add role assignments for smart key mapping
  originalPrompt: string,
  orchestrationExecutionId: string,
  supabase: any
): Promise<Response> {
  console.log(`[Multi-Role Orchestration] Starting orchestration for execution ID: ${orchestrationExecutionId}`);
  
  // 1. Decompose the task into sequential steps
  const decomposedTasks = await decomposeTaskIntoSteps(
    originalPrompt,
    classificationResult.roles,
    parsedBody.messages || []
  );
  
  console.log(`[Multi-Role Orchestration] Task decomposed into ${decomposedTasks.length} steps`);
  
  // 2. Create orchestration steps in the database - ALL AT ONCE to avoid race conditions
  console.log(`[Multi-Role Orchestration] Creating ${decomposedTasks.length} steps in database...`);

  const stepInserts = [];
  for (let i = 0; i < decomposedTasks.length; i++) {
    const task = decomposedTasks[i];
    const role = classificationResult.roles.find(r => r.roleId === task.roleId);

    // Find API key for this role with smart fallback
    const keyAssignmentResult = await findApiKeyForRole(
      customApiConfigId,
      task.roleId,
      availableApiKeys,
      roleAssignments,
      supabase
    );

    const apiKeyForRole = keyAssignmentResult.apiKey;
    const assignmentType = keyAssignmentResult.assignmentType;

    if (!apiKeyForRole) {
      console.warn(`[Multi-Role Orchestration] No API key found for role ${task.roleId}, step ${i+1} - assignment type: ${assignmentType}`);
    } else {
      console.log(`[Multi-Role Orchestration] Using ${assignmentType} key ${apiKeyForRole.id} for role ${task.roleId}, step ${i+1}`);
    }

    // Prepare step record for batch insert
    stepInserts.push({
      execution_id: orchestrationExecutionId,
      step_number: i + 1,
      role_id: task.roleId,
      api_key_id: apiKeyForRole?.id || null,
      model_name: apiKeyForRole?.predefined_model_id || 'unknown',
      prompt: task.prompt,
      status: i === 0 ? 'pending' : 'waiting',
      assignment_type: assignmentType, // Track how the key was assigned
      confidence: role?.confidence || 0.8 // Store role confidence
    });
  }

  // Insert all steps at once to ensure they're all available when monitoring starts
  try {
    const { error } = await supabase
      .from('orchestration_steps')
      .insert(stepInserts);

    if (error) {
      console.error(`[Multi-Role Orchestration] Error creating step records: ${error.message}`);
      throw new Error(`Failed to create orchestration steps: ${error.message}`);
    }

    console.log(`[Multi-Role Orchestration] Successfully created ${stepInserts.length} steps in database`);
  } catch (error) {
    console.error(`[Multi-Role Orchestration] Error creating steps: ${error}`);
    throw error;
  }
  
  // 3. Update execution status to in_progress
  try {
    await supabase
      .from('orchestration_executions')
      .update({ status: 'in_progress' })
      .eq('id', orchestrationExecutionId);
  } catch (error) {
    console.error(`[Multi-Role Orchestration] Error updating execution status: ${error}`);
  }
  
  // Get the origin from the request URL
  const origin = request.nextUrl.origin;
  const streamUrl = `${origin}/api/orchestration/stream/${orchestrationExecutionId}`;
  const statusUrl = `${origin}/api/orchestration/status/${orchestrationExecutionId}`;
  
  // Start the orchestration in the background after a short delay to allow client to connect
  setTimeout(async () => {
    try {
      console.log(`[Multi-Role Orchestration] Triggering orchestration start for ${orchestrationExecutionId}`);

      const startResponse = await fetch(`${origin}/api/orchestration/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          executionId: orchestrationExecutionId,
        }),
      });

      if (!startResponse.ok) {
        console.error(`[Multi-Role Orchestration] Failed to start orchestration: ${startResponse.status}`);
        const errorData = await startResponse.text();
        console.error(`[Multi-Role Orchestration] Error details: ${errorData}`);
      } else {
        console.log(`[Multi-Role Orchestration] Successfully started orchestration for ${orchestrationExecutionId}`);
      }
    } catch (error) {
      console.error(`[Multi-Role Orchestration] Error starting orchestration: ${error}`);
    }
  }, 1000); // Give client 1 second to connect to the stream

  // 5. Create and send SSE response that will keep the connection open
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    async start(controller) {
      // Send initial event with orchestration details
      const initialEvent = {
        id: crypto.randomUUID(),
        type: 'orchestration_started',
        data: {
          execution_id: orchestrationExecutionId,
          stream_url: streamUrl,
          status_url: statusUrl,
          total_steps: decomposedTasks.length,
          current_step: 1,
          status: 'in_progress',
          message: 'Orchestration started. Connecting to stream...'
        }
      };
      
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialEvent)}\n\n`));
      
      // Keep the connection open for a short time to ensure client receives the initial event
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Close the stream to allow the client to connect to the main orchestration stream
      controller.close();
    },
    cancel() {
      console.log(`[Multi-Role Orchestration] Client disconnected from initial stream for ${orchestrationExecutionId}`);
    }
  });
  
  // Return the SSE response
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'X-Accel-Buffering': 'no'
    }
  });
}

// Enhanced function to decompose a task into steps for multi-role orchestration
async function decomposeTaskIntoSteps(
  originalPrompt: string,
  roles: Array<{ roleId: string; confidence: number; executionOrder: number }>,
  messages: any[] = []
): Promise<Array<{ roleId: string; prompt: string; outputFormat?: string; moderatorInstructions?: string }>> {
  // Sort roles by execution order
  const sortedRoles = [...roles].sort((a, b) => a.executionOrder - b.executionOrder);
  
  // Create a more sophisticated sequential decomposition with better handoffs
  const tasks: Array<{ roleId: string; prompt: string; outputFormat?: string; moderatorInstructions?: string }> = [];
  
  if (sortedRoles.length === 1) {
    // If there's only one role, use the original prompt
    tasks.push({
      roleId: sortedRoles[0].roleId,
      prompt: originalPrompt,
      moderatorInstructions: `This is a single-role task for ${sortedRoles[0].roleId}. Ensure the response fully addresses the user's request.`
    });
  } else if (sortedRoles.length > 1) {
    // For multiple roles, create a sequence of tasks with clear handoffs
    
    // First role gets the original prompt with instructions to focus on its part
    const firstRole = sortedRoles[0];
    const firstRolePrompt = createRoleSpecificPrompt(originalPrompt, firstRole.roleId, true);
    tasks.push({
      roleId: firstRole.roleId,
      prompt: `${firstRolePrompt}\n\nIMPORTANT: Your output will be passed to a ${sortedRoles[1].roleId} specialist next, so focus on providing information that will be useful for that role.`,
      outputFormat: "Detailed output for the next step",
      moderatorInstructions: `This is the first step in a multi-role task. The ${firstRole.roleId} specialist should provide output that will be useful for the ${sortedRoles[1].roleId} specialist in the next step.`
    });
    
    // Middle roles (if any)
    for (let i = 1; i < sortedRoles.length - 1; i++) {
      const role = sortedRoles[i];
      const nextRole = sortedRoles[i + 1];
      tasks.push({
        roleId: role.roleId,
        prompt: `You are working as part of an AI team to address this request: "${originalPrompt}"\n\nA ${sortedRoles[i-1].roleId} specialist has already worked on this and produced the following output:\n\n{{previousOutput}}\n\nYour role is to handle the ${role.roleId} aspect of this request. Your output will be passed to a ${nextRole.roleId} specialist next, so focus on providing information that will be useful for that role.`,
        outputFormat: "Detailed output for the next step",
        moderatorInstructions: `This is step ${i+1} in a multi-role task. The ${role.roleId} specialist should build on the previous output and prepare content for the ${nextRole.roleId} specialist.`
      });
    }
    
    // Last role gets instructions to finalize
    const lastRole = sortedRoles[sortedRoles.length - 1];
    tasks.push({
      roleId: lastRole.roleId,
      prompt: `You are the final specialist in an AI team addressing this request: "${originalPrompt}"\n\nPrevious specialists have already worked on this and produced the following output:\n\n{{previousOutput}}\n\nAs the ${lastRole.roleId} specialist, your role is to finalize the solution and provide a complete, polished response to the user's original request.`,
      outputFormat: "Final complete solution",
      moderatorInstructions: `This is the final step in a multi-role task. The ${lastRole.roleId} specialist should provide a complete solution that incorporates all previous work.`
    });
  }
  
  return tasks;
}

// Helper function to create role-specific prompts
function createRoleSpecificPrompt(originalPrompt: string, roleId: string, isFirstStep: boolean): string {
  // Role-specific prompt templates
  const rolePrompts: { [key: string]: string } = {
    'brainstorming': `Focus on brainstorming creative ideas for this request: "${originalPrompt}"\n\nProvide detailed concepts, possibilities, and innovative approaches. Don't implement or code anything yet, just focus on generating ideas.`,
    
    'coding_frontend': `${isFirstStep 
      ? `Develop frontend code for this request: "${originalPrompt}"\n\nProvide complete, well-commented code that addresses the requirements.` 
      : `Based on the previous work, implement the frontend code needed. Make sure your code is complete and well-commented.`}`,
    
    'coding_backend': `${isFirstStep 
      ? `Develop backend code for this request: "${originalPrompt}"\n\nProvide complete, well-commented code that addresses the requirements.` 
      : `Based on the previous work, implement the backend code needed. Make sure your code is complete and well-commented.`}`,
    
    'content_writing': `${isFirstStep 
      ? `Write content for this request: "${originalPrompt}"\n\nCreate high-quality, engaging content that addresses the requirements.` 
      : `Based on the previous work, create polished content that effectively communicates the ideas.`}`,
    
    'research': `${isFirstStep 
      ? `Research information for this request: "${originalPrompt}"\n\nProvide comprehensive, accurate information with proper citations where possible.` 
      : `Based on the previous work, conduct thorough research to expand and validate the information.`}`,
    
    'logic_reasoning': `${isFirstStep 
      ? `Apply logical reasoning to this request: "${originalPrompt}"\n\nAnalyze the problem systematically and provide a structured solution approach.` 
      : `Based on the previous work, apply logical reasoning to refine and improve the solution.`}`,
    
    // Default prompt for any other role
    'default': `${isFirstStep 
      ? `Handle the ${roleId} aspect of this request: "${originalPrompt}"\n\nFocus specifically on your role's expertise.` 
      : `Based on the previous work, address the ${roleId} aspect of this request.`}`
  };
  
  return rolePrompts[roleId] || rolePrompts['default'];
}

// Helper function to get a human-readable description for each role
function getTaskDescription(roleId: string): string {
  const descriptions: { [key: string]: string } = {
    'brainstorming_ideation': 'Creative idea generation and concept development',
    'coding_frontend': 'Frontend development and UI implementation',
    'coding_backend': 'Backend development and algorithm implementation',
    'research_synthesis': 'Research, analysis, and information gathering',
    'writing': 'Content creation and copywriting',
    'logic_reasoning': 'Problem-solving and logical analysis',
    'summarization_briefing': 'Summarizing and condensing information',
    'translation_localization': 'Translation and cultural adaptation',
    'data_extraction_structuring': 'Data processing and organization',
    'education_tutoring': 'Educational content and learning materials',
    'general_chat': 'General conversation and assistance',
    'StoryTeller': 'Narrative creation and storytelling',
    'Joke_Teller': 'Humor and entertainment content'
  };
  
  return descriptions[roleId] || 'Specialized task processing';
}

// Helper function to generate orchestration step descriptions
function getOrchestrationStep(roleId: string, nextRoleId: string | null, isFinal: boolean): string {
  if (isFinal) {
    return `The **${roleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}** specialist will finalize the solution based on previous work.`;
  } else if (nextRoleId) {
    return `The **${roleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}** specialist will work first, then hand off to **${nextRoleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}**.`;
  } else {
    return `The **${roleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}** specialist will process this part of the request.`;
  }
}

// Enhanced helper function to find an API key for a specific role with smart fallback
async function findApiKeyForRole(
  customApiConfigId: string,
  roleId: string,
  availableApiKeys: any[],
  roleAssignments: any[],
  supabase: any
): Promise<{ apiKey: any | null; assignmentType: 'assigned' | 'general_chat_fallback' | 'first_available_fallback' | 'none' }> {
  try {
    console.log(`[Smart Key Assignment] Finding key for role: ${roleId}`);

    // Strategy 1: Check if there's a specific role assignment for this role
    const roleAssignment = roleAssignments.find(ra => ra.role_name === roleId);
    if (roleAssignment && roleAssignment.api_key_id) {
      const assignedApiKey = availableApiKeys.find(key => key.id === roleAssignment.api_key_id && key.status === 'active');
      if (assignedApiKey) {
        console.log(`[Smart Key Assignment] ✅ Found assigned key ${assignedApiKey.id} for role ${roleId}`);
        return { apiKey: assignedApiKey, assignmentType: 'assigned' };
      } else {
        console.log(`[Smart Key Assignment] ⚠️ Assigned key for role ${roleId} not found or inactive`);
      }
    }

    // Strategy 2: For unassigned roles, try to use the general chat default model
    const generalChatKey = availableApiKeys.find(key => key.is_default_general_chat_model === true && key.status === 'active');
    if (generalChatKey) {
      console.log(`[Smart Key Assignment] 🔄 Using general chat fallback key ${generalChatKey.id} for unassigned role ${roleId}`);
      return { apiKey: generalChatKey, assignmentType: 'general_chat_fallback' };
    }

    // Strategy 3: If no general chat default, use the first available active key
    const firstActiveKey = availableApiKeys.find(key => key.status === 'active');
    if (firstActiveKey) {
      console.log(`[Smart Key Assignment] 🔄 Using first available key ${firstActiveKey.id} for role ${roleId}`);
      return { apiKey: firstActiveKey, assignmentType: 'first_available_fallback' };
    }

    // Strategy 4: No keys available
    console.warn(`[Smart Key Assignment] ❌ No suitable key found for role ${roleId}`);
    return { apiKey: null, assignmentType: 'none' };

  } catch (error) {
    console.error(`[Smart Key Assignment] Error finding API key for role ${roleId}: ${error}`);
    return { apiKey: null, assignmentType: 'none' };
  }
}

// Enhanced function to create orchestration plan with key assignment details
async function createEnhancedOrchestrationPlan(
  decomposedTasks: Array<{ roleId: string; prompt: string; outputFormat?: string; moderatorInstructions?: string }>,
  classificationResult: { isMultiRole: boolean; roles: Array<{ roleId: string; confidence: number; executionOrder: number }>; reasoning: string },
  availableApiKeys: any[],
  roleAssignments: any[]
): Promise<string> {

  // Get key assignment information for each role
  const roleKeyInfo = await Promise.all(
    decomposedTasks.map(async (task) => {
      const keyResult = await findApiKeyForRole(
        '', // customApiConfigId not needed for this check
        task.roleId,
        availableApiKeys,
        roleAssignments,
        null // supabase not needed for this check
      );
      return {
        roleId: task.roleId,
        assignmentType: keyResult.assignmentType,
        hasKey: keyResult.apiKey !== null,
        keyLabel: keyResult.apiKey?.label || 'Unknown'
      };
    })
  );

  // Create enhanced plan with key assignment status
  const rolesList = decomposedTasks.map((task, index) => {
    const keyInfo = roleKeyInfo[index];
    const roleDisplayName = task.roleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    const keyStatusIcon = getKeyStatusIcon(keyInfo.assignmentType);
    const keyStatusText = getKeyStatusText(keyInfo.assignmentType, keyInfo.keyLabel);

    return `${index + 1}. **${roleDisplayName}** ${keyStatusIcon} - ${getTaskDescription(task.roleId)}
   ${keyStatusText}`;
  }).join('\n\n');

  const orchestrationSteps = decomposedTasks.map((task, index) => {
    const nextRole = index < decomposedTasks.length - 1 ? decomposedTasks[index + 1].roleId : null;
    return `${index + 1}. ${getOrchestrationStep(task.roleId, nextRole, index === decomposedTasks.length - 1)}`;
  }).join('\n');

  return `🎬 **AI Team Orchestration Started!**

Your request is being processed by ${decomposedTasks.length} specialized AI models working together:

${rolesList}

**Orchestration Plan:**
${orchestrationSteps}

**Reasoning:** ${classificationResult.reasoning}

🤖 **Moderator Status:** I'm now starting the orchestration process! Each specialist will work in sequence, and I'll coordinate between them, validate outputs, and provide real-time updates.

⚡ **EXECUTION STARTING:** The first specialist is being activated now. You can monitor progress at the stream URL provided below.

*This is a live orchestration session. The specialists are actually working together to solve your request!*`;
}

// Helper function to get key status icon
function getKeyStatusIcon(assignmentType: string): string {
  switch (assignmentType) {
    case 'assigned': return '✅';
    case 'general_chat_fallback': return '🔄';
    case 'first_available_fallback': return '⚡';
    case 'none': return '❌';
    default: return '❓';
  }
}

// Helper function to get key status text
function getKeyStatusText(assignmentType: string, keyLabel: string): string {
  switch (assignmentType) {
    case 'assigned':
      return `*Using dedicated model: ${keyLabel}*`;
    case 'general_chat_fallback':
      return `*Using general chat model: ${keyLabel} (no dedicated model assigned)*`;
    case 'first_available_fallback':
      return `*Using available model: ${keyLabel} (fallback)*`;
    case 'none':
      return `*⚠️ No suitable model available - this role may be skipped*`;
    default:
      return `*Status unknown*`;
  }
}

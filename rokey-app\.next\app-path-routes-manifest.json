{"/_not-found/page": "/_not-found", "/api/activity/route": "/api/activity", "/api/analytics/summary/route": "/api/analytics/summary", "/api/chat/conversations/route": "/api/chat/conversations", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/chat/messages/route": "/api/chat/messages", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/custom-configs/route": "/api/custom-configs", "/api/debug/checkout/route": "/api/debug/checkout", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/documents/list/route": "/api/documents/list", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/documents/search/route": "/api/documents/search", "/api/documents/upload/route": "/api/documents/upload", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/keys/route": "/api/keys", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/orchestration/start/route": "/api/orchestration/start", "/api/logs/route": "/api/logs", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/playground/route": "/api/playground", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/providers/list-models/route": "/api/providers/list-models", "/api/system-status/route": "/api/system-status", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/training/jobs/route": "/api/training/jobs", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/auth/callback/route": "/auth/callback", "/api/user/custom-roles/route": "/api/user/custom-roles", "/favicon.ico/route": "/favicon.ico", "/about/page": "/about", "/analytics/page": "/analytics", "/add-keys/page": "/add-keys", "/auth/signin/page": "/auth/signin", "/dashboard/page": "/dashboard", "/auth/verify-email/page": "/auth/verify-email", "/auth/signup/page": "/auth/signup", "/logs/page": "/logs", "/checkout/page": "/checkout", "/debug-session/page": "/debug-session", "/my-models/page": "/my-models", "/page": "/", "/playground/page": "/playground", "/features/page": "/features", "/my-models/[configId]/page": "/my-models/[configId]", "/pricing/page": "/pricing", "/routing-setup/page": "/routing-setup", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/training/page": "/training"}
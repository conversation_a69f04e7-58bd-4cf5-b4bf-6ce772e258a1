(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7220],{5114:(e,t,s)=>{Promise.resolve().then(s.bind(s,19363))},19363:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var i=s(95155),l=s(55020),a=s(6874),r=s.n(a),o=s(64274),n=s(48666),d=s(8246),c=s(64219),x=s(56075),m=s(75961);let h=[{label:"AI Models Supported",value:"300+"},{label:"API Requests Processed",value:"10M+"},{label:"Developers Trust Us",value:"5,000+"},{label:"Uptime Guarantee",value:"99.9%"}],u=[{icon:o.A,title:"Performance First",description:"We obsess over speed, reliability, and efficiency in everything we build."},{icon:d.A,title:"Security by Design",description:"Enterprise-grade security isn&apos;t an afterthought—it&apos;s built into our foundation."},{icon:n.A,title:"Innovation Driven",description:"We&apos;re constantly pushing the boundaries of what&apos;s possible with AI routing."},{icon:c.A,title:"Solo Built",description:"Built entirely by one developer who faced these exact problems and solved them."}],p={name:"Okoro <PERSON>wunyerem",role:"Founder & Developer"};function b(){return(0,i.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,i.jsx)(x.A,{}),(0,i.jsxs)("main",{className:"pt-20",children:[(0,i.jsx)("section",{className:"py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,i.jsxs)("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["From Frustration to",(0,i.jsx)("span",{className:"text-[#ff6b35] block",children:"Innovation"})]}),(0,i.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto mb-8",children:"The story of how one developer's late-night coding sessions and mounting API bills led to building the world's most intelligent AI routing platform - completely solo."})]})})}),(0,i.jsx)("section",{className:"py-16",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:h.map((e,t)=>(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2",children:e.value}),(0,i.jsx)("div",{className:"text-gray-600 font-medium",children:e.label})]},e.label))})})}),(0,i.jsx)("section",{className:"py-20 bg-gray-50",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-6",children:"The Breaking Point"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Every developer has that moment when they decide to build the tool they wish existed. This is mine."})]}),(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},className:"prose prose-lg mx-auto text-gray-700",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"It was 2 AM."}),' I was deep into developing a game that heavily relied on AI for procedural content generation. My API bills were skyrocketing, I\'d hit rate limits on three different providers, and the "intelligent" routing tool I was paying for had just routed my simple chat request to GPT-4 Turbo for the hundredth time that day.']}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"That's when it hit me."})," I'm Okoro David Chukwunyerem, and I've been building games and apps for years. I know how to solve complex routing problems. Why was I trusting someone else's broken solution when I could build something that actually works?"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"The eureka moment:"})," While experimenting with different providers, I discovered that by intelligently routing between multiple free trial API keys, I could get essentially unlimited usage for testing. No more $500 monthly bills for development work. No more hitting rate limits mid-sprint."]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Six months later,"})," what started as a weekend hack to solve my own problems had evolved into RouKey - a platform that gives any developer unlimited access to 300+ AI models with truly intelligent routing. Built entirely solo, tested in the trenches of real development work."]}),(0,i.jsx)("p",{className:"text-[#ff6b35] font-semibold text-lg border-l-4 border-[#ff6b35] pl-6 italic",children:'"I built RouKey because I was tired of choosing between going broke or building slowly. Now thousands of developers can test fearlessly and build faster than ever before."'})]})]})}),(0,i.jsx)("section",{className:"py-20",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-6",children:"My Values"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"These principles guide how I built RouKey and how I continue to develop it."})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:u.map((e,t)=>(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3,delay:.05*t},className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center",children:(0,i.jsx)(e.icon,{className:"w-6 h-6 text-white"})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600",children:e.description})]})]},e.title))})]})}),(0,i.jsxs)("section",{className:"py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,i.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"\n                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\n                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\n                ",backgroundSize:"60px 60px"}})}),(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative",children:[(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},className:"text-center mb-20",children:[(0,i.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["Meet the",(0,i.jsx)("span",{className:"text-[#ff6b35] block",children:"Solo Founder"})]}),(0,i.jsx)("p",{className:"text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed",children:"One developer's journey from frustration to building the ultimate AI routing platform"})]}),(0,i.jsx)(l.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},className:"bg-white rounded-3xl shadow-2xl overflow-hidden max-w-6xl mx-auto",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-0",children:[(0,i.jsx)("div",{className:"relative bg-gradient-to-br from-[#ff6b35] to-[#f7931e] p-8 flex items-center justify-center min-h-[700px]",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"w-[500px] h-[500px] rounded-3xl overflow-hidden shadow-2xl border-4 border-white/20",children:(0,i.jsx)("img",{src:"/founder.jpg",alt:"Okoro David Chukwunyerem",className:"w-full h-full object-cover"})}),(0,i.jsx)("div",{className:"absolute -top-6 -left-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-12 hover:rotate-0 transition-transform duration-300",children:(0,i.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"Solo Built"})}),(0,i.jsx)("div",{className:"absolute -bottom-6 -right-6 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-12 hover:rotate-0 transition-transform duration-300",children:(0,i.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"300+ Models"})}),(0,i.jsx)("div",{className:"absolute top-1/2 -right-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform rotate-6 hover:rotate-0 transition-transform duration-300",children:(0,i.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"Game Dev"})}),(0,i.jsx)("div",{className:"absolute bottom-1/4 -left-8 bg-white rounded-2xl px-4 py-2 shadow-xl transform -rotate-6 hover:rotate-0 transition-transform duration-300",children:(0,i.jsx)("div",{className:"text-[#ff6b35] font-bold text-sm",children:"∞ Requests"})})]})}),(0,i.jsxs)("div",{className:"p-12 flex flex-col justify-center",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h3",{className:"text-4xl font-bold text-gray-900 mb-3",children:p.name}),(0,i.jsx)("p",{className:"text-2xl text-[#ff6b35] font-semibold mb-6",children:p.role}),(0,i.jsx)("div",{className:"w-20 h-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full mb-8"})]}),(0,i.jsxs)("div",{className:"space-y-6 text-lg text-gray-700 leading-relaxed",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{className:"text-gray-900",children:"Game developer turned AI infrastructure pioneer."}),"After countless nights wrestling with broken routing tools and mounting API bills, I decided to build the solution I wished existed."]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{className:"text-gray-900",children:"The breakthrough:"})," While developing games that heavily used AI, I discovered that intelligent routing between multiple free trial API keys could give me essentially unlimited testing access. No more $500 monthly bills for development work."]}),(0,i.jsx)("div",{className:"bg-gray-50 rounded-2xl p-6 border-l-4 border-[#ff6b35]",children:(0,i.jsx)("p",{className:"text-[#ff6b35] font-semibold italic",children:'"I built RouKey because I was tired of choosing between going broke or building slowly. Six months later, thousands of developers are testing fearlessly and building faster than ever."'})}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{className:"text-gray-900",children:"Every line of code"})," in RouKey was written by me. Every feature was born from a real problem I faced. That's why it actually works."]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-6 mt-10 pt-8 border-t border-gray-200",children:[(0,i.jsxs)("div",{className:"text-center group",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300",children:"1"}),(0,i.jsx)("div",{className:"text-gray-600 text-sm",children:"Solo Developer"})]}),(0,i.jsxs)("div",{className:"text-center group",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300",children:"300+"}),(0,i.jsx)("div",{className:"text-gray-600 text-sm",children:"AI Models"})]}),(0,i.jsxs)("div",{className:"text-center group",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300",children:"∞"}),(0,i.jsx)("div",{className:"text-gray-600 text-sm",children:"API Requests"})]})]})]})]})})]})]}),(0,i.jsxs)("section",{className:"py-32 bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden",children:[(0,i.jsxs)("div",{className:"absolute inset-0",children:[(0,i.jsx)("div",{className:"absolute top-20 left-10 w-96 h-96 bg-[#ff6b35] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"}),(0,i.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 bg-[#f7931e] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse",style:{animationDelay:"2s"}})]}),(0,i.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative",children:(0,i.jsxs)(l.PY1.div,{initial:{opacity:0,y:15},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,i.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold text-white mb-8",children:["Ready to Build",(0,i.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] block",children:"Without Limits?"})]}),(0,i.jsx)("p",{className:"text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed",children:"Join thousands of developers who've discovered the secret to unlimited AI testing. Built by a developer who faced your exact problems."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center mb-16",children:[(0,i.jsx)(r(),{href:"/auth/signup",prefetch:!0,children:(0,i.jsx)(l.PY1.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer",children:"Start Building Now"})}),(0,i.jsx)(r(),{href:"/pricing",prefetch:!0,children:(0,i.jsx)(l.PY1.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-white/10 backdrop-blur-sm text-white px-12 py-5 rounded-2xl font-bold text-xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 cursor-pointer",children:"View Pricing"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl font-bold text-[#ff6b35] mb-2",children:"∞"}),(0,i.jsx)("div",{className:"text-gray-400",children:"API Requests"}),(0,i.jsx)("div",{className:"text-gray-500 text-sm",children:"No limits, ever"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl font-bold text-[#ff6b35] mb-2",children:"300+"}),(0,i.jsx)("div",{className:"text-gray-400",children:"AI Models"}),(0,i.jsx)("div",{className:"text-gray-500 text-sm",children:"All providers"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl font-bold text-[#ff6b35] mb-2",children:"$0"}),(0,i.jsx)("div",{className:"text-gray-400",children:"Overage Fees"}),(0,i.jsx)("div",{className:"text-gray-500 text-sm",children:"Pay only your API costs"})]})]})]})})]})]}),(0,i.jsx)(m.A,{})]})}},44383:(e,t,s)=>{"use strict";s.d(t,{f:()=>l.A,t:()=>i.A});var i=s(69598),l=s(74500)}},e=>{var t=t=>e(e.s=t);e.O(0,[7871,2115,5738,9968,6060,563,2662,8669,8848,4696,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(5114)),_N_E=e.O()}]);
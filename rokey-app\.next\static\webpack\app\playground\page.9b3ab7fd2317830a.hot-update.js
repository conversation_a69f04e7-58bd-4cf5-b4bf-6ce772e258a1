"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CogIcon.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CogIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495\"\n    }));\n}\n_c = CogIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CogIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CogIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx":
/*!***************************************************!*\
  !*** ./src/components/DynamicStatusIndicator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DynamicStatusIndicator),\n/* harmony export */   useStatusProgression: () => (/* binding */ useStatusProgression)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CommandLineIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default,useStatusProgression auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst STATUS_CONFIGS = {\n    initializing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        text: 'Initializing',\n        description: 'Starting up systems',\n        bgColor: 'bg-gradient-to-r from-slate-50 to-gray-50',\n        iconColor: 'text-slate-600',\n        borderColor: 'border-slate-200/60',\n        glowColor: 'shadow-slate-200/50',\n        gradientFrom: 'from-slate-400',\n        gradientTo: 'to-gray-400',\n        duration: 200\n    },\n    analyzing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        text: 'Analyzing',\n        description: 'Understanding your request',\n        bgColor: 'bg-gradient-to-r from-cyan-50 to-blue-50',\n        iconColor: 'text-cyan-600',\n        borderColor: 'border-cyan-200/60',\n        glowColor: 'shadow-cyan-200/50',\n        gradientFrom: 'from-cyan-400',\n        gradientTo: 'to-blue-400',\n        duration: 300\n    },\n    routing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        text: 'Smart routing',\n        description: 'Finding optimal path',\n        bgColor: 'bg-gradient-to-r from-indigo-50 to-purple-50',\n        iconColor: 'text-indigo-600',\n        borderColor: 'border-indigo-200/60',\n        glowColor: 'shadow-indigo-200/50',\n        gradientFrom: 'from-indigo-400',\n        gradientTo: 'to-purple-400',\n        duration: 400\n    },\n    complexity_analysis: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        text: 'Analyzing complexity',\n        description: 'Evaluating request depth',\n        bgColor: 'bg-gradient-to-r from-amber-50 to-yellow-50',\n        iconColor: 'text-amber-600',\n        borderColor: 'border-amber-200/60',\n        glowColor: 'shadow-amber-200/50',\n        gradientFrom: 'from-amber-400',\n        gradientTo: 'to-yellow-400',\n        duration: 500\n    },\n    role_classification: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        text: 'Assembling specialists',\n        description: 'Building expert team',\n        bgColor: 'bg-gradient-to-r from-violet-50 to-purple-50',\n        iconColor: 'text-violet-600',\n        borderColor: 'border-violet-200/60',\n        glowColor: 'shadow-violet-200/50',\n        gradientFrom: 'from-violet-400',\n        gradientTo: 'to-purple-400',\n        duration: 600\n    },\n    preparing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        text: 'Preparing',\n        description: 'Setting up processing',\n        bgColor: 'bg-gradient-to-r from-orange-50 to-amber-50',\n        iconColor: 'text-orange-600',\n        borderColor: 'border-orange-200/60',\n        glowColor: 'shadow-orange-200/50',\n        gradientFrom: 'from-orange-400',\n        gradientTo: 'to-amber-400',\n        duration: 300\n    },\n    connecting: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        text: 'Connecting',\n        description: 'Establishing AI link',\n        bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',\n        iconColor: 'text-rose-600',\n        borderColor: 'border-rose-200/60',\n        glowColor: 'shadow-rose-200/50',\n        gradientFrom: 'from-rose-400',\n        gradientTo: 'to-pink-400',\n        duration: 400\n    },\n    generating: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        text: 'Thinking deeply',\n        description: 'AI processing in progress',\n        bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n        iconColor: 'text-emerald-600',\n        borderColor: 'border-emerald-200/60',\n        glowColor: 'shadow-emerald-200/50',\n        gradientFrom: 'from-emerald-400',\n        gradientTo: 'to-teal-400',\n        duration: 800\n    },\n    typing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        text: 'Streaming response',\n        description: 'Delivering your answer',\n        bgColor: 'bg-gradient-to-r from-green-50 to-emerald-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-emerald-400'\n    },\n    finalizing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        text: 'Finalizing',\n        description: 'Adding finishing touches',\n        bgColor: 'bg-gradient-to-r from-teal-50 to-cyan-50',\n        iconColor: 'text-teal-600',\n        borderColor: 'border-teal-200/60',\n        glowColor: 'shadow-teal-200/50',\n        gradientFrom: 'from-teal-400',\n        gradientTo: 'to-cyan-400',\n        duration: 200\n    },\n    complete: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        text: 'Complete',\n        description: 'Response delivered',\n        bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-lime-400',\n        duration: 100\n    }\n};\nfunction DynamicStatusIndicator(param) {\n    let { currentStage, isStreaming = false, className = '', onStageChange, orchestrationStatus } = param;\n    _s();\n    const [displayStage, setDisplayStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(currentStage);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [orchestrationColorIndex, setOrchestrationColorIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastOrchestrationStatus, setLastOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    // Color palette for orchestration progress cycling\n    const orchestrationColors = [\n        {\n            bg: 'bg-gradient-to-r from-blue-50 to-indigo-50',\n            icon: 'text-blue-600',\n            border: 'border-blue-200/60',\n            glow: 'shadow-blue-200/50',\n            gradient: 'from-blue-400 to-indigo-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-purple-50 to-violet-50',\n            icon: 'text-purple-600',\n            border: 'border-purple-200/60',\n            glow: 'shadow-purple-200/50',\n            gradient: 'from-purple-400 to-violet-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-indigo-50 to-blue-50',\n            icon: 'text-indigo-600',\n            border: 'border-indigo-200/60',\n            glow: 'shadow-indigo-200/50',\n            gradient: 'from-indigo-400 to-blue-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-cyan-50 to-teal-50',\n            icon: 'text-cyan-600',\n            border: 'border-cyan-200/60',\n            glow: 'shadow-cyan-200/50',\n            gradient: 'from-cyan-400 to-teal-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-teal-50 to-emerald-50',\n            icon: 'text-teal-600',\n            border: 'border-teal-200/60',\n            glow: 'shadow-teal-200/50',\n            gradient: 'from-teal-400 to-emerald-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-green-50 to-lime-50',\n            icon: 'text-green-600',\n            border: 'border-green-200/60',\n            glow: 'shadow-green-200/50',\n            gradient: 'from-green-400 to-lime-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-yellow-50 to-amber-50',\n            icon: 'text-yellow-600',\n            border: 'border-yellow-200/60',\n            glow: 'shadow-yellow-200/50',\n            gradient: 'from-yellow-400 to-amber-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-orange-50 to-red-50',\n            icon: 'text-orange-600',\n            border: 'border-orange-200/60',\n            glow: 'shadow-orange-200/50',\n            gradient: 'from-orange-400 to-red-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-rose-50 to-pink-50',\n            icon: 'text-rose-600',\n            border: 'border-rose-200/60',\n            glow: 'shadow-rose-200/50',\n            gradient: 'from-rose-400 to-pink-400'\n        },\n        {\n            bg: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n            icon: 'text-emerald-600',\n            border: 'border-emerald-200/60',\n            glow: 'shadow-emerald-200/50',\n            gradient: 'from-emerald-400 to-teal-400'\n        }\n    ];\n    // Use orchestration colors if in orchestration mode, otherwise use default config\n    const isOrchestrationMode = !!orchestrationStatus;\n    const currentOrchestrationColor = orchestrationColors[orchestrationColorIndex % orchestrationColors.length];\n    // Get appropriate icon based on orchestration status\n    const getOrchestrationIcon = (status)=>{\n        if (status.includes('🔍') || status.includes('detected')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (status.includes('✅') || status.includes('complete')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        if (status.includes('🎯') || status.includes('Selected')) return TargetIcon;\n        if (status.includes('🏗️') || status.includes('workflow')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n        if (status.includes('🤖') || status.includes('agent')) return UserGroupIcon;\n        if (status.includes('👑') || status.includes('supervisor')) return CrownIcon;\n        if (status.includes('📋') || status.includes('Planning')) return ClipboardDocumentListIcon;\n        if (status.includes('🚀') || status.includes('starting')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        if (status.includes('🔄') || status.includes('synthesizing')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return STATUS_CONFIGS[displayStage].icon; // fallback\n    };\n    const config = isOrchestrationMode ? {\n        ...STATUS_CONFIGS[displayStage],\n        ...currentOrchestrationColor,\n        icon: getOrchestrationIcon(orchestrationStatus)\n    } : STATUS_CONFIGS[displayStage];\n    const Icon = config.icon;\n    // Handle stage transitions with speed-up animation\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (currentStage !== displayStage) {\n                console.log(\"\\uD83C\\uDFAF DynamicStatusIndicator: \".concat(displayStage, \" → \").concat(currentStage));\n                setIsTransitioning(true);\n                // Speed up animation during transition\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setDisplayStage(currentStage);\n                        setIsTransitioning(false);\n                        onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange(currentStage);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 200); // Brief transition period\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        currentStage,\n        displayStage,\n        onStageChange\n    ]);\n    // Handle orchestration status changes with color cycling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (orchestrationStatus && orchestrationStatus !== lastOrchestrationStatus) {\n                console.log(\"\\uD83C\\uDFA8 Orchestration status changed: \".concat(orchestrationStatus));\n                setLastOrchestrationStatus(orchestrationStatus);\n                setOrchestrationColorIndex({\n                    \"DynamicStatusIndicator.useEffect\": (prev)=>prev + 1\n                }[\"DynamicStatusIndicator.useEffect\"]);\n                setIsTransitioning(true);\n                // Brief transition animation\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setIsTransitioning(false);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 300);\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        orchestrationStatus,\n        lastOrchestrationStatus\n    ]);\n    // Remove auto-progression - let the hook handle stage management\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex justify-start \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.6s linear infinite' : 'spin 1.2s linear infinite',\n                            borderImage: \"conic-gradient(from 0deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 25%, transparent 50%, \").concat(config.iconColor.replace('text-', ''), \" 75%, transparent 100%) 1\"),\n                            filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.8s linear infinite reverse' : 'spin 1.6s linear infinite reverse',\n                            borderImage: \"conic-gradient(from 180deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 30%, transparent 60%, \").concat(config.iconColor.replace('text-', ''), \" 90%, transparent 100%) 1\"),\n                            opacity: 0.8\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            borderColor: config.iconColor.replace('text-', ''),\n                            opacity: 0.6,\n                            animation: 'pulse 2s ease-in-out infinite'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            boxShadow: \"0 0 12px \".concat(config.iconColor.replace('text-', ''), \"40, 0 0 24px \").concat(config.iconColor.replace('text-', ''), \"20\")\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 \".concat(config.bgColor, \" border-2 \").concat(config.borderColor, \" shadow-lg backdrop-blur-sm\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"w-3.5 h-3.5 transition-all duration-500 \".concat(config.iconColor, \" \").concat(isTransitioning ? 'scale-125 rotate-12' : 'scale-100', \" drop-shadow-lg\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 \".concat(config.bgColor, \" \").concat(config.borderColor, \" border \").concat(config.glowColor, \" shadow-sm backdrop-blur-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex items-center space-x-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"transition-all duration-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"text-xs font-semibold transition-colors duration-500 \".concat(config.iconColor, \" tracking-wide\"),\n                                    children: orchestrationStatus || config.text\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                isStreaming && displayStage === 'typing' && !orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Live\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this),\n                                orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Orchestrating\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, this),\n                    (displayStage === 'generating' || displayStage === 'typing') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: displayStage === 'typing' ? '100%' : '60%',\n                                    animation: displayStage === 'typing' ? 'progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite' : 'progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite'\n                                },\n                                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-full rounded-full transition-all duration-1000 bg-gradient-to-r \".concat(config.gradientFrom, \" \").concat(config.gradientTo, \" relative overflow-hidden\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        animation: 'progressShine 2s linear infinite',\n                                        transform: 'skewX(-20deg)'\n                                    },\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"f56d70faa8a01b64\",\n                children: \"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n_s(DynamicStatusIndicator, \"1p3xKMLQ9O9Eig1VCjGbm6b6hME=\");\n_c = DynamicStatusIndicator;\n// Hook for managing status progression\nfunction useStatusProgression() {\n    _s1();\n    const [currentStage, setCurrentStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('initializing');\n    const [stageHistory, setStageHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        'initializing'\n    ]);\n    const updateStage = (stage)=>{\n        setCurrentStage(stage);\n        setStageHistory((prev)=>[\n                ...prev,\n                stage\n            ]);\n    };\n    const reset = ()=>{\n        setCurrentStage('initializing');\n        setStageHistory([\n            'initializing'\n        ]);\n    };\n    return {\n        currentStage,\n        stageHistory,\n        updateStage,\n        reset\n    };\n}\n_s1(useStatusProgression, \"7mqPvLO7RfuzcLNJcZduGbqc5yY=\");\nvar _c;\n$RefreshReg$(_c, \"DynamicStatusIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\n"));

/***/ })

});
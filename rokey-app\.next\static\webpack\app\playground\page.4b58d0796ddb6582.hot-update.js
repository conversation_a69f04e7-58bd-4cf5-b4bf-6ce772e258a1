"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx":
/*!***************************************************!*\
  !*** ./src/components/DynamicStatusIndicator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DynamicStatusIndicator),\n/* harmony export */   useStatusProgression: () => (/* binding */ useStatusProgression)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CommandLineIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_TargetIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=TargetIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/__barrel_optimize__?names=TargetIcon!=!./node_modules/@heroicons/react/24/solid/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_TargetIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_barrel_optimize_names_TargetIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default,useStatusProgression auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Additional icons for orchestration\n\nconst STATUS_CONFIGS = {\n    initializing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        text: 'Initializing',\n        description: 'Starting up systems',\n        bgColor: 'bg-gradient-to-r from-slate-50 to-gray-50',\n        iconColor: 'text-slate-600',\n        borderColor: 'border-slate-200/60',\n        glowColor: 'shadow-slate-200/50',\n        gradientFrom: 'from-slate-400',\n        gradientTo: 'to-gray-400',\n        duration: 200\n    },\n    analyzing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        text: 'Analyzing',\n        description: 'Understanding your request',\n        bgColor: 'bg-gradient-to-r from-cyan-50 to-blue-50',\n        iconColor: 'text-cyan-600',\n        borderColor: 'border-cyan-200/60',\n        glowColor: 'shadow-cyan-200/50',\n        gradientFrom: 'from-cyan-400',\n        gradientTo: 'to-blue-400',\n        duration: 300\n    },\n    routing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        text: 'Smart routing',\n        description: 'Finding optimal path',\n        bgColor: 'bg-gradient-to-r from-indigo-50 to-purple-50',\n        iconColor: 'text-indigo-600',\n        borderColor: 'border-indigo-200/60',\n        glowColor: 'shadow-indigo-200/50',\n        gradientFrom: 'from-indigo-400',\n        gradientTo: 'to-purple-400',\n        duration: 400\n    },\n    complexity_analysis: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        text: 'Analyzing complexity',\n        description: 'Evaluating request depth',\n        bgColor: 'bg-gradient-to-r from-amber-50 to-yellow-50',\n        iconColor: 'text-amber-600',\n        borderColor: 'border-amber-200/60',\n        glowColor: 'shadow-amber-200/50',\n        gradientFrom: 'from-amber-400',\n        gradientTo: 'to-yellow-400',\n        duration: 500\n    },\n    role_classification: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        text: 'Assembling specialists',\n        description: 'Building expert team',\n        bgColor: 'bg-gradient-to-r from-violet-50 to-purple-50',\n        iconColor: 'text-violet-600',\n        borderColor: 'border-violet-200/60',\n        glowColor: 'shadow-violet-200/50',\n        gradientFrom: 'from-violet-400',\n        gradientTo: 'to-purple-400',\n        duration: 600\n    },\n    preparing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        text: 'Preparing',\n        description: 'Setting up processing',\n        bgColor: 'bg-gradient-to-r from-orange-50 to-amber-50',\n        iconColor: 'text-orange-600',\n        borderColor: 'border-orange-200/60',\n        glowColor: 'shadow-orange-200/50',\n        gradientFrom: 'from-orange-400',\n        gradientTo: 'to-amber-400',\n        duration: 300\n    },\n    connecting: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        text: 'Connecting',\n        description: 'Establishing AI link',\n        bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',\n        iconColor: 'text-rose-600',\n        borderColor: 'border-rose-200/60',\n        glowColor: 'shadow-rose-200/50',\n        gradientFrom: 'from-rose-400',\n        gradientTo: 'to-pink-400',\n        duration: 400\n    },\n    generating: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        text: 'Thinking deeply',\n        description: 'AI processing in progress',\n        bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n        iconColor: 'text-emerald-600',\n        borderColor: 'border-emerald-200/60',\n        glowColor: 'shadow-emerald-200/50',\n        gradientFrom: 'from-emerald-400',\n        gradientTo: 'to-teal-400',\n        duration: 800\n    },\n    typing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        text: 'Streaming response',\n        description: 'Delivering your answer',\n        bgColor: 'bg-gradient-to-r from-green-50 to-emerald-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-emerald-400'\n    },\n    finalizing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        text: 'Finalizing',\n        description: 'Adding finishing touches',\n        bgColor: 'bg-gradient-to-r from-teal-50 to-cyan-50',\n        iconColor: 'text-teal-600',\n        borderColor: 'border-teal-200/60',\n        glowColor: 'shadow-teal-200/50',\n        gradientFrom: 'from-teal-400',\n        gradientTo: 'to-cyan-400',\n        duration: 200\n    },\n    complete: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        text: 'Complete',\n        description: 'Response delivered',\n        bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-lime-400',\n        duration: 100\n    }\n};\nfunction DynamicStatusIndicator(param) {\n    let { currentStage, isStreaming = false, className = '', onStageChange, orchestrationStatus } = param;\n    _s();\n    const [displayStage, setDisplayStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(currentStage);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [orchestrationColorIndex, setOrchestrationColorIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastOrchestrationStatus, setLastOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [spinnerSpeed, setSpinnerSpeed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1); // 1 = normal speed, higher = faster\n    // Color palette for orchestration progress cycling\n    const orchestrationColors = [\n        {\n            bgColor: 'bg-gradient-to-r from-blue-50 to-indigo-50',\n            iconColor: 'text-blue-600',\n            borderColor: 'border-blue-200/60',\n            glowColor: 'shadow-blue-200/50',\n            gradientFrom: 'from-blue-400',\n            gradientTo: 'to-indigo-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-purple-50 to-violet-50',\n            iconColor: 'text-purple-600',\n            borderColor: 'border-purple-200/60',\n            glowColor: 'shadow-purple-200/50',\n            gradientFrom: 'from-purple-400',\n            gradientTo: 'to-violet-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-indigo-50 to-blue-50',\n            iconColor: 'text-indigo-600',\n            borderColor: 'border-indigo-200/60',\n            glowColor: 'shadow-indigo-200/50',\n            gradientFrom: 'from-indigo-400',\n            gradientTo: 'to-blue-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-cyan-50 to-teal-50',\n            iconColor: 'text-cyan-600',\n            borderColor: 'border-cyan-200/60',\n            glowColor: 'shadow-cyan-200/50',\n            gradientFrom: 'from-cyan-400',\n            gradientTo: 'to-teal-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-teal-50 to-emerald-50',\n            iconColor: 'text-teal-600',\n            borderColor: 'border-teal-200/60',\n            glowColor: 'shadow-teal-200/50',\n            gradientFrom: 'from-teal-400',\n            gradientTo: 'to-emerald-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',\n            iconColor: 'text-green-600',\n            borderColor: 'border-green-200/60',\n            glowColor: 'shadow-green-200/50',\n            gradientFrom: 'from-green-400',\n            gradientTo: 'to-lime-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-yellow-50 to-amber-50',\n            iconColor: 'text-yellow-600',\n            borderColor: 'border-yellow-200/60',\n            glowColor: 'shadow-yellow-200/50',\n            gradientFrom: 'from-yellow-400',\n            gradientTo: 'to-amber-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-orange-50 to-red-50',\n            iconColor: 'text-orange-600',\n            borderColor: 'border-orange-200/60',\n            glowColor: 'shadow-orange-200/50',\n            gradientFrom: 'from-orange-400',\n            gradientTo: 'to-red-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',\n            iconColor: 'text-rose-600',\n            borderColor: 'border-rose-200/60',\n            glowColor: 'shadow-rose-200/50',\n            gradientFrom: 'from-rose-400',\n            gradientTo: 'to-pink-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n            iconColor: 'text-emerald-600',\n            borderColor: 'border-emerald-200/60',\n            glowColor: 'shadow-emerald-200/50',\n            gradientFrom: 'from-emerald-400',\n            gradientTo: 'to-teal-400'\n        }\n    ];\n    // Use orchestration colors if in orchestration mode, otherwise use default config\n    const isOrchestrationMode = !!orchestrationStatus;\n    const currentOrchestrationColor = orchestrationColors[orchestrationColorIndex % orchestrationColors.length];\n    // Get appropriate icon based on orchestration status\n    const getOrchestrationIcon = (status)=>{\n        if (status.includes('🔍') || status.includes('detected')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (status.includes('✅') || status.includes('complete')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n        if (status.includes('🎯') || status.includes('Selected')) return _barrel_optimize_names_TargetIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__.TargetIcon;\n        if (status.includes('🏗️') || status.includes('workflow')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n        if (status.includes('🤖') || status.includes('agent')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n        if (status.includes('👑') || status.includes('supervisor')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; // Use StarIcon for supervisor\n        if (status.includes('📋') || status.includes('Planning')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n        if (status.includes('🚀') || status.includes('starting')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        if (status.includes('🔄') || status.includes('synthesizing')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        return STATUS_CONFIGS[displayStage].icon; // fallback\n    };\n    const config = isOrchestrationMode ? {\n        ...STATUS_CONFIGS[displayStage],\n        ...currentOrchestrationColor,\n        icon: getOrchestrationIcon(orchestrationStatus)\n    } : STATUS_CONFIGS[displayStage];\n    const Icon = config.icon;\n    // Handle stage transitions with speed-up animation\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (currentStage !== displayStage) {\n                console.log(\"\\uD83C\\uDFAF DynamicStatusIndicator: \".concat(displayStage, \" → \").concat(currentStage));\n                setIsTransitioning(true);\n                // Speed up animation during transition\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setDisplayStage(currentStage);\n                        setIsTransitioning(false);\n                        onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange(currentStage);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 200); // Brief transition period\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        currentStage,\n        displayStage,\n        onStageChange\n    ]);\n    // Handle orchestration status changes with color cycling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (orchestrationStatus && orchestrationStatus !== lastOrchestrationStatus) {\n                console.log(\"\\uD83C\\uDFA8 Orchestration status changed: \".concat(orchestrationStatus));\n                setLastOrchestrationStatus(orchestrationStatus);\n                setOrchestrationColorIndex({\n                    \"DynamicStatusIndicator.useEffect\": (prev)=>prev + 1\n                }[\"DynamicStatusIndicator.useEffect\"]);\n                setIsTransitioning(true);\n                // Brief transition animation\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setIsTransitioning(false);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 300);\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        orchestrationStatus,\n        lastOrchestrationStatus\n    ]);\n    // Remove auto-progression - let the hook handle stage management\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex justify-start \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.6s linear infinite' : 'spin 1.2s linear infinite',\n                            borderImage: \"conic-gradient(from 0deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 25%, transparent 50%, \").concat(config.iconColor.replace('text-', ''), \" 75%, transparent 100%) 1\"),\n                            filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1.5 w-9 h-9 rounded-full border-[3px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: isTransitioning ? 'spin 0.8s linear infinite reverse' : 'spin 1.6s linear infinite reverse',\n                            borderImage: \"conic-gradient(from 180deg, transparent 0%, \".concat(config.iconColor.replace('text-', ''), \" 30%, transparent 60%, \").concat(config.iconColor.replace('text-', ''), \" 90%, transparent 100%) 1\"),\n                            opacity: 0.8\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1 w-8 h-8 rounded-full border-[2px] border-transparent animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            borderColor: config.iconColor.replace('text-', ''),\n                            opacity: 0.6,\n                            animation: 'pulse 2s ease-in-out infinite'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-0.5 w-7 h-7 rounded-full border border-transparent animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            boxShadow: \"0 0 12px \".concat(config.iconColor.replace('text-', ''), \"40, 0 0 24px \").concat(config.iconColor.replace('text-', ''), \"20\")\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 \".concat(config.bgColor, \" border-2 \").concat(config.borderColor, \" shadow-lg backdrop-blur-sm\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"w-3.5 h-3.5 transition-all duration-500 \".concat(config.iconColor, \" \").concat(isTransitioning ? 'scale-125 rotate-12' : 'scale-100', \" drop-shadow-lg\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 \".concat(config.bgColor, \" \").concat(config.borderColor, \" border \").concat(config.glowColor, \" shadow-sm backdrop-blur-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex items-center space-x-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"transition-all duration-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"text-xs font-semibold transition-colors duration-500 \".concat(config.iconColor, \" tracking-wide\"),\n                                    children: orchestrationStatus || config.text\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                isStreaming && displayStage === 'typing' && !orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Live\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Orchestrating\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    (displayStage === 'generating' || displayStage === 'typing') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: displayStage === 'typing' ? '100%' : '60%',\n                                    animation: displayStage === 'typing' ? 'progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite' : 'progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite'\n                                },\n                                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-full rounded-full transition-all duration-1000 bg-gradient-to-r \".concat(config.gradientFrom, \" \").concat(config.gradientTo, \" relative overflow-hidden\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        animation: 'progressShine 2s linear infinite',\n                                        transform: 'skewX(-20deg)'\n                                    },\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"f56d70faa8a01b64\",\n                children: \"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(DynamicStatusIndicator, \"/2+Je50HRbnIv9Z3EeBsB5SYU78=\");\n_c = DynamicStatusIndicator;\n// Hook for managing status progression\nfunction useStatusProgression() {\n    _s1();\n    const [currentStage, setCurrentStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('initializing');\n    const [stageHistory, setStageHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        'initializing'\n    ]);\n    const updateStage = (stage)=>{\n        setCurrentStage(stage);\n        setStageHistory((prev)=>[\n                ...prev,\n                stage\n            ]);\n    };\n    const reset = ()=>{\n        setCurrentStage('initializing');\n        setStageHistory([\n            'initializing'\n        ]);\n    };\n    return {\n        currentStage,\n        stageHistory,\n        updateStage,\n        reset\n    };\n}\n_s1(useStatusProgression, \"7mqPvLO7RfuzcLNJcZduGbqc5yY=\");\nvar _c;\n$RefreshReg$(_c, \"DynamicStatusIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\n"));

/***/ })

});